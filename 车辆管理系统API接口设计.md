# 车辆管理系统API接口设计

## 1. 接口概述

### 1.1 基础信息
- **基础URL**: `/api/vehicle`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 通用响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 1.3 状态码说明
- 200: 操作成功
- 400: 请求参数错误
- 401: 未授权
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

## 2. 车辆信息管理接口

### 2.1 获取车辆列表
**接口地址**: `GET /api/vehicle/info/list`

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "vehicleNumber": "京A12345",
  "vehicleModel": "解放J6P",
  "status": 1,
  "departmentId": 1,
  "createTimeStart": "2024-01-01",
  "createTimeEnd": "2024-12-31"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1,
        "vehicleNumber": "京A12345",
        "vehicleModel": "解放J6P",
        "vehicleBrand": "一汽解放",
        "tonnage": 25.00,
        "length": 12.00,
        "width": 2.50,
        "height": 4.00,
        "engineNumber": "CA6DM2-42E5",
        "frameNumber": "LFV2A21K8K1234567",
        "purchaseDate": "2023-01-15",
        "status": 1,
        "statusText": "可用",
        "departmentId": 1,
        "departmentName": "第一工程队",
        "remark": "备注信息",
        "createUser": "admin",
        "createTime": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

### 2.2 获取车辆详情
**接口地址**: `GET /api/vehicle/info/{id}`

### 2.3 新增车辆
**接口地址**: `POST /api/vehicle/info`

**请求参数**:
```json
{
  "vehicleNumber": "京A12345",
  "vehicleModel": "解放J6P",
  "vehicleBrand": "一汽解放",
  "tonnage": 25.00,
  "length": 12.00,
  "width": 2.50,
  "height": 4.00,
  "engineNumber": "CA6DM2-42E5",
  "frameNumber": "LFV2A21K8K1234567",
  "purchaseDate": "2023-01-15",
  "status": 1,
  "departmentId": 1,
  "remark": "备注信息"
}
```

### 2.4 更新车辆
**接口地址**: `PUT /api/vehicle/info/{id}`

### 2.5 删除车辆
**接口地址**: `DELETE /api/vehicle/info/{id}`

### 2.6 批量导入车辆
**接口地址**: `POST /api/vehicle/info/import`

**请求参数**: FormData (Excel文件)

### 2.7 导出车辆信息
**接口地址**: `GET /api/vehicle/info/export`

## 3. 用车申请管理接口

### 3.1 获取申请列表
**接口地址**: `GET /api/vehicle/application/list`

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "applicationNo": "20240101001",
  "applicantId": 1,
  "departmentId": 1,
  "status": 0,
  "priority": 1,
  "useDateStart": "2024-01-01",
  "useDateEnd": "2024-12-31"
}
```

### 3.2 提交用车申请
**接口地址**: `POST /api/vehicle/application`

**请求参数**:
```json
{
  "useDate": "2024-01-15",
  "estimatedDuration": 8.0,
  "usePurpose": "运输建材",
  "priority": 1
}
```

### 3.3 队长审核
**接口地址**: `PUT /api/vehicle/application/{id}/team-review`

**请求参数**:
```json
{
  "reviewResult": 1,
  "opinion": "同意申请"
}
```

### 3.4 工程部审核
**接口地址**: `PUT /api/vehicle/application/{id}/engineering-review`

**请求参数**:
```json
{
  "reviewResult": 1,
  "opinion": "同意申请",
  "assignedVehicleId": 1
}
```

### 3.5 开始使用车辆
**接口地址**: `PUT /api/vehicle/application/{id}/start-use`

### 3.6 结束使用车辆
**接口地址**: `PUT /api/vehicle/application/{id}/end-use`

**请求参数**:
```json
{
  "actualEndTime": "2024-01-15T18:00:00Z",
  "workShiftHours": 8.0,
  "mileageStart": 10000.0,
  "mileageEnd": 10200.0,
  "fuelConsumption": 50.0,
  "remark": "使用正常"
}
```

## 4. 车辆看板接口

### 4.1 获取车辆状态看板
**接口地址**: `GET /api/vehicle/dashboard/status`

**请求参数**:
```json
{
  "date": "2024-01-15",
  "departmentId": 1
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalVehicles": 50,
    "availableVehicles": 30,
    "inUseVehicles": 15,
    "maintenanceVehicles": 5,
    "vehicleList": [
      {
        "id": 1,
        "vehicleNumber": "京A12345",
        "vehicleModel": "解放J6P",
        "currentStatus": "使用中",
        "currentUser": "张三",
        "usePurpose": "运输建材",
        "startTime": "08:00",
        "endTime": "18:00"
      }
    ]
  }
}
```

### 4.2 获取车辆计划
**接口地址**: `GET /api/vehicle/schedule/list`

### 4.3 更新车辆计划
**接口地址**: `PUT /api/vehicle/schedule/{id}`

## 5. 台班统计接口

### 5.1 获取台班统计
**接口地址**: `GET /api/vehicle/workshift/statistics`

**请求参数**:
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "vehicleId": 1,
  "departmentId": 1,
  "groupBy": "vehicle" // vehicle, department, date
}
```

### 5.2 导出台班报表
**接口地址**: `GET /api/vehicle/workshift/export`

## 6. 系统配置接口

### 6.1 获取配置列表
**接口地址**: `GET /api/vehicle/config/list`

### 6.2 更新配置
**接口地址**: `PUT /api/vehicle/config/{key}`

## 7. 数据字典接口

### 7.1 获取字典数据
**接口地址**: `GET /api/vehicle/dict/{dictType}`

**响应数据**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "dictCode": "1",
      "dictLabel": "可用",
      "dictValue": "1"
    },
    {
      "dictCode": "2",
      "dictLabel": "维修中",
      "dictValue": "2"
    }
  ]
}
```

## 8. 权限验证接口

### 8.1 检查用户权限
**接口地址**: `GET /api/vehicle/permission/check`

**请求参数**:
```json
{
  "permission": "vehicle:application:submit"
}
```

## 9. 消息通知接口

### 9.1 获取消息列表
**接口地址**: `GET /api/vehicle/notification/list`

### 9.2 标记消息已读
**接口地址**: `PUT /api/vehicle/notification/{id}/read`

## 10. 统计分析接口

### 10.1 获取使用统计
**接口地址**: `GET /api/vehicle/statistics/usage`

**响应数据**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalApplications": 100,
    "approvedApplications": 85,
    "rejectedApplications": 15,
    "totalWorkShiftHours": 1000.0,
    "averageUsageHours": 8.5,
    "vehicleUtilizationRate": 75.5,
    "departmentStats": [
      {
        "departmentName": "第一工程队",
        "applicationCount": 30,
        "workShiftHours": 300.0
      }
    ]
  }
}
```

### 10.2 获取趋势分析
**接口地址**: `GET /api/vehicle/statistics/trend`

## 11. 错误码定义

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 40001 | 车牌号已存在 | 添加车辆时车牌号重复 |
| 40002 | 车辆不存在 | 操作的车辆不存在 |
| 40003 | 车辆状态不允许此操作 | 车辆当前状态不允许执行操作 |
| 40004 | 申请单不存在 | 操作的申请单不存在 |
| 40005 | 申请状态不允许此操作 | 申请单当前状态不允许执行操作 |
| 40006 | 用车日期不能早于今天 | 申请用车日期验证失败 |
| 40007 | 车辆已被分配 | 车辆在指定时间已被分配 |
| 40008 | 权限不足 | 用户权限不足 |
| 40009 | 文件格式不正确 | 上传文件格式错误 |
| 40010 | 导入数据格式错误 | Excel导入数据格式错误 |

## 12. 接口调用示例

### 12.1 JavaScript调用示例
```javascript
// 获取车辆列表
const getVehicleList = async (params) => {
  try {
    const response = await fetch('/api/vehicle/info/list', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });
    const result = await response.json();
    return result;
  } catch (error) {
    console.error('获取车辆列表失败:', error);
  }
};

// 提交用车申请
const submitApplication = async (data) => {
  try {
    const response = await fetch('/api/vehicle/application', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    const result = await response.json();
    return result;
  } catch (error) {
    console.error('提交申请失败:', error);
  }
};
```

### 12.2 cURL调用示例
```bash
# 获取车辆列表
curl -X GET "http://localhost:8080/api/vehicle/info/list" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json"

# 提交用车申请
curl -X POST "http://localhost:8080/api/vehicle/application" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "useDate": "2024-01-15",
    "estimatedDuration": 8.0,
    "usePurpose": "运输建材",
    "priority": 1
  }'
```
