-- 车辆管理系统数据库建表SQL
-- 数据库版本：MySQL 8.0+
-- 字符集：utf8mb4
-- 排序规则：utf8mb4_unicode_ci

-- 创建数据库
CREATE DATABASE IF NOT EXISTS vehicle_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE vehicle_management;

-- 1. 车辆信息表
DROP TABLE IF EXISTS `vehicle_info`;
CREATE TABLE `vehicle_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `vehicle_number` varchar(20) NOT NULL COMMENT '车牌号',
  `vehicle_model` varchar(100) DEFAULT NULL COMMENT '车辆型号',
  `vehicle_brand` varchar(50) DEFAULT NULL COMMENT '车辆品牌',
  `tonnage` decimal(10,2) DEFAULT NULL COMMENT '吨位',
  `length` decimal(10,2) DEFAULT NULL COMMENT '长度(米)',
  `width` decimal(10,2) DEFAULT NULL COMMENT '宽度(米)',
  `height` decimal(10,2) DEFAULT NULL COMMENT '高度(米)',
  `engine_number` varchar(50) DEFAULT NULL COMMENT '发动机号',
  `frame_number` varchar(50) DEFAULT NULL COMMENT '车架号',
  `purchase_date` date DEFAULT NULL COMMENT '购买日期',
  `status` tinyint DEFAULT '1' COMMENT '使用状态：1-可用，2-维修中，3-报废',
  `department_id` bigint DEFAULT NULL COMMENT '所属队部ID',
  `department_name` varchar(100) DEFAULT NULL COMMENT '所属队部名称',
  `remark` text COMMENT '备注信息',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_vehicle_number` (`vehicle_number`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆信息表';

-- 2. 用车申请表
DROP TABLE IF EXISTS `vehicle_application`;
CREATE TABLE `vehicle_application` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `application_no` varchar(32) NOT NULL COMMENT '申请单号',
  `applicant_id` bigint NOT NULL COMMENT '申请人ID',
  `applicant_name` varchar(50) NOT NULL COMMENT '申请人姓名',
  `department_id` bigint NOT NULL COMMENT '申请部门ID',
  `department_name` varchar(100) NOT NULL COMMENT '申请部门名称',
  `use_date` date NOT NULL COMMENT '用车日期',
  `estimated_duration` decimal(10,2) DEFAULT NULL COMMENT '预计用车时长(小时)',
  `use_purpose` text NOT NULL COMMENT '用车目的',
  `priority` tinyint DEFAULT '1' COMMENT '优先级：1-一般，2-紧急',
  `status` tinyint DEFAULT '0' COMMENT '申请状态：0-待队长审核，1-待工程部审核，2-已分配车辆，3-使用中，4-已完成，5-已拒绝',
  `assigned_vehicle_id` bigint DEFAULT NULL COMMENT '分配车辆ID',
  `assigned_vehicle_number` varchar(20) DEFAULT NULL COMMENT '分配车牌号',
  `team_leader_review` tinyint DEFAULT NULL COMMENT '队长审核结果：1-通过，2-拒绝',
  `team_leader_opinion` text COMMENT '队长审核意见',
  `team_leader_review_time` datetime DEFAULT NULL COMMENT '队长审核时间',
  `team_leader_id` bigint DEFAULT NULL COMMENT '队长ID',
  `team_leader_name` varchar(50) DEFAULT NULL COMMENT '队长姓名',
  `engineering_review` tinyint DEFAULT NULL COMMENT '工程部审核结果：1-通过，2-拒绝',
  `engineering_opinion` text COMMENT '工程部审核意见',
  `engineering_review_time` datetime DEFAULT NULL COMMENT '工程部审核时间',
  `engineering_reviewer_id` bigint DEFAULT NULL COMMENT '工程部审核人ID',
  `engineering_reviewer_name` varchar(50) DEFAULT NULL COMMENT '工程部审核人姓名',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际使用开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际使用结束时间',
  `actual_duration` decimal(10,2) DEFAULT NULL COMMENT '实际使用时长(小时)',
  `work_shift_hours` decimal(10,2) DEFAULT NULL COMMENT '台班时长',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_application_no` (`application_no`),
  KEY `idx_applicant_id` (`applicant_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_use_date` (`use_date`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_assigned_vehicle_id` (`assigned_vehicle_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用车申请表';

-- 3. 车辆使用记录表
DROP TABLE IF EXISTS `vehicle_usage_record`;
CREATE TABLE `vehicle_usage_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `application_id` bigint NOT NULL COMMENT '申请单ID',
  `application_no` varchar(32) NOT NULL COMMENT '申请单号',
  `vehicle_id` bigint NOT NULL COMMENT '车辆ID',
  `vehicle_number` varchar(20) NOT NULL COMMENT '车牌号',
  `user_id` bigint NOT NULL COMMENT '使用人ID',
  `user_name` varchar(50) NOT NULL COMMENT '使用人姓名',
  `department_id` bigint NOT NULL COMMENT '使用部门ID',
  `department_name` varchar(100) NOT NULL COMMENT '使用部门名称',
  `use_date` date NOT NULL COMMENT '使用日期',
  `start_time` datetime NOT NULL COMMENT '开始使用时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束使用时间',
  `duration` decimal(10,2) DEFAULT NULL COMMENT '使用时长(小时)',
  `work_shift_hours` decimal(10,2) DEFAULT NULL COMMENT '台班时长',
  `mileage_start` decimal(10,2) DEFAULT NULL COMMENT '起始里程',
  `mileage_end` decimal(10,2) DEFAULT NULL COMMENT '结束里程',
  `fuel_consumption` decimal(10,2) DEFAULT NULL COMMENT '油耗(升)',
  `use_purpose` text COMMENT '使用目的',
  `remark` text COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_application_id` (`application_id`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_use_date` (`use_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆使用记录表';

-- 4. 车辆计划表
DROP TABLE IF EXISTS `vehicle_schedule`;
CREATE TABLE `vehicle_schedule` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `schedule_date` date NOT NULL COMMENT '计划日期',
  `vehicle_id` bigint NOT NULL COMMENT '车辆ID',
  `vehicle_number` varchar(20) NOT NULL COMMENT '车牌号',
  `application_id` bigint DEFAULT NULL COMMENT '关联申请单ID',
  `application_no` varchar(32) DEFAULT NULL COMMENT '关联申请单号',
  `user_id` bigint DEFAULT NULL COMMENT '计划使用人ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '计划使用人姓名',
  `department_id` bigint DEFAULT NULL COMMENT '使用部门ID',
  `department_name` varchar(100) DEFAULT NULL COMMENT '使用部门名称',
  `planned_start_time` time DEFAULT NULL COMMENT '计划开始时间',
  `planned_end_time` time DEFAULT NULL COMMENT '计划结束时间',
  `planned_duration` decimal(10,2) DEFAULT NULL COMMENT '计划使用时长(小时)',
  `use_purpose` text COMMENT '使用目的',
  `status` tinyint DEFAULT '1' COMMENT '计划状态：1-计划中，2-执行中，3-已完成，4-已取消',
  `remark` text COMMENT '备注',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_schedule_date` (`schedule_date`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_application_id` (`application_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆计划表';

-- 5. 台班统计表
DROP TABLE IF EXISTS `work_shift_statistics`;
CREATE TABLE `work_shift_statistics` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `vehicle_id` bigint NOT NULL COMMENT '车辆ID',
  `vehicle_number` varchar(20) NOT NULL COMMENT '车牌号',
  `department_id` bigint NOT NULL COMMENT '部门ID',
  `department_name` varchar(100) NOT NULL COMMENT '部门名称',
  `total_hours` decimal(10,2) DEFAULT '0.00' COMMENT '总使用时长(小时)',
  `work_shift_hours` decimal(10,2) DEFAULT '0.00' COMMENT '台班时长',
  `usage_count` int DEFAULT '0' COMMENT '使用次数',
  `total_mileage` decimal(10,2) DEFAULT '0.00' COMMENT '总里程',
  `total_fuel` decimal(10,2) DEFAULT '0.00' COMMENT '总油耗(升)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stat_date_vehicle` (`stat_date`,`vehicle_id`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_department_id` (`department_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='台班统计表';

-- 6. 系统配置表
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_desc` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型：string,number,boolean,json',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入初始配置数据
INSERT INTO `system_config` (`config_key`, `config_value`, `config_desc`, `config_type`) VALUES
('vehicle.application.auto_approve', 'false', '是否自动审核用车申请', 'boolean'),
('vehicle.application.max_advance_days', '7', '最大提前申请天数', 'number'),
('vehicle.workshift.standard_hours', '8', '标准台班时长(小时)', 'number'),
('vehicle.notification.enabled', 'true', '是否启用消息通知', 'boolean');

-- 创建索引优化查询性能
-- 车辆信息表索引
CREATE INDEX `idx_vehicle_info_status_dept` ON `vehicle_info` (`status`, `department_id`);

-- 用车申请表索引
CREATE INDEX `idx_vehicle_application_date_status` ON `vehicle_application` (`use_date`, `status`);
CREATE INDEX `idx_vehicle_application_applicant_date` ON `vehicle_application` (`applicant_id`, `use_date`);

-- 车辆使用记录表索引
CREATE INDEX `idx_vehicle_usage_vehicle_date` ON `vehicle_usage_record` (`vehicle_id`, `use_date`);
CREATE INDEX `idx_vehicle_usage_user_date` ON `vehicle_usage_record` (`user_id`, `use_date`);

-- 车辆计划表索引
CREATE INDEX `idx_vehicle_schedule_date_vehicle` ON `vehicle_schedule` (`schedule_date`, `vehicle_id`);

-- 台班统计表索引
CREATE INDEX `idx_work_shift_date_dept` ON `work_shift_statistics` (`stat_date`, `department_id`);

-- 创建视图：车辆使用状态视图
CREATE OR REPLACE VIEW `v_vehicle_status` AS
SELECT 
    vi.id,
    vi.vehicle_number,
    vi.vehicle_model,
    vi.vehicle_brand,
    vi.tonnage,
    vi.status as vehicle_status,
    vi.department_name,
    CASE 
        WHEN va.status = 3 THEN '使用中'
        WHEN va.status = 2 AND va.use_date = CURDATE() THEN '已分配'
        WHEN vi.status = 2 THEN '维修中'
        WHEN vi.status = 3 THEN '报废'
        ELSE '空闲'
    END as current_status,
    va.applicant_name as current_user,
    va.use_date as current_use_date,
    va.use_purpose as current_purpose
FROM vehicle_info vi
LEFT JOIN vehicle_application va ON vi.id = va.assigned_vehicle_id 
    AND va.status IN (2, 3) 
    AND va.use_date = CURDATE()
WHERE vi.deleted = 0;

-- 创建存储过程：生成申请单号
DELIMITER $$
CREATE PROCEDURE `generate_application_no`(OUT application_no VARCHAR(32))
BEGIN
    DECLARE current_date VARCHAR(8);
    DECLARE seq_num INT;
    
    SET current_date = DATE_FORMAT(NOW(), '%Y%m%d');
    
    SELECT IFNULL(MAX(CAST(SUBSTRING(application_no, 9) AS UNSIGNED)), 0) + 1 
    INTO seq_num
    FROM vehicle_application 
    WHERE application_no LIKE CONCAT(current_date, '%');
    
    SET application_no = CONCAT(current_date, LPAD(seq_num, 4, '0'));
END$$
DELIMITER ;

-- 创建触发器：自动生成申请单号
DELIMITER $$
CREATE TRIGGER `tr_vehicle_application_before_insert`
BEFORE INSERT ON `vehicle_application`
FOR EACH ROW
BEGIN
    IF NEW.application_no IS NULL OR NEW.application_no = '' THEN
        CALL generate_application_no(@new_application_no);
        SET NEW.application_no = @new_application_no;
    END IF;
END$$
DELIMITER ;

-- 创建触发器：自动更新台班统计
DELIMITER $$
CREATE TRIGGER `tr_vehicle_usage_after_update`
AFTER UPDATE ON `vehicle_usage_record`
FOR EACH ROW
BEGIN
    IF NEW.work_shift_hours IS NOT NULL AND (OLD.work_shift_hours IS NULL OR OLD.work_shift_hours != NEW.work_shift_hours) THEN
        INSERT INTO work_shift_statistics (
            stat_date, vehicle_id, vehicle_number, department_id, department_name,
            total_hours, work_shift_hours, usage_count, total_mileage, total_fuel
        ) VALUES (
            NEW.use_date, NEW.vehicle_id, NEW.vehicle_number, NEW.department_id, NEW.department_name,
            NEW.duration, NEW.work_shift_hours, 1,
            IFNULL(NEW.mileage_end - NEW.mileage_start, 0), IFNULL(NEW.fuel_consumption, 0)
        )
        ON DUPLICATE KEY UPDATE
            total_hours = total_hours - IFNULL(OLD.duration, 0) + IFNULL(NEW.duration, 0),
            work_shift_hours = work_shift_hours - IFNULL(OLD.work_shift_hours, 0) + IFNULL(NEW.work_shift_hours, 0),
            total_mileage = total_mileage - IFNULL(OLD.mileage_end - OLD.mileage_start, 0) + IFNULL(NEW.mileage_end - NEW.mileage_start, 0),
            total_fuel = total_fuel - IFNULL(OLD.fuel_consumption, 0) + IFNULL(NEW.fuel_consumption, 0),
            update_time = NOW();
    END IF;
END$$
DELIMITER ;

-- 插入示例数据
-- 车辆信息示例数据
INSERT INTO `vehicle_info` (`vehicle_number`, `vehicle_model`, `vehicle_brand`, `tonnage`, `length`, `width`, `height`, `engine_number`, `frame_number`, `purchase_date`, `status`, `department_id`, `department_name`, `create_user`) VALUES
('京A12345', '解放J6P', '一汽解放', 25.00, 12.00, 2.50, 4.00, 'CA6DM2-42E5', 'LFV2A21K8K1234567', '2023-01-15', 1, 1, '第一工程队', 'admin'),
('京A12346', '东风天龙', '东风商用车', 30.00, 13.50, 2.55, 4.20, 'DCI420-42', 'LFV2A21K8K1234568', '2023-02-20', 1, 1, '第一工程队', 'admin'),
('京A12347', '重汽豪沃', '中国重汽', 35.00, 14.00, 2.60, 4.30, 'MC13.48-50', 'LFV2A21K8K1234569', '2023-03-10', 1, 2, '第二工程队', 'admin'),
('京A12348', '陕汽德龙', '陕汽重卡', 28.00, 12.50, 2.50, 4.10, 'WP13.480E62', 'LFV2A21K8K1234570', '2023-04-05', 2, 2, '第二工程队', 'admin'),
('京A12349', '福田欧曼', '福田汽车', 20.00, 11.00, 2.45, 3.80, 'ISGe5-460', 'LFV2A21K8K1234571', '2023-05-12', 1, 3, '第三工程队', 'admin');

-- 系统配置补充数据
INSERT INTO `system_config` (`config_key`, `config_value`, `config_desc`, `config_type`) VALUES
('vehicle.maintenance.reminder_days', '30', '维修提醒提前天数', 'number'),
('vehicle.application.emergency_auto_assign', 'true', '紧急申请是否自动分配车辆', 'boolean'),
('vehicle.usage.max_daily_hours', '12', '每日最大使用时长(小时)', 'number'),
('vehicle.report.export_limit', '10000', '报表导出最大记录数', 'number');

-- 创建常用查询的存储过程
-- 获取车辆使用统计
DELIMITER $$
CREATE PROCEDURE `get_vehicle_usage_stats`(
    IN start_date DATE,
    IN end_date DATE,
    IN vehicle_id BIGINT
)
BEGIN
    SELECT
        v.vehicle_number,
        v.vehicle_model,
        v.department_name,
        COUNT(ur.id) as usage_count,
        SUM(ur.duration) as total_hours,
        SUM(ur.work_shift_hours) as total_work_shift,
        AVG(ur.duration) as avg_duration,
        SUM(IFNULL(ur.mileage_end - ur.mileage_start, 0)) as total_mileage,
        SUM(IFNULL(ur.fuel_consumption, 0)) as total_fuel
    FROM vehicle_info v
    LEFT JOIN vehicle_usage_record ur ON v.id = ur.vehicle_id
        AND ur.use_date BETWEEN start_date AND end_date
        AND ur.deleted = 0
    WHERE v.deleted = 0
        AND (vehicle_id IS NULL OR v.id = vehicle_id)
    GROUP BY v.id, v.vehicle_number, v.vehicle_model, v.department_name
    ORDER BY total_hours DESC;
END$$
DELIMITER ;

-- 获取部门用车统计
DELIMITER $$
CREATE PROCEDURE `get_department_usage_stats`(
    IN start_date DATE,
    IN end_date DATE,
    IN dept_id BIGINT
)
BEGIN
    SELECT
        ur.department_name,
        COUNT(DISTINCT ur.vehicle_id) as vehicle_count,
        COUNT(ur.id) as usage_count,
        SUM(ur.duration) as total_hours,
        SUM(ur.work_shift_hours) as total_work_shift,
        AVG(ur.duration) as avg_duration
    FROM vehicle_usage_record ur
    WHERE ur.use_date BETWEEN start_date AND end_date
        AND ur.deleted = 0
        AND (dept_id IS NULL OR ur.department_id = dept_id)
    GROUP BY ur.department_id, ur.department_name
    ORDER BY total_hours DESC;
END$$
DELIMITER ;

-- 创建数据清理存储过程
DELIMITER $$
CREATE PROCEDURE `cleanup_old_data`(IN days_to_keep INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE cleanup_date DATE;

    SET cleanup_date = DATE_SUB(CURDATE(), INTERVAL days_to_keep DAY);

    -- 清理过期的车辆计划
    DELETE FROM vehicle_schedule
    WHERE schedule_date < cleanup_date AND status IN (3, 4);

    -- 清理过期的台班统计（保留更长时间）
    DELETE FROM work_shift_statistics
    WHERE stat_date < DATE_SUB(CURDATE(), INTERVAL (days_to_keep * 3) DAY);

    SELECT CONCAT('清理完成，清理日期：', cleanup_date) as result;
END$$
DELIMITER ;

-- 创建权限相关表（如果需要独立的权限管理）
DROP TABLE IF EXISTS `vehicle_permission`;
CREATE TABLE `vehicle_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户姓名',
  `department_id` bigint NOT NULL COMMENT '部门ID',
  `department_name` varchar(100) NOT NULL COMMENT '部门名称',
  `role_type` varchar(20) NOT NULL COMMENT '角色类型：applicant-申请人，team_leader-队长，engineering-工程部，admin-管理员',
  `permissions` json COMMENT '权限配置JSON',
  `status` tinyint DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_role_type` (`role_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆管理权限表';

-- 创建数据字典表
DROP TABLE IF EXISTS `vehicle_dictionary`;
CREATE TABLE `vehicle_dictionary` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dict_type` varchar(50) NOT NULL COMMENT '字典类型',
  `dict_code` varchar(20) NOT NULL COMMENT '字典编码',
  `dict_label` varchar(100) NOT NULL COMMENT '字典标签',
  `dict_value` varchar(100) NOT NULL COMMENT '字典值',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `status` tinyint DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dict_type_code` (`dict_type`, `dict_code`),
  KEY `idx_dict_type` (`dict_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='车辆管理数据字典表';

-- 插入数据字典
INSERT INTO `vehicle_dictionary` (`dict_type`, `dict_code`, `dict_label`, `dict_value`, `sort_order`) VALUES
('vehicle_status', '1', '可用', '1', 1),
('vehicle_status', '2', '维修中', '2', 2),
('vehicle_status', '3', '报废', '3', 3),
('application_status', '0', '待队长审核', '0', 1),
('application_status', '1', '待工程部审核', '1', 2),
('application_status', '2', '已分配车辆', '2', 3),
('application_status', '3', '使用中', '3', 4),
('application_status', '4', '已完成', '4', 5),
('application_status', '5', '已拒绝', '5', 6),
('priority_level', '1', '一般', '1', 1),
('priority_level', '2', '紧急', '2', 2),
('review_result', '1', '通过', '1', 1),
('review_result', '2', '拒绝', '2', 2),
('schedule_status', '1', '计划中', '1', 1),
('schedule_status', '2', '执行中', '2', 2),
('schedule_status', '3', '已完成', '3', 3),
('schedule_status', '4', '已取消', '4', 4);

-- 创建定时任务相关的事件（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;

-- 每日自动清理过期数据
-- CREATE EVENT IF NOT EXISTS `daily_cleanup`
-- ON SCHEDULE EVERY 1 DAY
-- STARTS TIMESTAMP(CURRENT_DATE, '02:00:00')
-- DO
--   CALL cleanup_old_data(365);

-- 数据库初始化完成提示
SELECT '车辆管理系统数据库初始化完成！' as message;
