{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "noLib": false, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "strictFunctionTypes": false, "jsx": "preserve", "baseUrl": ".", "allowJs": true, "sourceMap": true, "esModuleInterop": true, "resolveJsonModule": true, "noUnusedLocals": true, "noUnusedParameters": true, "experimentalDecorators": true, "lib": ["dom", "esnext"], "noImplicitAny": false, "skipLibCheck": true, "types": ["vite/client"], "removeComments": true, "paths": {"/@/*": ["src/*"], "/#/*": ["types/*"]}}, "include": ["src", "types", "build", "mock", "vite.config.ts"], "exclude": ["node_modules", "tests/server/**/*.ts", "dist", "**/*.js"]}