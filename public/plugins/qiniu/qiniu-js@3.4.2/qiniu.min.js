!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.qiniu=e():t.qiniu=e()}(window,(function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(r,o,function(e){return t[e]}.bind(null,o));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="/dist/",n(n.s=87)}([function(t,e,n){t.exports=n(88)},function(t,e){var n=t.exports={version:"2.6.11"};"number"==typeof __e&&(__e=n)},function(t,e){t.exports=function(t){return t&&t.__esModule?t:{default:t}}},function(t,e){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(t,e,n){var r=n(3),o=n(1),i=n(13),u=n(14),a=n(15),s=function(t,e,n){var c,f,l,p=t&s.F,h=t&s.G,d=t&s.S,v=t&s.P,y=t&s.B,g=t&s.W,m=h?o:o[e]||(o[e]={}),b=m.prototype,w=h?r:d?r[e]:(r[e]||{}).prototype;for(c in h&&(n=e),n)(f=!p&&w&&void 0!==w[c])&&a(m,c)||(l=f?w[c]:n[c],m[c]=h&&"function"!=typeof w[c]?n[c]:y&&f?i(l,r):g&&w[c]==l?function(t){var e=function(e,n,r){if(this instanceof t){switch(arguments.length){case 0:return new t;case 1:return new t(e);case 2:return new t(e,n)}return new t(e,n,r)}return t.apply(this,arguments)};return e.prototype=t.prototype,e}(l):v&&"function"==typeof l?i(Function.call,l):l,v&&((m.virtual||(m.virtual={}))[c]=l,t&s.R&&b&&!b[c]&&u(b,c,l)))};s.F=1,s.G=2,s.S=4,s.P=8,s.B=16,s.W=32,s.U=64,s.R=128,t.exports=s},function(t,e,n){var r=n(51)("wks"),o=n(35),i=n(3).Symbol,u="function"==typeof i;(t.exports=function(t){return r[t]||(r[t]=u&&i[t]||(u?i:o)("Symbol."+t))}).store=r},function(t,e){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},function(t,e,n){t.exports=!n(16)((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},function(t,e,n){t.exports=n(119)},function(t,e,n){t.exports=n(120)},function(t,e,n){var r=n(11),o=n(66),i=n(45),u=Object.defineProperty;e.f=n(7)?Object.defineProperty:function(t,e,n){if(r(t),e=i(e,!0),r(n),o)try{return u(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(t[e]=n.value),t}},function(t,e,n){var r=n(6);t.exports=function(t){if(!r(t))throw TypeError(t+" is not an object!");return t}},function(t,e,n){t.exports=n(102)},function(t,e,n){var r=n(22);t.exports=function(t,e,n){if(r(t),void 0===e)return t;switch(n){case 1:return function(n){return t.call(e,n)};case 2:return function(n,r){return t.call(e,n,r)};case 3:return function(n,r,o){return t.call(e,n,r,o)}}return function(){return t.apply(e,arguments)}}},function(t,e,n){var r=n(10),o=n(32);t.exports=n(7)?function(t,e,n){return r.f(t,e,o(1,n))}:function(t,e,n){return t[e]=n,t}},function(t,e){var n={}.hasOwnProperty;t.exports=function(t,e){return n.call(t,e)}},function(t,e){t.exports=function(t){try{return!!t()}catch(t){return!0}}},function(t,e,n){var r=n(46),o=n(47);t.exports=function(t){return r(o(t))}},function(t,e,n){"use strict";var r=n(2),o=n(20),i=n(0);i(e,"__esModule",{value:!0});var u={compressImage:!0};i(e,"compressImage",{enumerable:!0,get:function(){return p.default}});var a=n(101);o(a).forEach((function(t){"default"!==t&&"__esModule"!==t&&(Object.prototype.hasOwnProperty.call(u,t)||i(e,t,{enumerable:!0,get:function(){return a[t]}}))}));var s=n(118);o(s).forEach((function(t){"default"!==t&&"__esModule"!==t&&(Object.prototype.hasOwnProperty.call(u,t)||i(e,t,{enumerable:!0,get:function(){return s[t]}}))}));var c=n(80);o(c).forEach((function(t){"default"!==t&&"__esModule"!==t&&(Object.prototype.hasOwnProperty.call(u,t)||i(e,t,{enumerable:!0,get:function(){return c[t]}}))}));var f=n(63);o(f).forEach((function(t){"default"!==t&&"__esModule"!==t&&(Object.prototype.hasOwnProperty.call(u,t)||i(e,t,{enumerable:!0,get:function(){return f[t]}}))}));var l=n(129);o(l).forEach((function(t){"default"!==t&&"__esModule"!==t&&(Object.prototype.hasOwnProperty.call(u,t)||i(e,t,{enumerable:!0,get:function(){return l[t]}}))}));var p=r(n(165))},function(t,e,n){t.exports=n(125)},function(t,e,n){t.exports=n(99)},function(t,e,n){var r=n(13),o=n(109),i=n(110),u=n(11),a=n(48),s=n(111),c={},f={};(e=t.exports=function(t,e,n,l,p){var h,d,v,y,g=p?function(){return t}:s(t),m=r(n,l,e?2:1),b=0;if("function"!=typeof g)throw TypeError(t+" is not iterable!");if(i(g)){for(h=a(t.length);h>b;b++)if((y=e?m(u(d=t[b])[0],d[1]):m(t[b]))===c||y===f)return y}else for(v=g.call(t);!(d=v.next()).done;)if((y=o(v,m,d.value,e))===c||y===f)return y}).BREAK=c,e.RETURN=f},function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},function(t,e,n){"use strict";var r=n(2);n(0)(e,"__esModule",{value:!0}),e.QiniuNetworkError=e.QiniuRequestError=e.QiniuError=e.QiniuErrorName=void 0;var o,i,u=r(n(33)),a=r(n(36)),s=(o=function(t,e){return(o=a.default||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?(0,u.default)(e):(n.prototype=e.prototype,new n)});e.QiniuErrorName=i,function(t){t.InvalidFile="InvalidFile",t.InvalidToken="InvalidToken",t.InvalidMetadata="InvalidMetadata",t.InvalidChunkSize="InvalidChunkSize",t.InvalidCustomVars="InvalidCustomVars",t.NotAvailableUploadHost="NotAvailableUploadHost",t.ReadCacheFailed="ReadCacheFailed",t.InvalidCacheData="InvalidCacheData",t.WriteCacheFailed="WriteCacheFailed",t.RemoveCacheFailed="RemoveCacheFailed",t.GetCanvasContextFailed="GetCanvasContextFailed",t.UnsupportedFileType="UnsupportedFileType",t.FileReaderReadFailed="FileReaderReadFailed",t.NotAvailableXMLHttpRequest="NotAvailableXMLHttpRequest",t.InvalidProgressEventTarget="InvalidProgressEventTarget",t.RequestError="RequestError"}(i||(e.QiniuErrorName=i={}));var c=function(t,e){this.name=t,this.message=e,this.stack=(new Error).stack};e.QiniuError=c;var f=function(t){function e(e,n,r,o){var u=t.call(this,i.RequestError,r)||this;return u.code=e,u.reqId=n,u.isRequestError=!0,u.data=o,u}return s(e,t),e}(c);e.QiniuRequestError=f;var l=function(t){function e(e,n){return void 0===n&&(n=""),t.call(this,0,n,e)||this}return s(e,t),e}(f);e.QiniuNetworkError=l},function(t,e,n){var r=n(67),o=n(52);t.exports=Object.keys||function(t){return r(t,o)}},function(t,e){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},function(t,e){t.exports=!0},function(t,e,n){var r=n(47);t.exports=function(t){return Object(r(t))}},function(t,e){t.exports={}},function(t,e,n){var r=n(10).f,o=n(15),i=n(5)("toStringTag");t.exports=function(t,e,n){t&&!o(t=n?t:t.prototype,i)&&r(t,i,{configurable:!0,value:e})}},function(t,e,n){var r=n(35)("meta"),o=n(6),i=n(15),u=n(10).f,a=0,s=Object.isExtensible||function(){return!0},c=!n(16)((function(){return s(Object.preventExtensions({}))})),f=function(t){u(t,r,{value:{i:"O"+ ++a,w:{}}})},l=t.exports={KEY:r,NEED:!1,fastKey:function(t,e){if(!o(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!i(t,r)){if(!s(t))return"F";if(!e)return"E";f(t)}return t[r].i},getWeak:function(t,e){if(!i(t,r)){if(!s(t))return!0;if(!e)return!1;f(t)}return t[r].w},onFreeze:function(t){return c&&l.NEED&&s(t)&&!i(t,r)&&f(t),t}}},function(t,e,n){var r=n(6);t.exports=function(t,e){if(!r(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},function(t,e,n){t.exports=n(90)},function(t,e,n){var r=n(11),o=n(92),i=n(52),u=n(50)("IE_PROTO"),a=function(){},s=function(){var t,e=n(44)("iframe"),r=i.length;for(e.style.display="none",n(68).appendChild(e),e.src="javascript:",(t=e.contentWindow.document).open(),t.write("<script>document.F=Object<\/script>"),t.close(),s=t.F;r--;)delete s.prototype[i[r]];return s()};t.exports=Object.create||function(t,e){var n;return null!==t?(a.prototype=r(t),n=new a,a.prototype=null,n[u]=t):n=s(),void 0===e?n:o(n,e)}},function(t,e){var n=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+r).toString(36))}},function(t,e,n){t.exports=n(95)},function(t,e){e.f={}.propertyIsEnumerable},function(t,e){},function(t,e,n){n(106);for(var r=n(3),o=n(14),i=n(28),u=n(5)("toStringTag"),a="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),s=0;s<a.length;s++){var c=a[s],f=r[c],l=f&&f.prototype;l&&!l[u]&&o(l,u,c),i[c]=i.Array}},function(t,e){t.exports=function(t,e,n,r){if(!(t instanceof e)||void 0!==r&&r in t)throw TypeError(n+": incorrect invocation!");return t}},function(t,e,n){var r=n(14);t.exports=function(t,e,n){for(var o in e)n&&t[o]?t[o]=e[o]:r(t,o,e[o]);return t}},function(t,e,n){var r=n(136),o=n(0),i=n(76),u=n(139);function a(){if("function"!=typeof u)return null;var t=new u;return a=function(){return t},t}t.exports=function(t){if(t&&t.__esModule)return t;if(null===t||"object"!==i(t)&&"function"!=typeof t)return{default:t};var e=a();if(e&&e.has(t))return e.get(t);var n={},u=o&&r;for(var s in t)if(Object.prototype.hasOwnProperty.call(t,s)){var c=u?r(t,s):null;c&&(c.get||c.set)?o(n,s,c):n[s]=t[s]}return n.default=t,e&&e.set(t,n),n}},function(t,e,n){"use strict";var r=n(42),o=n(2);n(0)(e,"__esModule",{value:!0}),e.getUpHosts=d,e.initUploadParts=function(t,e,n,r){var o=r+"/buckets/"+e+"/objects/"+(null!=n?f.urlSafeBase64Encode(n):"~")+"/uploads";return f.request(o,{method:"POST",headers:f.getAuthHeaders(t)})},e.uploadChunk=function(t,e,n,r,o){var i=v(f.getPutPolicy(t).bucketName,e,r)+"/"+n,u=f.getHeadersForChunkUpload(t);o.md5&&(u["Content-MD5"]=o.md5);return f.request(i,l(l({},o),{method:"PUT",headers:u}))},e.uploadComplete=function(t,e,n,r){var o=v(f.getPutPolicy(t).bucketName,e,n);return f.request(o,l(l({},r),{method:"POST",headers:f.getHeadersForMkFile(t)}))},e.deleteUploadedChunks=function(t,e,n){var r=v(f.getPutPolicy(t).bucketName,e,n);return f.request(r,{method:"DELETE",headers:f.getAuthHeaders(t)})},e.direct=function(t,e,n){return f.request(t,l({method:"POST",body:e},n))},e.getUploadUrl=function(t,e){return p(this,void 0,void 0,(function(){var n,r,o,i,u;return h(this,(function(a){switch(a.label){case 0:return n=(0,f.normalizeUploadConfig)(t),r=n.upprotocol,n.uphost.length>0?[2,r+"://"+n.uphost[0]]:[4,d((o=f.getPutPolicy(e)).assessKey,o.bucketName,r)];case 1:return i=a.sent(),u=i.data.up.acc.main,[2,r+"://"+u[0]]}}))}))};var i=o(n(8)),u=o(n(9)),a=o(n(12)),s=o(n(19)),c=n(148),f=r(n(18)),l=function(){return(l=s.default||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},p=function(t,e,n,r){return new(n||(n=a.default))((function(o,i){function u(t){try{s(r.next(t))}catch(t){i(t)}}function a(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}s((r=r.apply(t,e||[])).next())}))},h=function(t,e){var n,r,o,a,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof u.default&&(a[i.default]=function(){return this}),a;function c(i){return function(u){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=e.call(t,s)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}};function d(t,e,n){return p(this,void 0,void 0,(function(){var r,o;return h(this,(function(i){return r=(0,c.stringify)({ak:t,bucket:e}),o=n+"://api.qiniu.com/v2/query?"+r,[2,f.request(o,{method:"GET"})]}))}))}function v(t,e,n){var r=n.url,o=n.id;return r+"/buckets/"+t+"/objects/"+(null!=e?f.urlSafeBase64Encode(e):"~")+"/uploads/"+o}},function(t,e,n){var r=n(6),o=n(3).document,i=r(o)&&r(o.createElement);t.exports=function(t){return i?o.createElement(t):{}}},function(t,e,n){var r=n(6);t.exports=function(t,e){if(!r(t))return t;var n,o;if(e&&"function"==typeof(n=t.toString)&&!r(o=n.call(t)))return o;if("function"==typeof(n=t.valueOf)&&!r(o=n.call(t)))return o;if(!e&&"function"==typeof(n=t.toString)&&!r(o=n.call(t)))return o;throw TypeError("Can't convert object to primitive value")}},function(t,e,n){var r=n(25);t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==r(t)?t.split(""):Object(t)}},function(t,e){t.exports=function(t){if(null==t)throw TypeError("Can't call method on  "+t);return t}},function(t,e,n){var r=n(49),o=Math.min;t.exports=function(t){return t>0?o(r(t),9007199254740991):0}},function(t,e){var n=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?r:n)(t)}},function(t,e,n){var r=n(51)("keys"),o=n(35);t.exports=function(t){return r[t]||(r[t]=o(t))}},function(t,e,n){var r=n(1),o=n(3),i="__core-js_shared__",u=o[i]||(o[i]={});(t.exports=function(t,e){return u[t]||(u[t]=void 0!==e?e:{})})("versions",[]).push({version:r.version,mode:n(26)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},function(t,e){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(t,e,n){var r=n(37),o=n(32),i=n(17),u=n(45),a=n(15),s=n(66),c=Object.getOwnPropertyDescriptor;e.f=n(7)?c:function(t,e){if(t=i(t),e=u(e,!0),s)try{return c(t,e)}catch(t){}if(a(t,e))return o(!r.f.call(t,e),t[e])}},function(t,e,n){"use strict";var r=n(103)(!0);n(55)(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,n=this._i;return n>=e.length?{value:void 0,done:!0}:(t=r(e,n),this._i+=t.length,{value:t,done:!1})}))},function(t,e,n){"use strict";var r=n(26),o=n(4),i=n(56),u=n(14),a=n(28),s=n(104),c=n(29),f=n(105),l=n(5)("iterator"),p=!([].keys&&"next"in[].keys()),h="keys",d="values",v=function(){return this};t.exports=function(t,e,n,y,g,m,b){s(n,e,y);var w,_,x,k=function(t){if(!p&&t in C)return C[t];switch(t){case h:case d:return function(){return new n(this,t)}}return function(){return new n(this,t)}},E=e+" Iterator",O=g==d,S=!1,C=t.prototype,P=C[l]||C["@@iterator"]||g&&C[g],I=P||k(g),A=g?O?k("entries"):I:void 0,U="Array"==e&&C.entries||P;if(U&&(x=f(U.call(new t)))!==Object.prototype&&x.next&&(c(x,E,!0),r||"function"==typeof x[l]||u(x,l,v)),O&&P&&P.name!==d&&(S=!0,I=function(){return P.call(this)}),r&&!b||!p&&!S&&C[l]||u(C,l,I),a[e]=I,a[E]=v,g)if(w={values:O?I:k(d),keys:m?I:k(h),entries:A},b)for(_ in w)_ in C||i(C,_,w[_]);else o(o.P+o.F*(p||S),e,w);return w}},function(t,e,n){t.exports=n(14)},function(t,e,n){var r=n(25),o=n(5)("toStringTag"),i="Arguments"==r(function(){return arguments}());t.exports=function(t){var e,n,u;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),o))?n:i?r(e):"Object"==(u=r(e))&&"function"==typeof e.callee?"Arguments":u}},function(t,e,n){"use strict";var r=n(22);function o(t){var e,n;this.promise=new t((function(t,r){if(void 0!==e||void 0!==n)throw TypeError("Bad Promise constructor");e=t,n=r})),this.resolve=r(e),this.reject=r(n)}t.exports.f=function(t){return new o(t)}},function(t,e,n){e.f=n(5)},function(t,e,n){var r=n(3),o=n(1),i=n(26),u=n(59),a=n(10).f;t.exports=function(t){var e=o.Symbol||(o.Symbol=i?{}:r.Symbol||{});"_"==t.charAt(0)||t in e||a(e,t,{value:u.f(t)})}},function(t,e){e.f=Object.getOwnPropertySymbols},function(t,e,n){var r=n(25);t.exports=Array.isArray||function(t){return"Array"==r(t)}},function(t,e,n){"use strict";var r=n(2);n(0)(e,"__esModule",{value:!0}),e.getChunks=function(t,e){var n=e*y;if(n>t.size)n=t.size;else for(;t.size>1e4*n;)n*=2;for(var r=[],o=Math.ceil(t.size/n),i=0;i<o;i++){var u=t.slice(n*i,i===o-1?t.size:n*(i+1));r.push(u)}return r},e.isMetaDataValid=function(t){return(0,i.default)(t).every((function(t){return 0===t.indexOf("x-qn-meta-")}))},e.isCustomVarsValid=function(t){return(0,i.default)(t).every((function(t){return 0===t.indexOf("x:")}))},e.sum=function(t){return t.reduce((function(t,e){return t+e}),0)},e.setLocalFileInfo=function(t,e,n){try{localStorage.setItem(t,(0,o.default)(e))}catch(e){n.warn(new l.QiniuError(l.QiniuErrorName.WriteCacheFailed,"setLocalFileInfo failed: "+t))}},e.createLocalKey=function(t,e,n){return"qiniu_js_sdk_upload_file_name_"+t+(null==e?"_":"_key_"+e+"_")+"size_"+n},e.removeLocalFileInfo=g,e.getLocalFileInfo=function(t,e){var n=null;try{n=localStorage.getItem(t)}catch(n){e.warn(new l.QiniuError(l.QiniuErrorName.ReadCacheFailed,"getLocalFileInfo failed. key: "+t))}if(null==n)return null;var r=null;try{r=JSON.parse(n)}catch(n){g(t,e),e.warn(new l.QiniuError(l.QiniuErrorName.InvalidCacheData,"getLocalFileInfo failed to parse. key: "+t))}return r},e.getAuthHeaders=m,e.getHeadersForChunkUpload=function(t){var e=m(t);return h({"content-type":"application/octet-stream"},e)},e.getHeadersForMkFile=function(t){var e=m(t);return h({"content-type":"application/json"},e)},e.createXHR=b,e.computeMd5=function(t){return d(this,void 0,void 0,(function(){var e,n;return v(this,(function(r){switch(r.label){case 0:return[4,w(t)];case 1:return e=r.sent(),(n=new f.default.ArrayBuffer).append(e),[2,n.end()]}}))}))},e.readAsArrayBuffer=w,e.request=function(t,e){return new s.default((function(n,r){var o=b();if(o.open(e.method,t),e.onCreate&&e.onCreate(o),e.headers){var u=e.headers;(0,i.default)(u).forEach((function(t){o.setRequestHeader(t,u[t])}))}o.upload.addEventListener("progress",(function(t){t.lengthComputable&&e.onProgress&&e.onProgress({loaded:t.loaded,total:t.total})})),o.onreadystatechange=function(){var t=o.responseText;if(4===o.readyState){var e=o.getResponseHeader("x-reqId")||"";if(0!==o.status)if(200===o.status)try{n({data:JSON.parse(t),reqId:e})}catch(t){r(t)}else{var i="xhr request failed, code: "+o.status;t&&(i+=" response: "+t);var u=void 0;try{u=JSON.parse(t)}catch(t){}r(new l.QiniuRequestError(o.status,e,i,u))}else r(new l.QiniuNetworkError("network error.",e))}},o.send(e.body)}))},e.getPortFromUrl=function(t){if(t&&t.match){var e=t.match(/(^https?)/);if(!e)return"";var n=e[1];return(e=t.match(/^https?:\/\/([^:^/]*):(\d*)/))?e[2]:"http"===n?"80":"443"}return""},e.getDomainFromUrl=function(t){if(t&&t.match){var e=t.match(/^https?:\/\/([^:^/]*)/);return e?e[1]:""}return""},e.getPutPolicy=function(t){if(!t)throw new l.QiniuError(l.QiniuErrorName.InvalidToken,"invalid token.");var e=t.split(":");if(1===e.length)throw new l.QiniuError(l.QiniuErrorName.InvalidToken,"invalid token segments.");var n=e.length>3?e[1]:e[0];if(!n)throw new l.QiniuError(l.QiniuErrorName.InvalidToken,"missing assess key field.");var r=null;try{r=JSON.parse((0,p.urlSafeBase64Decode)(e[e.length-1]))}catch(t){throw new l.QiniuError(l.QiniuErrorName.InvalidToken,"token parse failed.")}if(null==r)throw new l.QiniuError(l.QiniuErrorName.InvalidToken,"putPolicy is null.");if(null==r.scope)throw new l.QiniuError(l.QiniuErrorName.InvalidToken,"scope field is null.");var o=r.scope.split(":")[0];if(!o)throw new l.QiniuError(l.QiniuErrorName.InvalidToken,"resolve bucketName failed.");return{assessKey:n,bucketName:o,scope:r.scope}},e.createObjectURL=function(t){return(window.URL||window.webkitURL||window.mozURL).createObjectURL(t)},e.MB=void 0;var o=r(n(81)),i=r(n(20)),u=r(n(8)),a=r(n(9)),s=r(n(12)),c=r(n(19)),f=r(n(128)),l=n(23),p=n(80),h=function(){return(h=c.default||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},d=function(t,e,n,r){return new(n||(n=s.default))((function(o,i){function u(t){try{s(r.next(t))}catch(t){i(t)}}function a(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}s((r=r.apply(t,e||[])).next())}))},v=function(t,e){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof a.default&&(i[u.default]=function(){return this}),i;function c(i){return function(u){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=e.call(t,s)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},y=Math.pow(1024,2);function g(t,e){try{localStorage.removeItem(t)}catch(n){e.warn(new l.QiniuError(l.QiniuErrorName.RemoveCacheFailed,"removeLocalFileInfo failed. key: "+t))}}function m(t){return{Authorization:"UpToken "+t}}function b(){if(window.XMLHttpRequest)return new XMLHttpRequest;if(window.ActiveXObject)return new window.ActiveXObject("Microsoft.XMLHTTP");throw new l.QiniuError(l.QiniuErrorName.NotAvailableXMLHttpRequest,"the current environment does not support.")}function w(t){return new s.default((function(e,n){var r=new FileReader;r.onload=function(t){if(t.target){var r=t.target.result;e(r)}else n(new l.QiniuError(l.QiniuErrorName.InvalidProgressEventTarget,"progress event target is undefined"))},r.onerror=function(){n(new l.QiniuError(l.QiniuErrorName.FileReaderReadFailed,"fileReader read failed"))},r.readAsArrayBuffer(t)}))}e.MB=y},function(t,e,n){var r=n(13),o=n(46),i=n(27),u=n(48),a=n(142);t.exports=function(t,e){var n=1==t,s=2==t,c=3==t,f=4==t,l=6==t,p=5==t||l,h=e||a;return function(e,a,d){for(var v,y,g=i(e),m=o(g),b=r(a,d,3),w=u(m.length),_=0,x=n?h(e,w):s?h(e,0):void 0;w>_;_++)if((p||_ in m)&&(y=b(v=m[_],_,g),t))if(n)x[_]=y;else if(y)switch(t){case 3:return!0;case 5:return v;case 6:return _;case 2:x.push(v)}else if(f)return!1;return l?-1:c||f?f:x}}},function(t,e,n){"use strict";var r=n(42),o=n(2);n(0)(e,"__esModule",{value:!0}),e.default=e.RETRY_CODE_LIST=e.FREEZE_CODE_LIST=e.DEFAULT_CHUNK_SIZE=void 0;var i=o(n(8)),u=o(n(9)),a=o(n(12)),s=o(n(19)),c=n(23),f=r(n(18)),l=function(){return(l=s.default||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},p=function(t,e,n,r){return new(n||(n=a.default))((function(o,i){function u(t){try{s(r.next(t))}catch(t){i(t)}}function a(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}s((r=r.apply(t,e||[])).next())}))},h=function(t,e){var n,r,o,a,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof u.default&&(a[i.default]=function(){return this}),a;function c(i){return function(u){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=e.call(t,s)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},d=function(t,e){var n="function"==typeof u.default&&t[i.default];if(!n)return t;var r,o,a=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(o)throw o.error}}return s},v=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(d(arguments[e]));return t};e.DEFAULT_CHUNK_SIZE=4;var y=[0,502,503,504,599];e.FREEZE_CODE_LIST=y;var g=v(y,[612]);e.RETRY_CODE_LIST=g;var m=Math.pow(1024,3),b=function(){function t(t,e,n,r){this.hostPool=n,this.logger=r,this.aborted=!1,this.retryCount=0,this.xhrList=[],this.config=t.config,r.info("config inited.",this.config),this.putExtra=l({fname:""},t.putExtra),r.info("putExtra inited.",this.putExtra),this.key=t.key,this.file=t.file,this.token=t.token,this.onData=e.onData,this.onError=e.onError,this.onComplete=e.onComplete;try{var o=f.getPutPolicy(this.token);this.bucketName=o.bucketName,this.assessKey=o.assessKey}catch(t){r.error("get putPolicy from token failed.",t),this.onError(t)}}return t.prototype.checkAndUpdateUploadHost=function(){return p(this,void 0,void 0,(function(){var t;return h(this,(function(e){switch(e.label){case 0:return this.logger.info("get available upload host."),[4,this.hostPool.getUp(this.assessKey,this.bucketName,this.config.upprotocol)];case 1:if(null==(t=e.sent()))throw new c.QiniuError(c.QiniuErrorName.NotAvailableUploadHost,"no available upload host.");return null!=this.uploadHost&&this.uploadHost.host!==t.host?this.logger.warn("host switches from "+this.uploadHost.host+" to "+t.host+"."):this.logger.info("use host "+t.host+"."),this.uploadHost=t,[2]}}))}))},t.prototype.checkAndUnfreezeHost=function(){this.logger.info("check unfreeze host."),null!=this.uploadHost&&this.uploadHost.isFrozen()&&(this.logger.warn(this.uploadHost.host+" will be unfrozen."),this.uploadHost.unfreeze())},t.prototype.checkAndFreezeHost=function(t){this.logger.info("check freeze host."),t instanceof c.QiniuRequestError&&null!=this.uploadHost&&y.includes(t.code)&&(this.logger.warn(this.uploadHost.host+" will be temporarily frozen."),this.uploadHost.freeze())},t.prototype.handleError=function(t){this.logger.error(t.message),this.onError(t)},t.prototype.putFile=function(){return p(this,void 0,void 0,(function(){var t,e,n;return h(this,(function(r){switch(r.label){case 0:if(this.aborted=!1,this.putExtra.fname||(this.logger.info("use file.name as fname."),this.putExtra.fname=this.file.name),this.file.size>1e4*m)return this.handleError(new c.QiniuError(c.QiniuErrorName.InvalidFile,"file size exceed maximum value 10000G")),[2];if(this.putExtra.customVars&&!f.isCustomVarsValid(this.putExtra.customVars))return this.handleError(new c.QiniuError(c.QiniuErrorName.InvalidCustomVars,"customVars key should start width x:")),[2];if(this.putExtra.metadata&&!f.isMetaDataValid(this.putExtra.metadata))return this.handleError(new c.QiniuError(c.QiniuErrorName.InvalidMetadata,"metadata key should start with x-qn-meta-")),[2];r.label=1;case 1:return r.trys.push([1,4,,5]),this.uploadAt=(new Date).getTime(),[4,this.checkAndUpdateUploadHost()];case 2:return r.sent(),[4,this.run()];case 3:return t=r.sent(),this.onComplete(t.data),this.checkAndUnfreezeHost(),this.sendLog(t.reqId,200),[2];case 4:return e=r.sent(),this.aborted?(this.logger.warn("upload is aborted."),this.sendLog("",-2),[2]):(this.clear(),this.logger.error(e),e instanceof c.QiniuRequestError&&(this.sendLog(e.reqId,e.code),this.checkAndFreezeHost(e),n=++this.retryCount<=this.config.retryCount,g.includes(e.code)&&n)?(this.logger.warn("error auto retry: "+this.retryCount+"/"+this.config.retryCount+"."),this.putFile(),[2]):(this.onError(e),[3,5]));case 5:return[2]}}))}))},t.prototype.clear=function(){this.xhrList.forEach((function(t){t.onreadystatechange=null,t.abort()})),this.xhrList=[],this.logger.info("cleanup uploading xhr.")},t.prototype.stop=function(){this.logger.info("aborted."),this.clear(),this.aborted=!0},t.prototype.addXhr=function(t){this.xhrList.push(t)},t.prototype.sendLog=function(t,e){var n,r;this.logger.report({code:e,reqId:t,remoteIp:"",upType:"jssdk-h5",size:this.file.size,time:Math.floor(this.uploadAt/1e3),port:f.getPortFromUrl(null===(n=this.uploadHost)||void 0===n?void 0:n.getUrl()),host:f.getDomainFromUrl(null===(r=this.uploadHost)||void 0===r?void 0:r.getUrl()),bytesSent:this.progress?this.progress.total.loaded:0,duration:Math.floor(((new Date).getTime()-this.uploadAt)/1e3)})},t.prototype.getProgressInfoItem=function(t,e,n){return l({size:e,loaded:t,percent:t/e*100},null==n?{}:{fromCache:n})},t}();e.default=b},function(t,e,n){t.exports=!n(7)&&!n(16)((function(){return 7!=Object.defineProperty(n(44)("div"),"a",{get:function(){return 7}}).a}))},function(t,e,n){var r=n(15),o=n(17),i=n(93)(!1),u=n(50)("IE_PROTO");t.exports=function(t,e){var n,a=o(t),s=0,c=[];for(n in a)n!=u&&r(a,n)&&c.push(n);for(;e.length>s;)r(a,n=e[s++])&&(~i(c,n)||c.push(n));return c}},function(t,e,n){var r=n(3).document;t.exports=r&&r.documentElement},function(t,e,n){var r=n(4),o=n(1),i=n(16);t.exports=function(t,e){var n=(o.Object||{})[t]||Object[t],u={};u[t]=e(n),r(r.S+r.F*i((function(){n(1)})),"Object",u)}},function(t,e){t.exports=function(t,e){return{value:e,done:!!t}}},function(t,e,n){var r=n(11),o=n(22),i=n(5)("species");t.exports=function(t,e){var n,u=r(t).constructor;return void 0===u||null==(n=r(u)[i])?e:o(n)}},function(t,e,n){var r,o,i,u=n(13),a=n(112),s=n(68),c=n(44),f=n(3),l=f.process,p=f.setImmediate,h=f.clearImmediate,d=f.MessageChannel,v=f.Dispatch,y=0,g={},m="onreadystatechange",b=function(){var t=+this;if(g.hasOwnProperty(t)){var e=g[t];delete g[t],e()}},w=function(t){b.call(t.data)};p&&h||(p=function(t){for(var e=[],n=1;arguments.length>n;)e.push(arguments[n++]);return g[++y]=function(){a("function"==typeof t?t:Function(t),e)},r(y),y},h=function(t){delete g[t]},"process"==n(25)(l)?r=function(t){l.nextTick(u(b,t,1))}:v&&v.now?r=function(t){v.now(u(b,t,1))}:d?(i=(o=new d).port2,o.port1.onmessage=w,r=u(i.postMessage,i,1)):f.addEventListener&&"function"==typeof postMessage&&!f.importScripts?(r=function(t){f.postMessage(t+"","*")},f.addEventListener("message",w,!1)):r=m in c("script")?function(t){s.appendChild(c("script")).onreadystatechange=function(){s.removeChild(this),b.call(t)}}:function(t){setTimeout(u(b,t,1),0)}),t.exports={set:p,clear:h}},function(t,e){t.exports=function(t){try{return{e:!1,v:t()}}catch(t){return{e:!0,v:t}}}},function(t,e,n){var r=n(11),o=n(6),i=n(58);t.exports=function(t,e){if(r(t),o(e)&&e.constructor===t)return e;var n=i.f(t);return(0,n.resolve)(e),n.promise}},function(t,e,n){"use strict";var r=n(3),o=n(1),i=n(10),u=n(7),a=n(5)("species");t.exports=function(t){var e="function"==typeof o[t]?o[t]:r[t];u&&e&&!e[a]&&i.f(e,a,{configurable:!0,get:function(){return this}})}},function(t,e,n){var r=n(8),o=n(9);function i(e){return t.exports=i="function"==typeof o&&"symbol"==typeof r?function(t){return typeof t}:function(t){return t&&"function"==typeof o&&t.constructor===o&&t!==o.prototype?"symbol":typeof t},i(e)}t.exports=i},function(t,e,n){"use strict";var r=n(3),o=n(15),i=n(7),u=n(4),a=n(56),s=n(30).KEY,c=n(16),f=n(51),l=n(29),p=n(35),h=n(5),d=n(59),v=n(60),y=n(121),g=n(62),m=n(11),b=n(6),w=n(27),_=n(17),x=n(45),k=n(32),E=n(34),O=n(122),S=n(53),C=n(61),P=n(10),I=n(24),A=S.f,U=P.f,M=O.f,j=r.Symbol,R=r.JSON,F=R&&R.stringify,L=h("_hidden"),T=h("toPrimitive"),N={}.propertyIsEnumerable,z=f("symbol-registry"),q=f("symbols"),H=f("op-symbols"),Q=Object.prototype,D="function"==typeof j&&!!C.f,B=r.QObject,V=!B||!B.prototype||!B.prototype.findChild,G=i&&c((function(){return 7!=E(U({},"a",{get:function(){return U(this,"a",{value:7}).a}})).a}))?function(t,e,n){var r=A(Q,e);r&&delete Q[e],U(t,e,n),r&&t!==Q&&U(Q,e,r)}:U,W=function(t){var e=q[t]=E(j.prototype);return e._k=t,e},K=D&&"symbol"==typeof j.iterator?function(t){return"symbol"==typeof t}:function(t){return t instanceof j},J=function(t,e,n){return t===Q&&J(H,e,n),m(t),e=x(e,!0),m(n),o(q,e)?(n.enumerable?(o(t,L)&&t[L][e]&&(t[L][e]=!1),n=E(n,{enumerable:k(0,!1)})):(o(t,L)||U(t,L,k(1,{})),t[L][e]=!0),G(t,e,n)):U(t,e,n)},X=function(t,e){m(t);for(var n,r=y(e=_(e)),o=0,i=r.length;i>o;)J(t,n=r[o++],e[n]);return t},Z=function(t){var e=N.call(this,t=x(t,!0));return!(this===Q&&o(q,t)&&!o(H,t))&&(!(e||!o(this,t)||!o(q,t)||o(this,L)&&this[L][t])||e)},Y=function(t,e){if(t=_(t),e=x(e,!0),t!==Q||!o(q,e)||o(H,e)){var n=A(t,e);return!n||!o(q,e)||o(t,L)&&t[L][e]||(n.enumerable=!0),n}},$=function(t){for(var e,n=M(_(t)),r=[],i=0;n.length>i;)o(q,e=n[i++])||e==L||e==s||r.push(e);return r},tt=function(t){for(var e,n=t===Q,r=M(n?H:_(t)),i=[],u=0;r.length>u;)!o(q,e=r[u++])||n&&!o(Q,e)||i.push(q[e]);return i};D||(a((j=function(){if(this instanceof j)throw TypeError("Symbol is not a constructor!");var t=p(arguments.length>0?arguments[0]:void 0),e=function(n){this===Q&&e.call(H,n),o(this,L)&&o(this[L],t)&&(this[L][t]=!1),G(this,t,k(1,n))};return i&&V&&G(Q,t,{configurable:!0,set:e}),W(t)}).prototype,"toString",(function(){return this._k})),S.f=Y,P.f=J,n(78).f=O.f=$,n(37).f=Z,C.f=tt,i&&!n(26)&&a(Q,"propertyIsEnumerable",Z,!0),d.f=function(t){return W(h(t))}),u(u.G+u.W+u.F*!D,{Symbol:j});for(var et="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),nt=0;et.length>nt;)h(et[nt++]);for(var rt=I(h.store),ot=0;rt.length>ot;)v(rt[ot++]);u(u.S+u.F*!D,"Symbol",{for:function(t){return o(z,t+="")?z[t]:z[t]=j(t)},keyFor:function(t){if(!K(t))throw TypeError(t+" is not a symbol!");for(var e in z)if(z[e]===t)return e},useSetter:function(){V=!0},useSimple:function(){V=!1}}),u(u.S+u.F*!D,"Object",{create:function(t,e){return void 0===e?E(t):X(E(t),e)},defineProperty:J,defineProperties:X,getOwnPropertyDescriptor:Y,getOwnPropertyNames:$,getOwnPropertySymbols:tt});var it=c((function(){C.f(1)}));u(u.S+u.F*it,"Object",{getOwnPropertySymbols:function(t){return C.f(w(t))}}),R&&u(u.S+u.F*(!D||c((function(){var t=j();return"[null]"!=F([t])||"{}"!=F({a:t})||"{}"!=F(Object(t))}))),"JSON",{stringify:function(t){for(var e,n,r=[t],o=1;arguments.length>o;)r.push(arguments[o++]);if(n=e=r[1],(b(e)||void 0!==t)&&!K(t))return g(e)||(e=function(t,e){if("function"==typeof n&&(e=n.call(this,t,e)),!K(e))return e}),r[1]=e,F.apply(R,r)}}),j.prototype[T]||n(14)(j.prototype,T,j.prototype.valueOf),l(j,"Symbol"),l(Math,"Math",!0),l(r.JSON,"JSON",!0)},function(t,e,n){var r=n(67),o=n(52).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return r(t,o)}},function(t,e,n){"use strict";var r=n(7),o=n(24),i=n(61),u=n(37),a=n(27),s=n(46),c=Object.assign;t.exports=!c||n(16)((function(){var t={},e={},n=Symbol(),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach((function(t){e[t]=t})),7!=c({},t)[n]||Object.keys(c({},e)).join("")!=r}))?function(t,e){for(var n=a(t),c=arguments.length,f=1,l=i.f,p=u.f;c>f;)for(var h,d=s(arguments[f++]),v=l?o(d).concat(l(d)):o(d),y=v.length,g=0;y>g;)h=v[g++],r&&!p.call(d,h)||(n[h]=d[h]);return n}:c},function(t,e,n){"use strict";n(0)(e,"__esModule",{value:!0}),e.urlSafeBase64Encode=function(t){return(t=function(t){var e,n,r,o,i,u,a,s,c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",f=0,l=0,p="",h=[];if(!t)return t;t=function(t){if(null==t)return"";var e,n,r=t+"",o="",i=0;e=n=0,i=r.length;for(var u=0;u<i;u++){var a=r.charCodeAt(u),s=null;if(a<128)n++;else if(a>127&&a<2048)s=String.fromCharCode(a>>6|192,63&a|128);else if((63488&a^55296)>0)s=String.fromCharCode(a>>12|224,a>>6&63|128,63&a|128);else{if((64512&a^55296)>0)throw new RangeError("Unmatched trail surrogate at "+u);var c=r.charCodeAt(++u);if((64512&c^56320)>0)throw new RangeError("Unmatched lead surrogate at "+(u-1));a=((1023&a)<<10)+(1023&c)+65536,s=String.fromCharCode(a>>18|240,a>>12&63|128,a>>6&63|128,63&a|128)}null!==s&&(n>e&&(o+=r.slice(e,n)),o+=s,e=n=u+1)}n>e&&(o+=r.slice(e,i));return o}(t+"");do{e=t.charCodeAt(f++),n=t.charCodeAt(f++),r=t.charCodeAt(f++),o=(s=e<<16|n<<8|r)>>18&63,i=s>>12&63,u=s>>6&63,a=63&s,h[l++]=c.charAt(o)+c.charAt(i)+c.charAt(u)+c.charAt(a)}while(f<t.length);switch(p=h.join(""),t.length%3){case 1:p=p.slice(0,-2)+"==";break;case 2:p=p.slice(0,-1)+"="}return p}(t)).replace(/\//g,"_").replace(/\+/g,"-")},e.urlSafeBase64Decode=function(t){return function(t){var e,n,r,o,i,u,a,s,c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",f=0,l=0,p=[];if(!t)return t;t+="";do{o=c.indexOf(t.charAt(f++)),i=c.indexOf(t.charAt(f++)),u=c.indexOf(t.charAt(f++)),a=c.indexOf(t.charAt(f++)),e=(s=o<<18|i<<12|u<<6|a)>>16&255,n=s>>8&255,r=255&s,p[l++]=64===u?String.fromCharCode(e):64===a?String.fromCharCode(e,n):String.fromCharCode(e,n,r)}while(f<t.length);return function(t){var e=[],n=0,r=0,o=0;t+="";for(;n<t.length;){r=255&t.charCodeAt(n),o=0,r<=191?(r&=127,o=1):r<=223?(r&=31,o=2):r<=239?(r&=15,o=3):(r&=7,o=4);for(var i=1;i<o;++i)r=r<<6|63&t.charCodeAt(i+n);4===o?(r-=65536,e.push(String.fromCharCode(55296|r>>10&1023)),e.push(String.fromCharCode(56320|1023&r))):e.push(String.fromCharCode(r)),n+=o}return e.join("")}(p.join(""))}(t=t.replace(/_/g,"/").replace(/-/g,"+"))}},function(t,e,n){t.exports=n(127)},function(t,e,n){"use strict";var r=n(20),o=n(0);o(e,"__esModule",{value:!0});var i=n(135);r(i).forEach((function(t){"default"!==t&&"__esModule"!==t&&o(e,t,{enumerable:!0,get:function(){return i[t]}})}))},function(t,e,n){"use strict";var r=n(2),o=n(42),i=n(20),u=n(0);u(e,"__esModule",{value:!0});var a={createUploadManager:!0};e.createUploadManager=d,e.default=function(t,e,n,r,o){var i=new f.default(n,null==o?void 0:o.disableStatisticsReport,null==o?void 0:o.debugLogLevel,t.name),u={file:t,key:e,token:n,putExtra:r,config:(0,l.normalizeUploadConfig)(o,i)},a=new p.HostPool(u.config.uphost);return new l.Observable((function(t){var e=d(u,{onData:function(e){return t.next(e)},onError:function(e){return t.error(e)},onComplete:function(e){return t.complete(e)}},a,i);return e.putFile(),e.stop.bind(e)}))};var s=o(n(147));i(s).forEach((function(t){"default"!==t&&"__esModule"!==t&&(Object.prototype.hasOwnProperty.call(a,t)||u(e,t,{enumerable:!0,get:function(){return s[t]}}))}));var c=r(n(151)),f=r(n(153)),l=n(18),p=n(155),h=n(65);function d(t,e,n,r){return t.config&&t.config.forceDirect?(r.info("ues forceDirect mode."),new c.default(t,e,n,r)):t.file.size>4*l.MB?(r.info("file size over 4M, use Resume."),new s.default(t,e,n,r)):(r.info("file size less or equal than 4M, use Direct."),new c.default(t,e,n,r))}i(h).forEach((function(t){"default"!==t&&"__esModule"!==t&&(Object.prototype.hasOwnProperty.call(a,t)||u(e,t,{enumerable:!0,get:function(){return h[t]}}))}))},function(t,e,n){"use strict";var r=n(3),o=n(4),i=n(30),u=n(16),a=n(14),s=n(41),c=n(21),f=n(40),l=n(6),p=n(29),h=n(10).f,d=n(64)(0),v=n(7);t.exports=function(t,e,n,y,g,m){var b=r[t],w=b,_=g?"set":"add",x=w&&w.prototype,k={};return v&&"function"==typeof w&&(m||x.forEach&&!u((function(){(new w).entries().next()})))?(w=e((function(e,n){f(e,w,t,"_c"),e._c=new b,null!=n&&c(n,g,e[_],e)})),d("add,clear,delete,forEach,get,has,set,keys,values,entries,toJSON".split(","),(function(t){var e="add"==t||"set"==t;!(t in x)||m&&"clear"==t||a(w.prototype,t,(function(n,r){if(f(this,w,t),!e&&m&&!l(n))return"get"==t&&void 0;var o=this._c[t](0===n?0:n,r);return e?this:o}))})),m||h(w.prototype,"size",{get:function(){return this._c.size}})):(w=y.getConstructor(e,t,g,_),s(w.prototype,n),i.NEED=!0),p(w,t),k[t]=w,o(o.G+o.W+o.F,k),m||y.setStrong(w,t,g),w}},function(t,e,n){"use strict";var r=n(4);t.exports=function(t){r(r.S,t,{of:function(){for(var t=arguments.length,e=new Array(t);t--;)e[t]=arguments[t];return new this(e)}})}},function(t,e,n){"use strict";var r=n(4),o=n(22),i=n(13),u=n(21);t.exports=function(t){r(r.S,t,{from:function(t){var e,n,r,a,s=arguments[1];return o(this),(e=void 0!==s)&&o(s),null==t?new this:(n=[],e?(r=0,a=i(s,arguments[2],2),u(t,!1,(function(t){n.push(a(t,r++))}))):u(t,!1,n.push,n),new this(n))}})}},function(t,e,n){"use strict";var r=n(2),o=n(0);o(e,"__esModule",{value:!0}),o(e,"QiniuErrorName",{enumerable:!0,get:function(){return i.QiniuErrorName}}),o(e,"QiniuError",{enumerable:!0,get:function(){return i.QiniuError}}),o(e,"QiniuRequestError",{enumerable:!0,get:function(){return i.QiniuRequestError}}),o(e,"QiniuNetworkError",{enumerable:!0,get:function(){return i.QiniuNetworkError}}),o(e,"imageMogr2",{enumerable:!0,get:function(){return u.imageMogr2}}),o(e,"watermark",{enumerable:!0,get:function(){return u.watermark}}),o(e,"imageInfo",{enumerable:!0,get:function(){return u.imageInfo}}),o(e,"exif",{enumerable:!0,get:function(){return u.exif}}),o(e,"pipeline",{enumerable:!0,get:function(){return u.pipeline}}),o(e,"deleteUploadedChunks",{enumerable:!0,get:function(){return a.deleteUploadedChunks}}),o(e,"getUploadUrl",{enumerable:!0,get:function(){return a.getUploadUrl}}),o(e,"upload",{enumerable:!0,get:function(){return s.default}}),o(e,"region",{enumerable:!0,get:function(){return c.region}}),o(e,"compressImage",{enumerable:!0,get:function(){return f.compressImage}}),o(e,"urlSafeBase64Encode",{enumerable:!0,get:function(){return f.urlSafeBase64Encode}}),o(e,"urlSafeBase64Decode",{enumerable:!0,get:function(){return f.urlSafeBase64Decode}}),o(e,"getHeadersForMkFile",{enumerable:!0,get:function(){return f.getHeadersForMkFile}}),o(e,"getHeadersForChunkUpload",{enumerable:!0,get:function(){return f.getHeadersForChunkUpload}});var i=n(23),u=n(98),a=n(43),s=r(n(83)),c=n(82),f=n(18)},function(t,e,n){n(89);var r=n(1).Object;t.exports=function(t,e,n){return r.defineProperty(t,e,n)}},function(t,e,n){var r=n(4);r(r.S+r.F*!n(7),"Object",{defineProperty:n(10).f})},function(t,e,n){n(91);var r=n(1).Object;t.exports=function(t,e){return r.create(t,e)}},function(t,e,n){var r=n(4);r(r.S,"Object",{create:n(34)})},function(t,e,n){var r=n(10),o=n(11),i=n(24);t.exports=n(7)?Object.defineProperties:function(t,e){o(t);for(var n,u=i(e),a=u.length,s=0;a>s;)r.f(t,n=u[s++],e[n]);return t}},function(t,e,n){var r=n(17),o=n(48),i=n(94);t.exports=function(t){return function(e,n,u){var a,s=r(e),c=o(s.length),f=i(u,c);if(t&&n!=n){for(;c>f;)if((a=s[f++])!=a)return!0}else for(;c>f;f++)if((t||f in s)&&s[f]===n)return t||f||0;return!t&&-1}}},function(t,e,n){var r=n(49),o=Math.max,i=Math.min;t.exports=function(t,e){return(t=r(t))<0?o(t+e,0):i(t,e)}},function(t,e,n){n(96),t.exports=n(1).Object.setPrototypeOf},function(t,e,n){var r=n(4);r(r.S,"Object",{setPrototypeOf:n(97).set})},function(t,e,n){var r=n(6),o=n(11),i=function(t,e){if(o(t),!r(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,r){try{(r=n(13)(Function.call,n(53).f(Object.prototype,"__proto__").set,2))(t,[]),e=!(t instanceof Array)}catch(t){e=!0}return function(t,n){return i(t,n),e?t.__proto__=n:r(t,n),t}}({},!1):void 0),check:i}},function(t,e,n){"use strict";n(0)(e,"__esModule",{value:!0}),e.imageView2=i,e.imageMogr2=u,e.watermark=a,e.imageInfo=function(t,e){var n=o(t,e)+"?imageInfo";return(0,r.request)(n,{method:"GET"})},e.exif=function(t,e){var n=o(t,e)+"?exif";return(0,r.request)(n,{method:"GET"})},e.pipeline=function(t,e,n){var r,s="[object Array]"===Object.prototype.toString.call(t),c=!1,f="";if(s){for(var l=0,p=t.length;l<p;l++){if(!(r=t[l]).fop)throw"fop can't be empty in pipeline";switch(r.fop){case"watermark":f+=a(r)+"|";break;case"imageView2":f+=i(r)+"|";break;case"imageMogr2":f+=u(r)+"|";break;default:c=!0}if(c)throw"fop is wrong in pipeline"}if(e&&n){var h=(f=o(e,n)+"?"+f).length;"|"===f.slice(h-1)&&(f=f.slice(0,h-1))}return f}throw"pipeline's first param should be array"};var r=n(18);function o(t,e){return t=encodeURIComponent(t),"/"!==e.slice(e.length-1)&&(e+="/"),e+t}function i(t,e,n){if(!/^\d$/.test(String(t.mode)))throw"mode should be number in imageView2";var r=t.mode,i=t.w,u=t.h,a=t.q,s=t.format;if(!i&&!u)throw"param w and h is empty in imageView2";var c="imageView2/"+encodeURIComponent(r);return c+=i?"/w/"+encodeURIComponent(i):"",c+=u?"/h/"+encodeURIComponent(u):"",c+=a?"/q/"+encodeURIComponent(a):"",c+=s?"/format/"+encodeURIComponent(s):"",e&&n&&(c=o(e,n)+"?"+c),c}function u(t,e,n){var r=t["auto-orient"],i=t.thumbnail,u=t.strip,a=t.gravity,s=t.crop,c=t.quality,f=t.rotate,l=t.format,p=t.blur,h="imageMogr2";return h+=r?"/auto-orient":"",h+=i?"/thumbnail/"+encodeURIComponent(i):"",h+=u?"/strip":"",h+=a?"/gravity/"+encodeURIComponent(a):"",h+=c?"/quality/"+encodeURIComponent(c):"",h+=s?"/crop/"+encodeURIComponent(s):"",h+=f?"/rotate/"+encodeURIComponent(f):"",h+=l?"/format/"+encodeURIComponent(l):"",h+=p?"/blur/"+encodeURIComponent(p):"",e&&n&&(h=o(e,n)+"?"+h),h}function a(t,e,n){var i=t.mode;if(!i)throw"mode can't be empty in watermark";var u="watermark/"+i;if(1!==i&&2!==i)throw"mode is wrong";if(1===i){var a=t.image;if(!a)throw"image can't be empty in watermark";u+=a?"/image/"+(0,r.urlSafeBase64Encode)(a):""}if(2===i){var s=t.text,c=t.font,f=t.fontsize,l=t.fill;if(!s)throw"text can't be empty in watermark";u+=s?"/text/"+(0,r.urlSafeBase64Encode)(s):"",u+=c?"/font/"+(0,r.urlSafeBase64Encode)(c):"",u+=f?"/fontsize/"+f:"",u+=l?"/fill/"+(0,r.urlSafeBase64Encode)(l):""}var p=t.dissolve,h=t.gravity,d=t.dx,v=t.dy;return u+=p?"/dissolve/"+encodeURIComponent(p):"",u+=h?"/gravity/"+encodeURIComponent(h):"",u+=d?"/dx/"+encodeURIComponent(d):"",u+=v?"/dy/"+encodeURIComponent(v):"",e&&n&&(u=o(e,n)+"?"+u),u}},function(t,e,n){n(100),t.exports=n(1).Object.keys},function(t,e,n){var r=n(27),o=n(24);n(69)("keys",(function(){return function(t){return o(r(t))}}))},function(t,e,n){"use strict";var r=n(2);n(0)(e,"__esModule",{value:!0}),e.Pool=void 0;var o=r(n(12)),i=function(){function t(t,e){this.runTask=t,this.limit=e,this.aborted=!1,this.queue=[],this.processing=[]}return t.prototype.enqueue=function(t){var e=this;return new o.default((function(n,r){e.queue.push({task:t,resolve:n,reject:r}),e.check()}))},t.prototype.run=function(t){var e=this;this.queue=this.queue.filter((function(e){return e!==t})),this.processing.push(t),this.runTask(t.task).then((function(){e.processing=e.processing.filter((function(e){return e!==t})),t.resolve(),e.check()}),(function(e){return t.reject(e)}))},t.prototype.check=function(){var t=this;if(!this.aborted){var e=this.processing.length,n=this.limit-e;this.queue.slice(0,n).forEach((function(e){t.run(e)}))}},t.prototype.abort=function(){this.queue=[],this.aborted=!0},t}();e.Pool=i},function(t,e,n){n(38),n(54),n(39),n(108),n(116),n(117),t.exports=n(1).Promise},function(t,e,n){var r=n(49),o=n(47);t.exports=function(t){return function(e,n){var i,u,a=String(o(e)),s=r(n),c=a.length;return s<0||s>=c?t?"":void 0:(i=a.charCodeAt(s))<55296||i>56319||s+1===c||(u=a.charCodeAt(s+1))<56320||u>57343?t?a.charAt(s):i:t?a.slice(s,s+2):u-56320+(i-55296<<10)+65536}}},function(t,e,n){"use strict";var r=n(34),o=n(32),i=n(29),u={};n(14)(u,n(5)("iterator"),(function(){return this})),t.exports=function(t,e,n){t.prototype=r(u,{next:o(1,n)}),i(t,e+" Iterator")}},function(t,e,n){var r=n(15),o=n(27),i=n(50)("IE_PROTO"),u=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return t=o(t),r(t,i)?t[i]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?u:null}},function(t,e,n){"use strict";var r=n(107),o=n(70),i=n(28),u=n(17);t.exports=n(55)(Array,"Array",(function(t,e){this._t=u(t),this._i=0,this._k=e}),(function(){var t=this._t,e=this._k,n=this._i++;return!t||n>=t.length?(this._t=void 0,o(1)):o(0,"keys"==e?n:"values"==e?t[n]:[n,t[n]])}),"values"),i.Arguments=i.Array,r("keys"),r("values"),r("entries")},function(t,e){t.exports=function(){}},function(t,e,n){"use strict";var r,o,i,u,a=n(26),s=n(3),c=n(13),f=n(57),l=n(4),p=n(6),h=n(22),d=n(40),v=n(21),y=n(71),g=n(72).set,m=n(113)(),b=n(58),w=n(73),_=n(114),x=n(74),k="Promise",E=s.TypeError,O=s.process,S=O&&O.versions,C=S&&S.v8||"",P=s.Promise,I="process"==f(O),A=function(){},U=o=b.f,M=!!function(){try{var t=P.resolve(1),e=(t.constructor={})[n(5)("species")]=function(t){t(A,A)};return(I||"function"==typeof PromiseRejectionEvent)&&t.then(A)instanceof e&&0!==C.indexOf("6.6")&&-1===_.indexOf("Chrome/66")}catch(t){}}(),j=function(t){var e;return!(!p(t)||"function"!=typeof(e=t.then))&&e},R=function(t,e){if(!t._n){t._n=!0;var n=t._c;m((function(){for(var r=t._v,o=1==t._s,i=0,u=function(e){var n,i,u,a=o?e.ok:e.fail,s=e.resolve,c=e.reject,f=e.domain;try{a?(o||(2==t._h&&T(t),t._h=1),!0===a?n=r:(f&&f.enter(),n=a(r),f&&(f.exit(),u=!0)),n===e.promise?c(E("Promise-chain cycle")):(i=j(n))?i.call(n,s,c):s(n)):c(r)}catch(t){f&&!u&&f.exit(),c(t)}};n.length>i;)u(n[i++]);t._c=[],t._n=!1,e&&!t._h&&F(t)}))}},F=function(t){g.call(s,(function(){var e,n,r,o=t._v,i=L(t);if(i&&(e=w((function(){I?O.emit("unhandledRejection",o,t):(n=s.onunhandledrejection)?n({promise:t,reason:o}):(r=s.console)&&r.error&&r.error("Unhandled promise rejection",o)})),t._h=I||L(t)?2:1),t._a=void 0,i&&e.e)throw e.v}))},L=function(t){return 1!==t._h&&0===(t._a||t._c).length},T=function(t){g.call(s,(function(){var e;I?O.emit("rejectionHandled",t):(e=s.onrejectionhandled)&&e({promise:t,reason:t._v})}))},N=function(t){var e=this;e._d||(e._d=!0,(e=e._w||e)._v=t,e._s=2,e._a||(e._a=e._c.slice()),R(e,!0))},z=function(t){var e,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===t)throw E("Promise can't be resolved itself");(e=j(t))?m((function(){var r={_w:n,_d:!1};try{e.call(t,c(z,r,1),c(N,r,1))}catch(t){N.call(r,t)}})):(n._v=t,n._s=1,R(n,!1))}catch(t){N.call({_w:n,_d:!1},t)}}};M||(P=function(t){d(this,P,k,"_h"),h(t),r.call(this);try{t(c(z,this,1),c(N,this,1))}catch(t){N.call(this,t)}},(r=function(t){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=n(41)(P.prototype,{then:function(t,e){var n=U(y(this,P));return n.ok="function"!=typeof t||t,n.fail="function"==typeof e&&e,n.domain=I?O.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&R(this,!1),n.promise},catch:function(t){return this.then(void 0,t)}}),i=function(){var t=new r;this.promise=t,this.resolve=c(z,t,1),this.reject=c(N,t,1)},b.f=U=function(t){return t===P||t===u?new i(t):o(t)}),l(l.G+l.W+l.F*!M,{Promise:P}),n(29)(P,k),n(75)(k),u=n(1).Promise,l(l.S+l.F*!M,k,{reject:function(t){var e=U(this);return(0,e.reject)(t),e.promise}}),l(l.S+l.F*(a||!M),k,{resolve:function(t){return x(a&&this===u?P:this,t)}}),l(l.S+l.F*!(M&&n(115)((function(t){P.all(t).catch(A)}))),k,{all:function(t){var e=this,n=U(e),r=n.resolve,o=n.reject,i=w((function(){var n=[],i=0,u=1;v(t,!1,(function(t){var a=i++,s=!1;n.push(void 0),u++,e.resolve(t).then((function(t){s||(s=!0,n[a]=t,--u||r(n))}),o)})),--u||r(n)}));return i.e&&o(i.v),n.promise},race:function(t){var e=this,n=U(e),r=n.reject,o=w((function(){v(t,!1,(function(t){e.resolve(t).then(n.resolve,r)}))}));return o.e&&r(o.v),n.promise}})},function(t,e,n){var r=n(11);t.exports=function(t,e,n,o){try{return o?e(r(n)[0],n[1]):e(n)}catch(e){var i=t.return;throw void 0!==i&&r(i.call(t)),e}}},function(t,e,n){var r=n(28),o=n(5)("iterator"),i=Array.prototype;t.exports=function(t){return void 0!==t&&(r.Array===t||i[o]===t)}},function(t,e,n){var r=n(57),o=n(5)("iterator"),i=n(28);t.exports=n(1).getIteratorMethod=function(t){if(null!=t)return t[o]||t["@@iterator"]||i[r(t)]}},function(t,e){t.exports=function(t,e,n){var r=void 0===n;switch(e.length){case 0:return r?t():t.call(n);case 1:return r?t(e[0]):t.call(n,e[0]);case 2:return r?t(e[0],e[1]):t.call(n,e[0],e[1]);case 3:return r?t(e[0],e[1],e[2]):t.call(n,e[0],e[1],e[2]);case 4:return r?t(e[0],e[1],e[2],e[3]):t.call(n,e[0],e[1],e[2],e[3])}return t.apply(n,e)}},function(t,e,n){var r=n(3),o=n(72).set,i=r.MutationObserver||r.WebKitMutationObserver,u=r.process,a=r.Promise,s="process"==n(25)(u);t.exports=function(){var t,e,n,c=function(){var r,o;for(s&&(r=u.domain)&&r.exit();t;){o=t.fn,t=t.next;try{o()}catch(r){throw t?n():e=void 0,r}}e=void 0,r&&r.enter()};if(s)n=function(){u.nextTick(c)};else if(!i||r.navigator&&r.navigator.standalone)if(a&&a.resolve){var f=a.resolve(void 0);n=function(){f.then(c)}}else n=function(){o.call(r,c)};else{var l=!0,p=document.createTextNode("");new i(c).observe(p,{characterData:!0}),n=function(){p.data=l=!l}}return function(r){var o={fn:r,next:void 0};e&&(e.next=o),t||(t=o,n()),e=o}}},function(t,e,n){var r=n(3).navigator;t.exports=r&&r.userAgent||""},function(t,e,n){var r=n(5)("iterator"),o=!1;try{var i=[7][r]();i.return=function(){o=!0},Array.from(i,(function(){throw 2}))}catch(t){}t.exports=function(t,e){if(!e&&!o)return!1;var n=!1;try{var i=[7],u=i[r]();u.next=function(){return{done:n=!0}},i[r]=function(){return u},t(i)}catch(t){}return n}},function(t,e,n){"use strict";var r=n(4),o=n(1),i=n(3),u=n(71),a=n(74);r(r.P+r.R,"Promise",{finally:function(t){var e=u(this,o.Promise||i.Promise),n="function"==typeof t;return this.then(n?function(n){return a(e,t()).then((function(){return n}))}:t,n?function(n){return a(e,t()).then((function(){throw n}))}:t)}})},function(t,e,n){"use strict";var r=n(4),o=n(58),i=n(73);r(r.S,"Promise",{try:function(t){var e=o.f(this),n=i(t);return(n.e?e.reject:e.resolve)(n.v),e.promise}})},function(t,e,n){"use strict";var r=n(2);n(0)(e,"__esModule",{value:!0}),e.Observable=e.Subscriber=void 0;var o,i=r(n(76)),u=r(n(19)),a=r(n(33)),s=r(n(36)),c=(o=function(t,e){return(o=s.default||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?(0,a.default)(e):(n.prototype=e.prototype,new n)}),f=function(){return(f=u.default||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},l=function(t){function e(e,n,r){var o=t.call(this)||this;return o.isStopped=!1,e&&"object"===(0,i.default)(e)?o.destination=e:o.destination=f(f(f({},e&&{next:e}),n&&{error:n}),r&&{complete:r}),o}return c(e,t),e.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this))},e.prototype.next=function(t){!this.isStopped&&this.destination.next&&this.destination.next(t)},e.prototype.error=function(t){!this.isStopped&&this.destination.error&&(this.isStopped=!0,this.destination.error(t))},e.prototype.complete=function(t){!this.isStopped&&this.destination.complete&&(this.isStopped=!0,this.destination.complete(t))},e}(function(){function t(){this.closed=!1}return t.prototype.unsubscribe=function(){this.closed||(this.closed=!0,this._unsubscribe&&this._unsubscribe())},t.prototype.add=function(t){this._unsubscribe=t},t}());e.Subscriber=l;var p=function(){function t(t){this._subscribe=t}return t.prototype.subscribe=function(t,e,n){var r=new l(t,e,n);return r.add(this._subscribe(r)),r},t}();e.Observable=p},function(t,e,n){n(54),n(39),t.exports=n(59).f("iterator")},function(t,e,n){n(77),n(38),n(123),n(124),t.exports=n(1).Symbol},function(t,e,n){var r=n(24),o=n(61),i=n(37);t.exports=function(t){var e=r(t),n=o.f;if(n)for(var u,a=n(t),s=i.f,c=0;a.length>c;)s.call(t,u=a[c++])&&e.push(u);return e}},function(t,e,n){var r=n(17),o=n(78).f,i={}.toString,u="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function(t){return u&&"[object Window]"==i.call(t)?function(t){try{return o(t)}catch(t){return u.slice()}}(t):o(r(t))}},function(t,e,n){n(60)("asyncIterator")},function(t,e,n){n(60)("observable")},function(t,e,n){n(126),t.exports=n(1).Object.assign},function(t,e,n){var r=n(4);r(r.S+r.F,"Object",{assign:n(79)})},function(t,e,n){var r=n(1),o=r.JSON||(r.JSON={stringify:JSON.stringify});t.exports=function(t){return o.stringify.apply(o,arguments)}},function(t,e,n){t.exports=function(t){"use strict";var e=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];function n(t,e){var n=t[0],r=t[1],o=t[2],i=t[3];r=((r+=((o=((o+=((i=((i+=((n=((n+=(r&o|~r&i)+e[0]-680876936|0)<<7|n>>>25)+r|0)&r|~n&o)+e[1]-389564586|0)<<12|i>>>20)+n|0)&n|~i&r)+e[2]+606105819|0)<<17|o>>>15)+i|0)&i|~o&n)+e[3]-1044525330|0)<<22|r>>>10)+o|0,r=((r+=((o=((o+=((i=((i+=((n=((n+=(r&o|~r&i)+e[4]-176418897|0)<<7|n>>>25)+r|0)&r|~n&o)+e[5]+1200080426|0)<<12|i>>>20)+n|0)&n|~i&r)+e[6]-1473231341|0)<<17|o>>>15)+i|0)&i|~o&n)+e[7]-45705983|0)<<22|r>>>10)+o|0,r=((r+=((o=((o+=((i=((i+=((n=((n+=(r&o|~r&i)+e[8]+1770035416|0)<<7|n>>>25)+r|0)&r|~n&o)+e[9]-1958414417|0)<<12|i>>>20)+n|0)&n|~i&r)+e[10]-42063|0)<<17|o>>>15)+i|0)&i|~o&n)+e[11]-1990404162|0)<<22|r>>>10)+o|0,r=((r+=((o=((o+=((i=((i+=((n=((n+=(r&o|~r&i)+e[12]+1804603682|0)<<7|n>>>25)+r|0)&r|~n&o)+e[13]-40341101|0)<<12|i>>>20)+n|0)&n|~i&r)+e[14]-1502002290|0)<<17|o>>>15)+i|0)&i|~o&n)+e[15]+1236535329|0)<<22|r>>>10)+o|0,r=((r+=((o=((o+=((i=((i+=((n=((n+=(r&i|o&~i)+e[1]-165796510|0)<<5|n>>>27)+r|0)&o|r&~o)+e[6]-1069501632|0)<<9|i>>>23)+n|0)&r|n&~r)+e[11]+643717713|0)<<14|o>>>18)+i|0)&n|i&~n)+e[0]-373897302|0)<<20|r>>>12)+o|0,r=((r+=((o=((o+=((i=((i+=((n=((n+=(r&i|o&~i)+e[5]-701558691|0)<<5|n>>>27)+r|0)&o|r&~o)+e[10]+38016083|0)<<9|i>>>23)+n|0)&r|n&~r)+e[15]-660478335|0)<<14|o>>>18)+i|0)&n|i&~n)+e[4]-405537848|0)<<20|r>>>12)+o|0,r=((r+=((o=((o+=((i=((i+=((n=((n+=(r&i|o&~i)+e[9]+568446438|0)<<5|n>>>27)+r|0)&o|r&~o)+e[14]-1019803690|0)<<9|i>>>23)+n|0)&r|n&~r)+e[3]-187363961|0)<<14|o>>>18)+i|0)&n|i&~n)+e[8]+1163531501|0)<<20|r>>>12)+o|0,r=((r+=((o=((o+=((i=((i+=((n=((n+=(r&i|o&~i)+e[13]-1444681467|0)<<5|n>>>27)+r|0)&o|r&~o)+e[2]-51403784|0)<<9|i>>>23)+n|0)&r|n&~r)+e[7]+1735328473|0)<<14|o>>>18)+i|0)&n|i&~n)+e[12]-1926607734|0)<<20|r>>>12)+o|0,r=((r+=((o=((o+=((i=((i+=((n=((n+=(r^o^i)+e[5]-378558|0)<<4|n>>>28)+r|0)^r^o)+e[8]-2022574463|0)<<11|i>>>21)+n|0)^n^r)+e[11]+1839030562|0)<<16|o>>>16)+i|0)^i^n)+e[14]-35309556|0)<<23|r>>>9)+o|0,r=((r+=((o=((o+=((i=((i+=((n=((n+=(r^o^i)+e[1]-1530992060|0)<<4|n>>>28)+r|0)^r^o)+e[4]+1272893353|0)<<11|i>>>21)+n|0)^n^r)+e[7]-155497632|0)<<16|o>>>16)+i|0)^i^n)+e[10]-1094730640|0)<<23|r>>>9)+o|0,r=((r+=((o=((o+=((i=((i+=((n=((n+=(r^o^i)+e[13]+681279174|0)<<4|n>>>28)+r|0)^r^o)+e[0]-358537222|0)<<11|i>>>21)+n|0)^n^r)+e[3]-722521979|0)<<16|o>>>16)+i|0)^i^n)+e[6]+76029189|0)<<23|r>>>9)+o|0,r=((r+=((o=((o+=((i=((i+=((n=((n+=(r^o^i)+e[9]-640364487|0)<<4|n>>>28)+r|0)^r^o)+e[12]-421815835|0)<<11|i>>>21)+n|0)^n^r)+e[15]+530742520|0)<<16|o>>>16)+i|0)^i^n)+e[2]-995338651|0)<<23|r>>>9)+o|0,r=((r+=((i=((i+=(r^((n=((n+=(o^(r|~i))+e[0]-198630844|0)<<6|n>>>26)+r|0)|~o))+e[7]+1126891415|0)<<10|i>>>22)+n|0)^((o=((o+=(n^(i|~r))+e[14]-1416354905|0)<<15|o>>>17)+i|0)|~n))+e[5]-57434055|0)<<21|r>>>11)+o|0,r=((r+=((i=((i+=(r^((n=((n+=(o^(r|~i))+e[12]+1700485571|0)<<6|n>>>26)+r|0)|~o))+e[3]-1894986606|0)<<10|i>>>22)+n|0)^((o=((o+=(n^(i|~r))+e[10]-1051523|0)<<15|o>>>17)+i|0)|~n))+e[1]-2054922799|0)<<21|r>>>11)+o|0,r=((r+=((i=((i+=(r^((n=((n+=(o^(r|~i))+e[8]+1873313359|0)<<6|n>>>26)+r|0)|~o))+e[15]-30611744|0)<<10|i>>>22)+n|0)^((o=((o+=(n^(i|~r))+e[6]-1560198380|0)<<15|o>>>17)+i|0)|~n))+e[13]+1309151649|0)<<21|r>>>11)+o|0,r=((r+=((i=((i+=(r^((n=((n+=(o^(r|~i))+e[4]-145523070|0)<<6|n>>>26)+r|0)|~o))+e[11]-1120210379|0)<<10|i>>>22)+n|0)^((o=((o+=(n^(i|~r))+e[2]+718787259|0)<<15|o>>>17)+i|0)|~n))+e[9]-343485551|0)<<21|r>>>11)+o|0,t[0]=n+t[0]|0,t[1]=r+t[1]|0,t[2]=o+t[2]|0,t[3]=i+t[3]|0}function r(t){var e,n=[];for(e=0;e<64;e+=4)n[e>>2]=t.charCodeAt(e)+(t.charCodeAt(e+1)<<8)+(t.charCodeAt(e+2)<<16)+(t.charCodeAt(e+3)<<24);return n}function o(t){var e,n=[];for(e=0;e<64;e+=4)n[e>>2]=t[e]+(t[e+1]<<8)+(t[e+2]<<16)+(t[e+3]<<24);return n}function i(t){var e,o,i,u,a,s,c=t.length,f=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=c;e+=64)n(f,r(t.substring(e-64,e)));for(o=(t=t.substring(e-64)).length,i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],e=0;e<o;e+=1)i[e>>2]|=t.charCodeAt(e)<<(e%4<<3);if(i[e>>2]|=128<<(e%4<<3),e>55)for(n(f,i),e=0;e<16;e+=1)i[e]=0;return u=(u=8*c).toString(16).match(/(.*?)(.{0,8})$/),a=parseInt(u[2],16),s=parseInt(u[1],16)||0,i[14]=a,i[15]=s,n(f,i),f}function u(t){var e,r,i,u,a,s,c=t.length,f=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=c;e+=64)n(f,o(t.subarray(e-64,e)));for(r=(t=e-64<c?t.subarray(e-64):new Uint8Array(0)).length,i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],e=0;e<r;e+=1)i[e>>2]|=t[e]<<(e%4<<3);if(i[e>>2]|=128<<(e%4<<3),e>55)for(n(f,i),e=0;e<16;e+=1)i[e]=0;return u=(u=8*c).toString(16).match(/(.*?)(.{0,8})$/),a=parseInt(u[2],16),s=parseInt(u[1],16)||0,i[14]=a,i[15]=s,n(f,i),f}function a(t){var n,r="";for(n=0;n<4;n+=1)r+=e[t>>8*n+4&15]+e[t>>8*n&15];return r}function s(t){var e;for(e=0;e<t.length;e+=1)t[e]=a(t[e]);return t.join("")}function c(t){return/[\u0080-\uFFFF]/.test(t)&&(t=unescape(encodeURIComponent(t))),t}function f(t,e){var n,r=t.length,o=new ArrayBuffer(r),i=new Uint8Array(o);for(n=0;n<r;n+=1)i[n]=t.charCodeAt(n);return e?i:o}function l(t){return String.fromCharCode.apply(null,new Uint8Array(t))}function p(t,e,n){var r=new Uint8Array(t.byteLength+e.byteLength);return r.set(new Uint8Array(t)),r.set(new Uint8Array(e),t.byteLength),n?r:r.buffer}function h(t){var e,n=[],r=t.length;for(e=0;e<r-1;e+=2)n.push(parseInt(t.substr(e,2),16));return String.fromCharCode.apply(String,n)}function d(){this.reset()}return s(i("hello")),"undefined"==typeof ArrayBuffer||ArrayBuffer.prototype.slice||function(){function e(t,e){return(t=0|t||0)<0?Math.max(t+e,0):Math.min(t,e)}ArrayBuffer.prototype.slice=function(n,r){var o,i,u,a,s=this.byteLength,c=e(n,s),f=s;return r!==t&&(f=e(r,s)),c>f?new ArrayBuffer(0):(o=f-c,i=new ArrayBuffer(o),u=new Uint8Array(i),a=new Uint8Array(this,c,o),u.set(a),i)}}(),d.prototype.append=function(t){return this.appendBinary(c(t)),this},d.prototype.appendBinary=function(t){this._buff+=t,this._length+=t.length;var e,o=this._buff.length;for(e=64;e<=o;e+=64)n(this._hash,r(this._buff.substring(e-64,e)));return this._buff=this._buff.substring(e-64),this},d.prototype.end=function(t){var e,n,r=this._buff,o=r.length,i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<o;e+=1)i[e>>2]|=r.charCodeAt(e)<<(e%4<<3);return this._finish(i,o),n=s(this._hash),t&&(n=h(n)),this.reset(),n},d.prototype.reset=function(){return this._buff="",this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},d.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash.slice()}},d.prototype.setState=function(t){return this._buff=t.buff,this._length=t.length,this._hash=t.hash,this},d.prototype.destroy=function(){delete this._hash,delete this._buff,delete this._length},d.prototype._finish=function(t,e){var r,o,i,u=e;if(t[u>>2]|=128<<(u%4<<3),u>55)for(n(this._hash,t),u=0;u<16;u+=1)t[u]=0;r=(r=8*this._length).toString(16).match(/(.*?)(.{0,8})$/),o=parseInt(r[2],16),i=parseInt(r[1],16)||0,t[14]=o,t[15]=i,n(this._hash,t)},d.hash=function(t,e){return d.hashBinary(c(t),e)},d.hashBinary=function(t,e){var n=s(i(t));return e?h(n):n},d.ArrayBuffer=function(){this.reset()},d.ArrayBuffer.prototype.append=function(t){var e,r=p(this._buff.buffer,t,!0),i=r.length;for(this._length+=t.byteLength,e=64;e<=i;e+=64)n(this._hash,o(r.subarray(e-64,e)));return this._buff=e-64<i?new Uint8Array(r.buffer.slice(e-64)):new Uint8Array(0),this},d.ArrayBuffer.prototype.end=function(t){var e,n,r=this._buff,o=r.length,i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<o;e+=1)i[e>>2]|=r[e]<<(e%4<<3);return this._finish(i,o),n=s(this._hash),t&&(n=h(n)),this.reset(),n},d.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},d.ArrayBuffer.prototype.getState=function(){var t=d.prototype.getState.call(this);return t.buff=l(t.buff),t},d.ArrayBuffer.prototype.setState=function(t){return t.buff=f(t.buff,!0),d.prototype.setState.call(this,t)},d.ArrayBuffer.prototype.destroy=d.prototype.destroy,d.ArrayBuffer.prototype._finish=d.prototype._finish,d.ArrayBuffer.hash=function(t,e){var n=s(u(new Uint8Array(t)));return e?h(n):n},d}()},function(t,e,n){"use strict";var r=n(2);n(0)(e,"__esModule",{value:!0}),e.normalizeUploadConfig=function(t,e){var n=l({},t),r=n.upprotocol,i=n.uphost,u=p(n,["upprotocol","uphost"]),a=l({uphost:[],retryCount:3,checkByMD5:!1,forceDirect:!1,useCdnDomain:!0,checkByServer:!1,concurrentRequestLimit:3,chunkSize:f.DEFAULT_CHUNK_SIZE,upprotocol:"https",debugLogLevel:"OFF",disableStatisticsReport:!1},u);r&&(a.upprotocol=r.replace(/:$/,""));var s=[];e&&null!=(null==t?void 0:t.uphost)&&null!=(null==t?void 0:t.region)&&e.warn("do not use both the uphost and region config.");if(i)(0,o.default)(i)?s.push.apply(s,d(i)):s.push(i);else if(null==a?void 0:a.region){var h=c.regionUphostMap[null==a?void 0:a.region];a.useCdnDomain?s.push.apply(s,d(h.cdnUphost)):s.push.apply(s,d(h.srcUphost))}return l(l({},a),{uphost:s.filter(Boolean)})};var o=r(n(130)),i=r(n(8)),u=r(n(9)),a=r(n(133)),s=r(n(19)),c=n(82),f=n(83),l=function(){return(l=s.default||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},p=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof a.default){var o=0;for(r=(0,a.default)(t);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]])}return n},h=function(t,e){var n="function"==typeof u.default&&t[i.default];if(!n)return t;var r,o,a=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(o)throw o.error}}return s},d=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(h(arguments[e]));return t}},function(t,e,n){t.exports=n(131)},function(t,e,n){n(132),t.exports=n(1).Array.isArray},function(t,e,n){var r=n(4);r(r.S,"Array",{isArray:n(62)})},function(t,e,n){t.exports=n(134)},function(t,e,n){n(77),t.exports=n(1).Object.getOwnPropertySymbols},function(t,e,n){"use strict";var r;n(0)(e,"__esModule",{value:!0}),e.regionUphostMap=e.region=void 0;var o={z0:"z0",z1:"z1",z2:"z2",na0:"na0",as0:"as0",cnEast2:"cn-east-2"};e.region=o;var i=((r={})[o.z0]={srcUphost:["up.qiniup.com"],cdnUphost:["upload.qiniup.com"]},r[o.z1]={srcUphost:["up-z1.qiniup.com"],cdnUphost:["upload-z1.qiniup.com"]},r[o.z2]={srcUphost:["up-z2.qiniup.com"],cdnUphost:["upload-z2.qiniup.com"]},r[o.na0]={srcUphost:["up-na0.qiniup.com"],cdnUphost:["upload-na0.qiniup.com"]},r[o.as0]={srcUphost:["up-as0.qiniup.com"],cdnUphost:["upload-as0.qiniup.com"]},r[o.cnEast2]={srcUphost:["up-cn-east-2.qiniup.com"],cdnUphost:["upload-cn-east-2.qiniup.com"]},r);e.regionUphostMap=i},function(t,e,n){t.exports=n(137)},function(t,e,n){n(138);var r=n(1).Object;t.exports=function(t,e){return r.getOwnPropertyDescriptor(t,e)}},function(t,e,n){var r=n(17),o=n(53).f;n(69)("getOwnPropertyDescriptor",(function(){return function(t,e){return o(r(t),e)}}))},function(t,e,n){t.exports=n(140)},function(t,e,n){n(38),n(39),n(141),n(145),n(146),t.exports=n(1).WeakMap},function(t,e,n){"use strict";var r,o=n(3),i=n(64)(0),u=n(56),a=n(30),s=n(79),c=n(144),f=n(6),l=n(31),p=n(31),h=!o.ActiveXObject&&"ActiveXObject"in o,d="WeakMap",v=a.getWeak,y=Object.isExtensible,g=c.ufstore,m=function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}},b={get:function(t){if(f(t)){var e=v(t);return!0===e?g(l(this,d)).get(t):e?e[this._i]:void 0}},set:function(t,e){return c.def(l(this,d),t,e)}},w=t.exports=n(84)(d,m,b,c,!0,!0);p&&h&&(s((r=c.getConstructor(m,d)).prototype,b),a.NEED=!0,i(["delete","has","get","set"],(function(t){var e=w.prototype,n=e[t];u(e,t,(function(e,o){if(f(e)&&!y(e)){this._f||(this._f=new r);var i=this._f[t](e,o);return"set"==t?this:i}return n.call(this,e,o)}))})))},function(t,e,n){var r=n(143);t.exports=function(t,e){return new(r(t))(e)}},function(t,e,n){var r=n(6),o=n(62),i=n(5)("species");t.exports=function(t){var e;return o(t)&&("function"!=typeof(e=t.constructor)||e!==Array&&!o(e.prototype)||(e=void 0),r(e)&&null===(e=e[i])&&(e=void 0)),void 0===e?Array:e}},function(t,e,n){"use strict";var r=n(41),o=n(30).getWeak,i=n(11),u=n(6),a=n(40),s=n(21),c=n(64),f=n(15),l=n(31),p=c(5),h=c(6),d=0,v=function(t){return t._l||(t._l=new y)},y=function(){this.a=[]},g=function(t,e){return p(t.a,(function(t){return t[0]===e}))};y.prototype={get:function(t){var e=g(this,t);if(e)return e[1]},has:function(t){return!!g(this,t)},set:function(t,e){var n=g(this,t);n?n[1]=e:this.a.push([t,e])},delete:function(t){var e=h(this.a,(function(e){return e[0]===t}));return~e&&this.a.splice(e,1),!!~e}},t.exports={getConstructor:function(t,e,n,i){var c=t((function(t,r){a(t,c,e,"_i"),t._t=e,t._i=d++,t._l=void 0,null!=r&&s(r,n,t[i],t)}));return r(c.prototype,{delete:function(t){if(!u(t))return!1;var n=o(t);return!0===n?v(l(this,e)).delete(t):n&&f(n,this._i)&&delete n[this._i]},has:function(t){if(!u(t))return!1;var n=o(t);return!0===n?v(l(this,e)).has(t):n&&f(n,this._i)}}),c},def:function(t,e,n){var r=o(i(e),!0);return!0===r?v(t).set(e,n):r[t._i]=n,t},ufstore:v}},function(t,e,n){n(85)("WeakMap")},function(t,e,n){n(86)("WeakMap")},function(t,e,n){"use strict";var r=n(42),o=n(2);n(0)(e,"__esModule",{value:!0}),e.default=void 0;var i,u=o(n(81)),a=o(n(8)),s=o(n(9)),c=o(n(12)),f=o(n(19)),l=o(n(33)),p=o(n(36)),h=n(43),d=n(23),v=r(n(18)),y=o(n(65)),g=(i=function(t,e){return(i=p.default||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}i(t,e),t.prototype=null===e?(0,l.default)(e):(n.prototype=e.prototype,new n)}),m=function(){return(m=f.default||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},b=function(t,e,n,r){return new(n||(n=c.default))((function(o,i){function u(t){try{s(r.next(t))}catch(t){i(t)}}function a(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}s((r=r.apply(t,e||[])).next())}))},w=function(t,e){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof s.default&&(i[a.default]=function(){return this}),i;function c(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=e.call(t,u)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}};var _=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return g(e,t),e.prototype.run=function(){return b(this,void 0,void 0,(function(){var t,e,n,r,o,i=this;return w(this,(function(u){switch(u.label){case 0:if(this.logger.info("start run Resume."),!this.config.chunkSize||(a=this.config.chunkSize,!/^[1-9]\d*$/.test(String(a))))throw new d.QiniuError(d.QiniuErrorName.InvalidChunkSize,"chunkSize must be a positive integer");if(this.config.chunkSize>1024)throw new d.QiniuError(d.QiniuErrorName.InvalidChunkSize,"chunkSize maximum value is 1024");return[4,this.initBeforeUploadChunks()];case 1:u.sent(),t=new v.Pool((function(e){return b(i,void 0,void 0,(function(){return w(this,(function(n){switch(n.label){case 0:if(this.aborted)throw t.abort(),new Error("pool is aborted");return[4,this.uploadChunk(e)];case 1:return n.sent(),[2]}}))}))}),this.config.concurrentRequestLimit),e=null,n=this.getLocalKey(),r=this.chunks.map((function(e,n){return t.enqueue({chunk:e,index:n})})),u.label=2;case 2:return u.trys.push([2,5,,6]),[4,c.default.all(r)];case 3:return u.sent(),[4,this.mkFileReq()];case 4:return e=u.sent(),[3,6];case 5:throw(o=u.sent())instanceof d.QiniuRequestError&&(612===o.code||400===o.code)&&v.removeLocalFileInfo(n,this.logger),o;case 6:return v.removeLocalFileInfo(n,this.logger),[2,e]}var a}))}))},e.prototype.uploadChunk=function(t){return b(this,void 0,void 0,(function(){var e,n,r,o,i,u,a,s,c,f=this;return w(this,(function(l){switch(l.label){case 0:return e=t.index,n=t.chunk,r=this.cachedUploadedList[e],this.logger.info("upload part "+e+", cache:",r),o=this.config.checkByMD5,i=function(){f.usedCacheList[e]=!0,f.updateChunkProgress(n.size,e),f.uploadedList[e]=r,f.updateLocalCache()},r&&!o?(i(),[2]):[4,v.computeMd5(n)];case 1:return u=l.sent(),this.logger.info("computed part md5.",u),r&&u===r.md5?(i(),[2]):(this.usedCacheList[e]=!1,a=function(t){f.updateChunkProgress(t.loaded,e)},s={body:n,md5:this.config.checkByServer?u:void 0,onProgress:a,onCreate:function(t){return f.addXhr(t)}},this.logger.info("part "+e+" start uploading."),[4,(0,h.uploadChunk)(this.token,this.key,t.index+1,this.getUploadInfo(),s)]);case 2:return c=l.sent(),this.logger.info("part "+e+" upload completed."),a({loaded:n.size,total:n.size}),this.uploadedList[e]={etag:c.data.etag,md5:c.data.md5,size:n.size},this.updateLocalCache(),[2]}}))}))},e.prototype.mkFileReq=function(){return b(this,void 0,void 0,(function(){var t,e,n=this;return w(this,(function(r){switch(r.label){case 0:return t=m(m(m({parts:this.uploadedList.map((function(t,e){return{etag:t.etag,partNumber:e+1}})),fname:this.putExtra.fname},this.putExtra.mimeType&&{mimeType:this.putExtra.mimeType}),this.putExtra.customVars&&{customVars:this.putExtra.customVars}),this.putExtra.metadata&&{metadata:this.putExtra.metadata}),this.logger.info("parts upload completed, make file.",t),[4,(0,h.uploadComplete)(this.token,this.key,this.getUploadInfo(),{onCreate:function(t){return n.addXhr(t)},body:(0,u.default)(t)})];case 1:return e=r.sent(),this.logger.info("finish Resume Progress."),this.updateMkFileProgress(1),[2,e]}}))}))},e.prototype.initBeforeUploadChunks=function(){return b(this,void 0,void 0,(function(){var t,e,n;return w(this,(function(r){switch(r.label){case 0:return this.uploadedList=[],this.usedCacheList=[],(t=v.getLocalFileInfo(this.getLocalKey(),this.logger))?[3,2]:(this.logger.info("init upload parts from api."),[4,(0,h.initUploadParts)(this.token,this.bucketName,this.key,this.uploadHost.getUrl())]);case 1:return e=r.sent(),this.logger.info("initd upload parts of id: "+e.data.uploadId+"."),this.uploadId=e.data.uploadId,this.cachedUploadedList=[],[3,3];case 2:n=["resume upload parts from local cache,","total "+t.data.length+" part,","id is "+t.id+"."],this.logger.info(n.join(" ")),this.cachedUploadedList=t.data,this.uploadId=t.id,r.label=3;case 3:return this.chunks=v.getChunks(this.file,this.config.chunkSize),this.loaded={mkFileProgress:0,chunks:this.chunks.map((function(t){return 0}))},this.notifyResumeProgress(),[2]}}))}))},e.prototype.getUploadInfo=function(){return{id:this.uploadId,url:this.uploadHost.getUrl()}},e.prototype.getLocalKey=function(){return v.createLocalKey(this.file.name,this.key,this.file.size)},e.prototype.updateLocalCache=function(){v.setLocalFileInfo(this.getLocalKey(),{id:this.uploadId,data:this.uploadedList},this.logger)},e.prototype.updateChunkProgress=function(t,e){this.loaded.chunks[e]=t,this.notifyResumeProgress()},e.prototype.updateMkFileProgress=function(t){this.loaded.mkFileProgress=t,this.notifyResumeProgress()},e.prototype.notifyResumeProgress=function(){var t=this;this.progress={total:this.getProgressInfoItem(v.sum(this.loaded.chunks)+this.loaded.mkFileProgress,this.file.size+1),chunks:this.chunks.map((function(e,n){var r=t.usedCacheList[n];return t.getProgressInfoItem(t.loaded.chunks[n],e.size,r)})),uploadInfo:{id:this.uploadId,url:this.uploadHost.getUrl()}},this.onData(this.progress)},e}(y.default);e.default=_},function(t,e,n){"use strict";e.decode=e.parse=n(149),e.encode=e.stringify=n(150)},function(t,e,n){"use strict";function r(t,e){return Object.prototype.hasOwnProperty.call(t,e)}t.exports=function(t,e,n,i){e=e||"&",n=n||"=";var u={};if("string"!=typeof t||0===t.length)return u;var a=/\+/g;t=t.split(e);var s=1e3;i&&"number"==typeof i.maxKeys&&(s=i.maxKeys);var c=t.length;s>0&&c>s&&(c=s);for(var f=0;f<c;++f){var l,p,h,d,v=t[f].replace(a,"%20"),y=v.indexOf(n);y>=0?(l=v.substr(0,y),p=v.substr(y+1)):(l=v,p=""),h=decodeURIComponent(l),d=decodeURIComponent(p),r(u,h)?o(u[h])?u[h].push(d):u[h]=[u[h],d]:u[h]=d}return u};var o=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)}},function(t,e,n){"use strict";var r=function(t){switch(typeof t){case"string":return t;case"boolean":return t?"true":"false";case"number":return isFinite(t)?t:"";default:return""}};t.exports=function(t,e,n,a){return e=e||"&",n=n||"=",null===t&&(t=void 0),"object"==typeof t?i(u(t),(function(u){var a=encodeURIComponent(r(u))+n;return o(t[u])?i(t[u],(function(t){return a+encodeURIComponent(r(t))})).join(e):a+encodeURIComponent(r(t[u]))})).join(e):a?encodeURIComponent(r(a))+n+encodeURIComponent(r(t)):""};var o=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)};function i(t,e){if(t.map)return t.map(e);for(var n=[],r=0;r<t.length;r++)n.push(e(t[r],r));return n}var u=Object.keys||function(t){var e=[];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.push(n);return e}},function(t,e,n){"use strict";var r=n(2);n(0)(e,"__esModule",{value:!0}),e.default=void 0;var o,i=r(n(20)),u=r(n(8)),a=r(n(9)),s=r(n(12)),c=r(n(33)),f=r(n(36)),l=n(152),p=n(43),h=r(n(65)),d=(o=function(t,e){return(o=f.default||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?(0,c.default)(e):(n.prototype=e.prototype,new n)}),v=function(t,e,n,r){return new(n||(n=s.default))((function(o,i){function u(t){try{s(r.next(t))}catch(t){i(t)}}function a(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}s((r=r.apply(t,e||[])).next())}))},y=function(t,e){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:c(0),throw:c(1),return:c(2)},"function"==typeof a.default&&(i[u.default]=function(){return this}),i;function c(i){return function(u){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=e.call(t,s)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},g=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return d(e,t),e.prototype.run=function(){return v(this,void 0,void 0,(function(){var t,e,n,r,o,u=this;return y(this,(function(a){switch(a.label){case 0:return this.logger.info("start run Direct."),(t=new FormData).append("file",this.file),t.append("token",this.token),null!=this.key&&t.append("key",this.key),t.append("fname",this.putExtra.fname),this.config.checkByServer?[4,l.CRC32.file(this.file)]:[3,2];case 1:e=a.sent(),t.append("crc32",e.toString()),a.label=2;case 2:return this.putExtra.customVars&&(this.logger.info("init customVars."),n=this.putExtra.customVars,(0,i.default)(n).forEach((function(e){return t.append(e,n[e].toString())})),this.logger.info("customVars inited.")),this.putExtra.metadata&&(this.logger.info("init metadata."),r=this.putExtra.metadata,(0,i.default)(r).forEach((function(e){return t.append(e,r[e].toString())}))),this.logger.info("formData inited."),[4,(0,p.direct)(this.uploadHost.getUrl(),t,{onProgress:function(t){u.updateDirectProgress(t.loaded,t.total)},onCreate:function(t){return u.addXhr(t)}})];case 3:return o=a.sent(),this.logger.info("Direct progress finish."),this.finishDirectProgress(),[2,o]}}))}))},e.prototype.updateDirectProgress=function(t,e){this.progress={total:this.getProgressInfoItem(t,e+1)},this.onData(this.progress)},e.prototype.finishDirectProgress=function(){if(!this.progress)return this.logger.warn("progress is null."),this.progress={total:this.getProgressInfoItem(this.file.size,this.file.size)},void this.onData(this.progress);var t=this.progress.total;this.progress={total:this.getProgressInfoItem(t.loaded+1,t.size)},this.onData(this.progress)},e}(h.default);e.default=g},function(t,e,n){"use strict";var r=n(2);n(0)(e,"__esModule",{value:!0}),e.CRC32=void 0;var o=r(n(8)),i=r(n(9)),u=r(n(12)),a=n(63),s=function(t,e,n,r){return new(n||(n=u.default))((function(o,i){function u(t){try{s(r.next(t))}catch(t){i(t)}}function a(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}s((r=r.apply(t,e||[])).next())}))},c=function(t,e){var n,r,u,a,s={label:0,sent:function(){if(1&u[0])throw u[1];return u[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof i.default&&(a[o.default]=function(){return this}),a;function c(o){return function(i){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(u=2&o[0]?r.return:o[0]?r.throw||((u=r.return)&&u.call(r),0):r.next)&&!(u=u.call(r,o[1])).done)return u;switch(r=0,u&&(o=[2&o[0],u.value]),o[0]){case 0:case 1:u=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,r=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(u=s.trys,(u=u.length>0&&u[u.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!u||o[1]>u[0]&&o[1]<u[3])){s.label=o[1];break}if(6===o[0]&&s.label<u[1]){s.label=u[1],u=o;break}if(u&&s.label<u[2]){s.label=u[2],s.ops.push(o);break}u[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],r=0}finally{n=u=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,i])}}},f=function(){function t(){this.crc=-1,this.table=this.makeTable()}return t.prototype.makeTable=function(){for(var t=new Array,e=0;e<256;e++){for(var n=e,r=0;r<8;r++)1&n?n=n>>>1^3988292384:n>>>=1;t[e]=n}return t},t.prototype.append=function(t){for(var e=this.crc,n=0;n<t.byteLength;n++)e=e>>>8^this.table[255&(e^t[n])];this.crc=e},t.prototype.compute=function(){return(-1^this.crc)>>>0},t.prototype.readAsUint8Array=function(t){return s(this,void 0,void 0,(function(){var e;return c(this,(function(n){switch(n.label){case 0:return"function"!=typeof t.arrayBuffer?[3,2]:(e=Uint8Array.bind,[4,t.arrayBuffer()]);case 1:return[2,new(e.apply(Uint8Array,[void 0,n.sent()]))];case 2:return[2,new u.default((function(e,n){var r=new FileReader;r.onload=function(){null!=r.result&&"string"!=typeof r.result?e(new Uint8Array(r.result)):n()},r.readAsArrayBuffer(t)}))]}}))}))},t.prototype.file=function(t){return s(this,void 0,void 0,(function(){var e,n,r,o,i,u;return c(this,(function(s){switch(s.label){case 0:return t.size<=a.MB?(e=this.append,[4,this.readAsUint8Array(t)]):[3,2];case 1:return e.apply(this,[s.sent()]),[2,this.compute()];case 2:n=Math.ceil(t.size/a.MB),r=0,s.label=3;case 3:return r<n?(o=r*a.MB,i=r===n-1?t.size:o+a.MB,[4,this.readAsUint8Array(t.slice(o,i))]):[3,6];case 4:u=s.sent(),this.append(new Uint8Array(u)),s.label=5;case 5:return r++,[3,3];case 6:return[2,this.compute()]}}))}))},t.file=function(e){return(new t).file(e)},t}();e.CRC32=f},function(t,e,n){"use strict";var r=n(2);n(0)(e,"__esModule",{value:!0}),e.default=void 0;var o=r(n(8)),i=r(n(9)),u=n(154),a=function(t,e){var n="function"==typeof i.default&&t[o.default];if(!n)return t;var r,u,a=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(t){u={error:t}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(u)throw u.error}}return s},s=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(a(arguments[e]));return t},c=function(){function t(e,n,r,o){void 0===n&&(n=!0),void 0===r&&(r="OFF"),void 0===o&&(o="UPLOAD"),this.token=e,this.disableReport=n,this.level=r,this.prefix=o,this.id=++t.id}return t.prototype.getPrintPrefix=function(t){return"Qiniu-JS-SDK ["+t+"]["+this.prefix+"#"+this.id+"]:"},t.prototype.report=function(t,e){if(!this.disableReport)try{(0,u.reportV3)(this.token,t,e)}catch(t){this.warn(t)}},t.prototype.info=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=["INFO"];n.includes(this.level)&&console.log.apply(console,s([this.getPrintPrefix("INFO")],t))},t.prototype.warn=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=["INFO","WARN"];n.includes(this.level)&&console.warn.apply(console,s([this.getPrintPrefix("WARN")],t))},t.prototype.error=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=["INFO","WARN","ERROR"];n.includes(this.level)&&console.error.apply(console,s([this.getPrintPrefix("ERROR")],t))},t.id=0,t}();e.default=c},function(t,e,n){"use strict";n(0)(e,"__esModule",{value:!0}),e.reportV3=function t(e,n,o){void 0===o&&(o=3);var i=(0,r.createXHR)();i.open("POST","https://uplog.qbox.me/log/3"),i.setRequestHeader("Content-type","application/x-www-form-urlencoded"),i.setRequestHeader("Authorization",(0,r.getAuthHeaders)(e).Authorization),i.onreadystatechange=function(){4===i.readyState&&200!==i.status&&o>0&&t(e,n,o-1)};var u=[n.code||"",n.reqId||"",n.host||"",n.remoteIp||"",n.port||"",n.duration||"",n.time||"",n.bytesSent||"",n.upType||"",n.size||""].join(",");i.send(u)};var r=n(18)},function(t,e,n){"use strict";var r=n(2);n(0)(e,"__esModule",{value:!0}),e.HostPool=e.Host=void 0;var o=r(n(156)),i=r(n(8)),u=r(n(9)),a=r(n(12)),s=n(43),c=function(t,e,n,r){return new(n||(n=a.default))((function(o,i){function u(t){try{s(r.next(t))}catch(t){i(t)}}function a(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}s((r=r.apply(t,e||[])).next())}))},f=function(t,e){var n,r,o,a,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof u.default&&(a[i.default]=function(){return this}),a;function c(i){return function(u){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=e.call(t,s)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},l=function(t,e){var n="function"==typeof u.default&&t[i.default];if(!n)return t;var r,o,a=n.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(r=a.next()).done;)s.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(o)throw o.error}}return s},p=function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(l(arguments[e]));return t},h=new o.default,d=function(){function t(t,e){this.host=t,this.protocol=e}return t.prototype.isFrozen=function(){var t=(new Date).getTime(),e=h.get(this.host);return null!=e&&e>=t},t.prototype.freeze=function(t){void 0===t&&(t=20);var e=(new Date).getTime()+1e3*t;h.set(this.host,e)},t.prototype.unfreeze=function(){h.delete(this.host)},t.prototype.getUrl=function(){return this.protocol+"://"+this.host},t.prototype.getUnfreezeTime=function(){return h.get(this.host)},t}();e.Host=d;var v=function(){function t(t){void 0===t&&(t=[]),this.initHosts=t,this.cachedHostsMap=new o.default}return t.prototype.register=function(t,e,n,r){this.cachedHostsMap.set(t+"@"+e,n.map((function(t){return new d(t,r)})))},t.prototype.refresh=function(t,e,n){var r,o,i,u;return c(this,void 0,void 0,(function(){var a,c;return f(this,(function(f){switch(f.label){case 0:return(this.cachedHostsMap.get(t+"@"+e)||[]).length>0?[2]:this.initHosts.length>0?(this.register(t,e,this.initHosts,n),[2]):[4,(0,s.getUpHosts)(t,e,n)];case 1:return null!=(null==(a=f.sent())?void 0:a.data)&&(c=p((null===(o=null===(r=a.data.up)||void 0===r?void 0:r.acc)||void 0===o?void 0:o.main)||[],(null===(u=null===(i=a.data.up)||void 0===i?void 0:i.acc)||void 0===u?void 0:u.backup)||[]),this.register(t,e,c,n)),[2]}}))}))},t.prototype.getUp=function(t,e,n){return c(this,void 0,void 0,(function(){var r,o;return f(this,(function(i){switch(i.label){case 0:return[4,this.refresh(t,e,n)];case 1:return i.sent(),0===(r=this.cachedHostsMap.get(t+"@"+e)||[]).length?[2,null]:(o=r.filter((function(t){return!t.isFrozen()}))).length>0?[2,o[0]]:[2,r.slice().sort((function(t,e){return(t.getUnfreezeTime()||0)-(e.getUnfreezeTime()||0)}))[0]]}}))}))},t}();e.HostPool=v},function(t,e,n){t.exports=n(157)},function(t,e,n){n(38),n(54),n(39),n(158),n(160),n(163),n(164),t.exports=n(1).Map},function(t,e,n){"use strict";var r=n(159),o=n(31),i="Map";t.exports=n(84)(i,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{get:function(t){var e=r.getEntry(o(this,i),t);return e&&e.v},set:function(t,e){return r.def(o(this,i),0===t?0:t,e)}},r,!0)},function(t,e,n){"use strict";var r=n(10).f,o=n(34),i=n(41),u=n(13),a=n(40),s=n(21),c=n(55),f=n(70),l=n(75),p=n(7),h=n(30).fastKey,d=n(31),v=p?"_s":"size",y=function(t,e){var n,r=h(e);if("F"!==r)return t._i[r];for(n=t._f;n;n=n.n)if(n.k==e)return n};t.exports={getConstructor:function(t,e,n,c){var f=t((function(t,r){a(t,f,e,"_i"),t._t=e,t._i=o(null),t._f=void 0,t._l=void 0,t[v]=0,null!=r&&s(r,n,t[c],t)}));return i(f.prototype,{clear:function(){for(var t=d(this,e),n=t._i,r=t._f;r;r=r.n)r.r=!0,r.p&&(r.p=r.p.n=void 0),delete n[r.i];t._f=t._l=void 0,t[v]=0},delete:function(t){var n=d(this,e),r=y(n,t);if(r){var o=r.n,i=r.p;delete n._i[r.i],r.r=!0,i&&(i.n=o),o&&(o.p=i),n._f==r&&(n._f=o),n._l==r&&(n._l=i),n[v]--}return!!r},forEach:function(t){d(this,e);for(var n,r=u(t,arguments.length>1?arguments[1]:void 0,3);n=n?n.n:this._f;)for(r(n.v,n.k,this);n&&n.r;)n=n.p},has:function(t){return!!y(d(this,e),t)}}),p&&r(f.prototype,"size",{get:function(){return d(this,e)[v]}}),f},def:function(t,e,n){var r,o,i=y(t,e);return i?i.v=n:(t._l=i={i:o=h(e,!0),k:e,v:n,p:r=t._l,n:void 0,r:!1},t._f||(t._f=i),r&&(r.n=i),t[v]++,"F"!==o&&(t._i[o]=i)),t},getEntry:y,setStrong:function(t,e,n){c(t,e,(function(t,n){this._t=d(t,e),this._k=n,this._l=void 0}),(function(){for(var t=this,e=t._k,n=t._l;n&&n.r;)n=n.p;return t._t&&(t._l=n=n?n.n:t._t._f)?f(0,"keys"==e?n.k:"values"==e?n.v:[n.k,n.v]):(t._t=void 0,f(1))}),n?"entries":"values",!n,!0),l(e)}}},function(t,e,n){var r=n(4);r(r.P+r.R,"Map",{toJSON:n(161)("Map")})},function(t,e,n){var r=n(57),o=n(162);t.exports=function(t){return function(){if(r(this)!=t)throw TypeError(t+"#toJSON isn't generic");return o(this)}}},function(t,e,n){var r=n(21);t.exports=function(t,e){var n=[];return r(t,!1,n.push,n,e),n}},function(t,e,n){n(85)("Map")},function(t,e,n){n(86)("Map")},function(t,e,n){"use strict";var r=n(2);n(0)(e,"__esModule",{value:!0}),e.default=void 0;var o=r(n(20)),i=r(n(8)),u=r(n(9)),a=r(n(12)),s=r(n(19)),c=n(23),f=n(63),l=function(){return(l=s.default||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},p=function(t,e,n,r){return new(n||(n=a.default))((function(o,i){function u(t){try{s(r.next(t))}catch(t){i(t)}}function a(t){try{s(r.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}s((r=r.apply(t,e||[])).next())}))},h=function(t,e){var n,r,o,a,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof u.default&&(a[i.default]=function(){return this}),a;function c(i){return function(u){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=e.call(t,s)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},d={PNG:"image/png",JPEG:"image/jpeg",WEBP:"image/webp",BMP:"image/bmp"},v=Math.log(2),y=(0,o.default)(d).map((function(t){return d[t]})),g=d.JPEG;var m=function(){function t(t,e){this.file=t,this.config=e,this.config=l({quality:.92,noCompressIfLarger:!1},this.config)}return t.prototype.process=function(){return p(this,void 0,void 0,(function(){var t,e,n,r,o,i;return h(this,(function(u){switch(u.label){case 0:if(this.outputType=this.file.type,t={},a=this.file.type,!y.includes(a))throw new c.QiniuError(c.QiniuErrorName.UnsupportedFileType,"unsupported file type: "+this.file.type);return[4,this.getOriginImage()];case 1:return e=u.sent(),[4,this.getCanvas(e)];case 2:return n=u.sent(),r=1,this.config.maxWidth&&(r=Math.min(1,this.config.maxWidth/n.width)),this.config.maxHeight&&(r=Math.min(1,r,this.config.maxHeight/n.height)),t.width=n.width,t.height=n.height,[4,this.doScale(n,r)];case 3:return o=u.sent(),(i=this.toBlob(o)).size>this.file.size&&this.config.noCompressIfLarger?[2,{dist:this.file,width:t.width,height:t.height}]:[2,{dist:i,width:o.width,height:o.height}]}var a}))}))},t.prototype.clear=function(t,e,n){this.outputType===g?(t.fillStyle="#fff",t.fillRect(0,0,e,n)):t.clearRect(0,0,e,n)},t.prototype.getOriginImage=function(){var t=this;return new a.default((function(e,n){var r=(0,f.createObjectURL)(t.file),o=new Image;o.onload=function(){e(o)},o.onerror=function(){n("image load error")},o.src=r}))},t.prototype.getCanvas=function(t){var e=this;return new a.default((function(n,r){var o=document.createElement("canvas"),i=o.getContext("2d");if(i){var u=t.width,a=t.height;o.height=a,o.width=u,e.clear(i,u,a),i.drawImage(t,0,0),n(o)}else r(new c.QiniuError(c.QiniuErrorName.GetCanvasContextFailed,"context is null"))}))},t.prototype.doScale=function(t,e){return p(this,void 0,void 0,(function(){var n,r,o,i,u,a,s,f,l,p,d,y,g,m,b,w;return h(this,(function(h){if(1===e)return[2,t];if(n=t.getContext("2d"),r=Math.min(4,Math.ceil(1/e/v)),o=Math.pow(e,1/r),i=document.createElement("canvas"),u=i.getContext("2d"),a=t.width,s=t.height,f=a,l=s,i.width=a,i.height=s,!u||!n)throw new c.QiniuError(c.QiniuErrorName.GetCanvasContextFailed,"mctx or sctx can't be null");for(y=0;y<r;y++)g=a*o|0,m=s*o|0,y===r-1&&(g=f*e,m=l*e),y%2==0?(p=t,d=u):(p=i,d=n),this.clear(d,a,s),d.drawImage(p,0,0,a,s,0,0,g,m),a=g,s=m;return b=p===t?i:t,w=d.getImageData(0,0,a,s),b.width=a,b.height=s,d.putImageData(w,0,0),[2,b]}))}))},t.prototype.toBlob=function(t){var e=t.toDataURL(this.outputType,this.config.quality),n=atob(e.split(",")[1]).split("").map((function(t){return t.charCodeAt(0)}));return new Blob([new Uint8Array(n)],{type:this.outputType})},t}(),b=function(t,e){return new m(t,e).process()};e.default=b}])}));
//# sourceMappingURL=qiniu.min.js.map