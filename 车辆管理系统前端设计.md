# 车辆管理系统前端设计

## 1. 项目结构

### 1.1 目录结构
```
src/views/vehicle/
├── components/                 # 公共组件
│   ├── VehicleSelector.vue    # 车辆选择器
│   ├── StatusTag.vue          # 状态标签
│   ├── PriorityTag.vue        # 优先级标签
│   └── WorkShiftInput.vue     # 台班输入组件
├── vehicleInfo/               # 车辆信息管理
│   ├── index.vue             # 车辆列表页
│   ├── Detail.vue            # 车辆详情页
│   ├── Form.vue              # 车辆表单页
│   ├── ImportModal.vue       # 导入弹窗
│   └── helper/
│       └── index.ts          # 辅助函数
├── application/               # 用车申请管理
│   ├── index.vue             # 申请列表页
│   ├── Form.vue              # 申请表单页
│   ├── Detail.vue            # 申请详情页
│   ├── ReviewModal.vue       # 审核弹窗
│   └── helper/
│       └── index.ts
├── dashboard/                 # 车辆看板
│   ├── index.vue             # 看板主页
│   ├── StatusBoard.vue       # 状态看板
│   ├── ScheduleBoard.vue     # 计划看板
│   └── components/
│       ├── VehicleCard.vue   # 车辆卡片
│       └── ScheduleItem.vue  # 计划项
├── workshift/                # 台班管理
│   ├── index.vue             # 台班统计页
│   ├── Statistics.vue        # 统计分析页
│   └── components/
│       ├── StatChart.vue     # 统计图表
│       └── ExportModal.vue   # 导出弹窗
└── api/                      # API接口
    ├── vehicleInfo.ts
    ├── application.ts
    ├── dashboard.ts
    └── workshift.ts
```

### 1.2 路由配置
```typescript
// src/router/routes/vehicle.ts
export const vehicleRoutes = [
  {
    path: '/vehicle',
    name: 'Vehicle',
    component: () => import('@/layouts/default/index.vue'),
    meta: { title: '车辆管理', icon: 'ant-design:car-outlined' },
    children: [
      {
        path: 'info',
        name: 'VehicleInfo',
        component: () => import('@/views/vehicle/vehicleInfo/index.vue'),
        meta: { title: '车辆信息', permission: 'vehicle:info:view' }
      },
      {
        path: 'application',
        name: 'VehicleApplication',
        component: () => import('@/views/vehicle/application/index.vue'),
        meta: { title: '用车申请', permission: 'vehicle:application:view' }
      },
      {
        path: 'dashboard',
        name: 'VehicleDashboard',
        component: () => import('@/views/vehicle/dashboard/index.vue'),
        meta: { title: '车辆看板', permission: 'vehicle:dashboard:view' }
      },
      {
        path: 'workshift',
        name: 'VehicleWorkshift',
        component: () => import('@/views/vehicle/workshift/index.vue'),
        meta: { title: '台班管理', permission: 'vehicle:workshift:view' }
      }
    ]
  }
];
```

## 2. 核心页面设计

### 2.1 车辆信息管理页面

#### 2.1.1 页面布局
```vue
<template>
  <div class="vehicle-info-page">
    <!-- 搜索区域 -->
    <div class="search-form">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="车牌号">
          <a-input v-model:value="searchForm.vehicleNumber" placeholder="请输入车牌号" />
        </a-form-item>
        <a-form-item label="车辆型号">
          <a-input v-model:value="searchForm.vehicleModel" placeholder="请输入车辆型号" />
        </a-form-item>
        <a-form-item label="使用状态">
          <a-select v-model:value="searchForm.status" placeholder="请选择状态" allowClear>
            <a-select-option value="1">可用</a-select-option>
            <a-select-option value="2">维修中</a-select-option>
            <a-select-option value="3">报废</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <a-button type="primary" @click="handleAdd" v-auth="'vehicle:info:add'">
        <template #icon><PlusOutlined /></template>
        新增车辆
      </a-button>
      <a-button @click="handleImport" v-auth="'vehicle:info:import'">
        <template #icon><ImportOutlined /></template>
        批量导入
      </a-button>
      <a-button @click="handleExport" v-auth="'vehicle:info:export'">
        <template #icon><ExportOutlined /></template>
        导出数据
      </a-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <StatusTag :status="record.status" type="vehicle" />
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">查看</a-button>
              <a-button type="link" size="small" @click="handleEdit(record)" v-auth="'vehicle:info:edit'">编辑</a-button>
              <a-popconfirm title="确定删除吗？" @confirm="handleDelete(record)" v-auth="'vehicle:info:delete'">
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 表单弹窗 -->
    <VehicleForm
      v-model:visible="formVisible"
      :form-data="formData"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 导入弹窗 -->
    <ImportModal
      v-model:visible="importVisible"
      @success="handleImportSuccess"
    />
  </div>
</template>
```

#### 2.1.2 表格列配置
```typescript
const columns = [
  {
    title: '车牌号',
    dataIndex: 'vehicleNumber',
    key: 'vehicleNumber',
    width: 120,
    fixed: 'left'
  },
  {
    title: '车辆型号',
    dataIndex: 'vehicleModel',
    key: 'vehicleModel',
    width: 150
  },
  {
    title: '品牌',
    dataIndex: 'vehicleBrand',
    key: 'vehicleBrand',
    width: 120
  },
  {
    title: '吨位',
    dataIndex: 'tonnage',
    key: 'tonnage',
    width: 80,
    customRender: ({ text }) => text ? `${text}吨` : '-'
  },
  {
    title: '尺寸(长×宽×高)',
    key: 'dimensions',
    width: 150,
    customRender: ({ record }) => {
      const { length, width, height } = record;
      return length && width && height ? `${length}×${width}×${height}m` : '-';
    }
  },
  {
    title: '使用状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '所属队部',
    dataIndex: 'departmentName',
    key: 'departmentName',
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm')
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
];
```

### 2.2 用车申请页面

#### 2.2.1 申请表单
```vue
<template>
  <div class="application-form">
    <a-form
      :model="formData"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      @finish="handleSubmit"
    >
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="用车日期" name="useDate">
            <a-date-picker
              v-model:value="formData.useDate"
              :disabled-date="disabledDate"
              placeholder="请选择用车日期"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="预计时长" name="estimatedDuration">
            <a-input-number
              v-model:value="formData.estimatedDuration"
              :min="0.5"
              :max="24"
              :step="0.5"
              placeholder="请输入预计使用时长"
              style="width: 100%"
            >
              <template #addonAfter>小时</template>
            </a-input-number>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="优先级" name="priority">
            <a-radio-group v-model:value="formData.priority">
              <a-radio :value="1">一般</a-radio>
              <a-radio :value="2">紧急</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="用车目的" name="usePurpose">
        <a-textarea
          v-model:value="formData.usePurpose"
          :rows="4"
          placeholder="请详细描述用车目的"
        />
      </a-form-item>

      <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
        <a-space>
          <a-button type="primary" html-type="submit" :loading="submitting">提交申请</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>
```

### 2.3 车辆看板页面

#### 2.3.1 状态看板
```vue
<template>
  <div class="vehicle-dashboard">
    <!-- 统计卡片 -->
    <a-row :gutter="16" class="stats-cards">
      <a-col :span="6">
        <a-card>
          <a-statistic title="总车辆数" :value="stats.totalVehicles" />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic title="可用车辆" :value="stats.availableVehicles" value-style="color: #52c41a" />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic title="使用中" :value="stats.inUseVehicles" value-style="color: #1890ff" />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic title="维修中" :value="stats.maintenanceVehicles" value-style="color: #ff4d4f" />
        </a-card>
      </a-col>
    </a-row>

    <!-- 日期切换 -->
    <div class="date-selector">
      <a-radio-group v-model:value="selectedDate" @change="handleDateChange">
        <a-radio-button value="today">今天</a-radio-button>
        <a-radio-button value="tomorrow">明天</a-radio-button>
      </a-radio-group>
      <a-date-picker v-model:value="customDate" @change="handleCustomDateChange" />
    </div>

    <!-- 车辆状态网格 -->
    <div class="vehicle-grid">
      <a-row :gutter="[16, 16]">
        <a-col :span="6" v-for="vehicle in vehicleList" :key="vehicle.id">
          <VehicleCard
            :vehicle="vehicle"
            :editable="selectedDate === 'tomorrow'"
            @edit="handleEditSchedule"
          />
        </a-col>
      </a-row>
    </div>
  </div>
</template>
```

#### 2.3.2 车辆卡片组件
```vue
<template>
  <div class="vehicle-card" :class="statusClass">
    <a-card size="small">
      <template #title>
        <div class="card-title">
          <span class="vehicle-number">{{ vehicle.vehicleNumber }}</span>
          <StatusTag :status="vehicle.currentStatus" />
        </div>
      </template>
      
      <template #extra v-if="editable">
        <a-button type="link" size="small" @click="handleEdit">编辑</a-button>
      </template>

      <div class="card-content">
        <p class="vehicle-model">{{ vehicle.vehicleModel }}</p>
        <div class="usage-info" v-if="vehicle.currentUser">
          <p><strong>使用人：</strong>{{ vehicle.currentUser }}</p>
          <p><strong>用途：</strong>{{ vehicle.usePurpose }}</p>
          <p><strong>时间：</strong>{{ vehicle.startTime }} - {{ vehicle.endTime }}</p>
        </div>
        <div class="empty-info" v-else>
          <p class="empty-text">暂无安排</p>
        </div>
      </div>
    </a-card>
  </div>
</template>
```

### 2.4 台班统计页面

#### 2.4.1 统计图表
```vue
<template>
  <div class="workshift-statistics">
    <!-- 筛选条件 -->
    <div class="filter-form">
      <a-form layout="inline" :model="filterForm">
        <a-form-item label="统计时间">
          <a-range-picker v-model:value="filterForm.dateRange" />
        </a-form-item>
        <a-form-item label="统计维度">
          <a-select v-model:value="filterForm.groupBy" style="width: 120px">
            <a-select-option value="vehicle">按车辆</a-select-option>
            <a-select-option value="department">按部门</a-select-option>
            <a-select-option value="date">按日期</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleQuery">查询</a-button>
          <a-button @click="handleExport">导出</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 图表区域 -->
    <a-row :gutter="16">
      <a-col :span="12">
        <a-card title="台班时长统计">
          <StatChart type="bar" :data="chartData.workShiftHours" />
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="使用率统计">
          <StatChart type="line" :data="chartData.utilizationRate" />
        </a-card>
      </a-col>
    </a-row>

    <!-- 详细数据表格 -->
    <a-card title="详细数据" style="margin-top: 16px">
      <a-table
        :columns="tableColumns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
      />
    </a-card>
  </div>
</template>
```

## 3. 组件设计

### 3.1 状态标签组件
```vue
<template>
  <a-tag :color="tagColor">{{ tagText }}</a-tag>
</template>

<script setup lang="ts">
interface Props {
  status: number | string;
  type: 'vehicle' | 'application' | 'schedule';
}

const props = defineProps<Props>();

const statusConfig = {
  vehicle: {
    1: { color: 'green', text: '可用' },
    2: { color: 'orange', text: '维修中' },
    3: { color: 'red', text: '报废' }
  },
  application: {
    0: { color: 'blue', text: '待队长审核' },
    1: { color: 'cyan', text: '待工程部审核' },
    2: { color: 'purple', text: '已分配车辆' },
    3: { color: 'orange', text: '使用中' },
    4: { color: 'green', text: '已完成' },
    5: { color: 'red', text: '已拒绝' }
  }
};

const config = computed(() => statusConfig[props.type][props.status] || { color: 'default', text: '未知' });
const tagColor = computed(() => config.value.color);
const tagText = computed(() => config.value.text);
</script>
```

### 3.2 车辆选择器组件
```vue
<template>
  <a-select
    v-model:value="selectedValue"
    :placeholder="placeholder"
    :loading="loading"
    show-search
    :filter-option="filterOption"
    @change="handleChange"
  >
    <a-select-option
      v-for="vehicle in vehicleList"
      :key="vehicle.id"
      :value="vehicle.id"
      :disabled="vehicle.status !== 1"
    >
      <div class="vehicle-option">
        <span class="vehicle-number">{{ vehicle.vehicleNumber }}</span>
        <span class="vehicle-model">{{ vehicle.vehicleModel }}</span>
        <StatusTag :status="vehicle.status" type="vehicle" size="small" />
      </div>
    </a-select-option>
  </a-select>
</template>
```

## 4. 样式设计

### 4.1 主题色彩
```scss
// 车辆管理主题色
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;

// 状态色彩
$status-available: #52c41a;
$status-in-use: #1890ff;
$status-maintenance: #faad14;
$status-scrapped: #ff4d4f;
```

### 4.2 布局样式
```scss
.vehicle-info-page {
  .search-form {
    background: #fff;
    padding: 16px;
    margin-bottom: 16px;
    border-radius: 6px;
  }

  .action-bar {
    margin-bottom: 16px;
    
    .ant-btn {
      margin-right: 8px;
    }
  }

  .table-container {
    background: #fff;
    border-radius: 6px;
  }
}

.vehicle-dashboard {
  .stats-cards {
    margin-bottom: 24px;
  }

  .date-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }

  .vehicle-grid {
    .vehicle-card {
      &.available {
        .ant-card {
          border-color: $status-available;
        }
      }

      &.in-use {
        .ant-card {
          border-color: $status-in-use;
        }
      }

      &.maintenance {
        .ant-card {
          border-color: $status-maintenance;
        }
      }
    }
  }
}
```

## 5. 响应式设计

### 5.1 移动端适配
```scss
@media (max-width: 768px) {
  .vehicle-grid {
    .ant-col {
      span: 24 !important;
      margin-bottom: 16px;
    }
  }

  .search-form {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }

  .stats-cards {
    .ant-col {
      span: 12 !important;
      margin-bottom: 16px;
    }
  }
}
```

### 5.2 平板适配
```scss
@media (min-width: 769px) and (max-width: 1024px) {
  .vehicle-grid {
    .ant-col {
      span: 8 !important;
    }
  }

  .stats-cards {
    .ant-col {
      span: 12 !important;
    }
  }
}
```

这个前端设计文档涵盖了车辆管理系统的主要页面结构、组件设计和样式规范，为开发团队提供了详细的实现指导。
