{"name": "xx-web-vue3", "version": "3.5.0", "description": "数智化培训", "author": {"name": "xx科技有限公司", "url": "http://www.xx.cn/"}, "homepage": "http://www.xx.cn/", "license": "MIT", "scripts": {"commit": "czg", "bootstrap": "pnpm install", "serve": "npm run dev", "dev": "vite", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --mode test && esno ./build/script/postBuild.ts", "build:no-cache": "pnpm clean:cache && npm run build", "report": "cross-env REPORT=true npm run build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build && vite preview", "preview:dist": "vite preview", "log": "conventional-changelog -p angular -i CHANGELOG.md -s", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "clean:lib": "rimraf node_modules", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "reinstall": "rimraf pnpm-lock.yaml && rimraf package.lock.json && rimraf node_modules && npm run bootstrap", "gen:icon": "esno ./build/generate/icon/index.ts"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "gifsicle": "5.2.0"}, "dependencies": {"@ant-design/colors": "7.0.0", "@ant-design/icons-vue": "6.1.0", "@fullcalendar/core": "6.1.8", "@fullcalendar/daygrid": "6.1.8", "@fullcalendar/interaction": "6.1.8", "@fullcalendar/timegrid": "6.1.8", "@fullcalendar/vue3": "6.1.8", "@iconify/iconify": "3.1.0", "@kjgl77/datav-vue3": "^1.7.4", "@logicflow/core": "1.2.1", "@logicflow/extension": "1.2.1", "@vue/runtime-core": "3.3.4", "@vue/shared": "3.3.4", "@vueuse/core": "10.1.2", "@vueuse/router": "^10.11.0", "@vueuse/shared": "10.1.2", "@zxcvbn-ts/core": "2.2.1", "ant-design-vue": "3.2.20", "axios": "1.4.0", "codemirror": "5.65.12", "cron-parser": "4.8.1", "cropperjs": "1.5.13", "crypto-js": "4.1.1", "dayjs": "1.11.7", "echarts": "5.4.2", "echarts-stat": "1.2.0", "ezuikit-js": "8.1.9-beta.3", "file-saver": "^2.0.5", "highcharts": "11.0.1", "highcharts-vue": "1.4.1", "html2canvas": "^1.4.1", "intro.js": "7.0.1", "jsbarcode": "3.11.5", "jspdf": "^3.0.0", "lodash-es": "4.17.21", "monaco-editor": "0.38.0", "nprogress": "0.2.0", "path-to-regexp": "6.2.1", "pinia": "2.1.3", "print-js": "1.6.0", "qrcode": "1.5.1", "qs": "6.11.1", "resize-observer-polyfill": "1.5.1", "showdown": "2.1.0", "sortablejs": "1.15.0", "spark-md5": "3.0.2", "terser": "5.14.2", "tinymce": "5.10.7", "vditor": "3.9.1", "vue": "3.3.13", "vue-grid-layout": "3.0.0-beta1", "vue-i18n": "9.2.2", "vue-json-pretty": "2.2.4", "vue-router": "4.2.1", "vue-simple-uploader": "1.0.0", "vue-types": "5.0.2", "vue3-draggable-resizable": "1.6.5", "vue3-tree-org": "4.2.2", "vuedraggable": "4.1.0", "xlsx": "0.18.5"}, "devDependencies": {"@commitlint/cli": "17.6.3", "@commitlint/config-conventional": "17.6.3", "@iconify/json": "2.2.43", "@purge-icons/generated": "0.9.0", "@rys-fe/vite-plugin-theme": "0.8.6", "@types/codemirror": "5.60.7", "@types/crypto-js": "4.1.1", "@types/ezuikit-js": "^8.0.0", "@types/fs-extra": "11.0.1", "@types/inquirer": "8.2.6", "@types/intro.js": "5.1.1", "@types/lodash-es": "4.17.7", "@types/node": "18.15.11", "@types/nprogress": "0.2.0", "@types/qrcode": "1.5.0", "@types/qs": "6.9.7", "@types/showdown": "2.0.0", "@types/sortablejs": "1.15.1", "@typescript-eslint/eslint-plugin": "5.57.0", "@typescript-eslint/parser": "5.57.0", "@vitejs/plugin-legacy": "4.0.3", "@vitejs/plugin-vue": "4.2.3", "@vitejs/plugin-vue-jsx": "3.0.1", "@vue/compiler-sfc": "3.2.47", "@vue/test-utils": "2.3.2", "autoprefixer": "10.4.14", "conventional-changelog-cli": "2.2.2", "cross-env": "7.0.3", "cz-git": "1.6.1", "czg": "1.6.1", "dotenv": "16.0.3", "eslint": "8.37.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-vue": "9.10.0", "esno": "0.16.3", "fs-extra": "11.1.1", "inquirer": "9.1.5", "less": "4.1.3", "lint-staged": "13.2.0", "npm-run-all": "4.1.5", "picocolors": "1.0.0", "postcss": "8.4.21", "postcss-html": "1.5.0", "postcss-less": "6.0.0", "prettier": "2.8.8", "rimraf": "4.4.1", "rollup": "3.7.4", "rollup-plugin-visualizer": "5.9.0", "script-loader": "^0.7.2", "stylelint": "15.4.0", "stylelint-config-prettier": "9.0.3", "stylelint-config-recommended": "11.0.0", "stylelint-config-recommended-vue": "1.4.0", "stylelint-config-standard": "32.0.0", "stylelint-order": "6.0.3", "ts-node": "10.9.1", "typescript": "5.0.4", "vite": "4.3.8", "vite-plugin-cdn-import": "0.3.5", "vite-plugin-compression": "0.5.1", "vite-plugin-html": "3.2.0", "vite-plugin-imagemin": "0.6.1", "vite-plugin-mkcert": "1.10.1", "vite-plugin-purge-icons": "0.9.2", "vite-plugin-pwa": "0.14.0", "vite-plugin-style-import": "2.0.0", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.6.4", "vite-plugin-vue-setup-extend": "0.4.0", "vite-plugin-windicss": "1.8.10", "vue-eslint-parser": "9.1.1", "vue-tsc": "1.6.5"}, "engines": {"node": ">=16.15.0", "pnpm": ">=8.1.0"}}