# 车辆管理系统需求文档

## 1. 项目概述

### 1.1 项目背景
为了规范车辆使用管理，提高车辆使用效率，建立完善的车辆申请、审核、分配、使用、回填台班等全流程管理系统。

### 1.2 项目目标
- 实现车辆信息的统一管理
- 建立规范的用车申请流程
- 提供车辆使用状态的实时监控
- 支持台班数据的统计和导出
- 提升车辆资源配置效率

## 2. 功能需求

### 2.1 车辆信息管理

#### 2.1.1 功能描述
对车辆基础信息进行录入、编辑、查询、删除等操作。

#### 2.1.2 功能详情
- **车辆信息录入**：支持手动录入和Excel批量导入
- **车辆信息编辑**：支持对已录入车辆信息的修改
- **车辆信息查询**：支持多条件组合查询
- **车辆信息删除**：支持单个和批量删除（需权限控制）

#### 2.1.3 车辆信息字段
- 车牌号（必填，唯一）`（车牌号要等申请流程最后一步，台班回填阶段来填车牌号。）`
- 车辆型号
- 车辆品牌
- 吨位
- 车辆使用单位
- 备注信息
- 创建人
- 创建时间
- 更新人
- 更新时间

### 2.2 用车申请管理

#### 2.2.1 功能描述
提供完整的用车申请流程，包括申请提交、审核、车辆分配、使用确认等环节。

#### 2.2.2 申请流程
1. **申请提交**：申请人填写用车申请单
2. **队长审核**：队长对申请进行初审
3. **工程部接受**：工程部进行最终审核和车辆分配
4. **使用确认**：确认车辆使用
5. **台班回填**：使用结束后回填台班数据

#### 2.2.3 申请限制条件
- **人员限制**：只有自由人员可以申请用车`(人员限制可能要针对相应的人员开通权限，一般都是各个队的资料员，不一定是自有人员)`
- **优先级控制**：紧急申请优先分配车辆
- **车辆可用性**：只能分配可用状态的车辆

#### 2.2.4 用车申请字段
- 申请单号（系统自动生成）
- 申请人
- 申请部门/队部
- 用车日期
- 预计用车时长
- 用车目的
- 优先级（一般/紧急）
- 申请状态（待审核/已审核/已分配/使用中/已完成/已拒绝）
- 分配车辆
- 队长审核意见
- 队长审核时间
- 工程部审核意见
- 工程部审核时间
- 实际使用开始时间
- 实际使用结束时间
- 实际使用时长（台班）
- 创建时间
- 更新时间

### 2.3 车辆看板管理

#### 2.3.1 功能描述
提供车辆使用状态的可视化展示，支持当天状态查看和明天计划管理。

#### 2.3.2 功能详情
- **当天车辆状态**：展示当天所有车辆的使用状态
- **明天计划管理**：展示明天的车辆安排计划，支持工程部直接修改
- **状态实时更新**：车辆状态实时同步更新

#### 2.3.3 车辆状态
- 空闲：车辆可用，未分配
- 已分配：车辆已分配但未开始使用
- 使用中：车辆正在使用
- 维修中：车辆在维修，不可用
- 其他：其他特殊状态

### 2.4 台班管理

#### 2.4.1 功能描述
对车辆使用台班进行记录、统计和分析。

#### 2.4.2 功能详情
- **台班回填**：使用结束后回填实际使用时长
- **台班统计**：按时间、车辆、部门等维度统计台班数据
- **台班分析**：提供台班使用效率分析报表

### 2.5 数据导出

#### 2.5.1 功能描述
支持各类数据的Excel导出功能。

#### 2.5.2 导出内容
- 车辆信息导出
- 用车申请记录导出
- 台班统计数据导出
- 车辆使用报表导出

## 3. 权限设计

### 3.1 角色定义
- **申请人**：可提交用车申请，查看自己的申请记录
- **队长**：可审核本队部的用车申请
- **工程部人员**：可进行最终审核、车辆分配、计划管理
- **系统管理员**：拥有所有权限

### 3.2 权限矩阵
| 功能模块 | 申请人 | 队长 | 工程部 | 管理员 |
|---------|--------|------|--------|--------|
| 车辆信息查看 | ✓ | ✓ | ✓ | ✓ |
| 车辆信息管理 | ✗ | ✗ | ✓ | ✓ |
| 用车申请提交 | ✓ | ✓ | ✓ | ✓ |
| 队长审核 | ✗ | ✓ | ✗ | ✓ |
| 工程部审核 | ✗ | ✗ | ✓ | ✓ |
| 车辆分配 | ✗ | ✗ | ✓ | ✓ |
| 车辆看板 | ✓ | ✓ | ✓ | ✓ |
| 计划管理 | ✗ | ✗ | ✓ | ✓ |
| 台班回填 | ✓ | ✓ | ✓ | ✓ |
| 数据导出 | ✗ | ✓ | ✓ | ✓ |

## 4. 技术要求

### 4.1 前端技术栈
- Vue 3.x
- TypeScript
- Ant Design Vue
- Vite
- Pinia (状态管理)

### 4.2 后端技术栈
- Java Spring Boot
- MySQL 8.0+
- MyBatis Plus
- Redis (缓存)

### 4.3 部署要求
- 支持Docker容器化部署
- 支持集群部署
- 数据库支持主从复制

## 5. 非功能性需求

### 5.1 性能要求
- 页面响应时间 < 2秒
- 支持并发用户数 > 100
- 数据导出时间 < 30秒（万条数据）

### 5.2 安全要求
- 用户身份认证
- 操作权限控制
- 数据传输加密
- 操作日志记录

### 5.3 可用性要求
- 系统可用性 > 99%
- 支持7×24小时运行
- 提供数据备份和恢复机制

## 6. 界面设计要求

### 6.1 设计原则
- 界面简洁美观
- 操作流程清晰
- 响应式设计，支持PC和移动端
- 符合企业VI规范

### 6.2 关键页面
- 车辆信息管理页面
- 用车申请页面
- 审核页面
- 车辆看板页面
- 台班统计页面

## 7. 数据字典

### 7.1 优先级字典
- 1: 一般
- 2: 紧急

### 7.2 申请状态字典
- 0: 待队长审核
- 1: 待工程部审核
- 2: 已分配车辆
- 3: 使用中
- 4: 已完成
- 5: 已拒绝

### 7.3 车辆状态字典
- 1: 可用
- 2: 维修中
- 3: 报废

### 7.4 审核结果字典
- 1: 通过
- 2: 拒绝

## 8. 项目计划

### 8.1 开发阶段
- 第一阶段：车辆信息管理（1周）
- 第二阶段：用车申请流程（2周）
- 第三阶段：车辆看板和台班管理（1周）
- 第四阶段：数据导出和报表（1周）
- 第五阶段：测试和优化（1周）

### 8.2 里程碑
- 需求确认：第1周
- 原型设计：第2周
- 开发完成：第6周
- 测试完成：第7周
- 上线部署：第8周
