import * as XLSX from "xlsx";
import {getUserIdByNameAndPhoneNumber} from "/@/api/permission/user";
import {trim} from "lodash-es";
import {useMessage} from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();


export async function importUserInfoExcel(file: any, perIds: string[]): Promise<string[]> {
  try {
    const data = await file.arrayBuffer();
    const workbook = XLSX.read(data, {type: 'binary'});
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    const sheetJson = XLSX.utils.sheet_to_json(worksheet, {header: 1});
    const tableData = sheetJson.slice(1).map((item: any[]) => {
      return {
        realName: item[0],
        mobilePhone: item[1],
      }
    });
    console.log('tableData', tableData);

    // Wait for the API call to complete and return the result
    const res = await getUserIdByNameAndPhoneNumber({list: tableData});
    const userIdList = res.data.idList;
    console.log('userIdList', userIdList);

    perIds = [...new Set([...perIds, ...userIdList])];

    let unmatchedUserString = '';
    res.data.list.forEach((item, index) => {
        if (item.realName) {
          if (index === 0) {
            unmatchedUserString += item.realName;
          } else {
            unmatchedUserString += item.realName + ',';
          }
        }
      }
    )
    if (trim(unmatchedUserString) !== '') {
      createConfirm({
        iconType: 'error',
        title: unmatchedUserString + ' 匹配用户未成功',
      })
    }

    console.log('importUserInfoExcel perIds', perIds)
    return perIds;

  } catch (error) {
    console.error('Error parsing the Excel file', error);
  }
}
