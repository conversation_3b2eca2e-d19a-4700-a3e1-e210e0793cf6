export default {
  api: {
    operationFailed: '操作失敗',
    errorTip: '錯誤提示',
    errorMessage: '操作失敗,系統異常!',
    timeoutMessage: '登錄超時,請重新登錄!',
    apiTimeoutMessage: '接口請求超時,請刷新頁面重試!',
    apiRequestFailed: '請求出錯，請稍候重試',
    networkException: '網絡異常',
    networkExceptionMsg: '網絡異常，請檢查您的網絡連接是否正常!',

    errMsg401: '用戶沒有權限（令牌、用戶名、密碼錯誤）!',
    errMsg403: '用戶得到授權，但是訪問是被禁止的。!',
    errMsg404: '網絡請求錯誤,未找到該資源!',
    errMsg405: '網絡請求錯誤,請求方法未允許!',
    errMsg408: '網絡請求超時!',
    errMsg500: '服務器錯誤,請聯系管理員!',
    errMsg501: '網絡未實現!',
    errMsg502: '網絡錯誤!',
    errMsg503: '服務不可用，服務器暫時過載或維護!',
    errMsg504: '網絡超時!',
    errMsg505: 'http版本不支持該請求!',
  },
  app: { logoutTip: '溫馨提醒', logoutMessage: '是否確認退出系統?', menuLoading: '菜單加載中...' },
  errorLog: {
    tableTitle: '錯誤日誌列表',
    tableColumnType: '類型',
    tableColumnDate: '時間',
    tableColumnFile: '文件',
    tableColumnMsg: '錯誤信息',
    tableColumnStackMsg: 'stack信息',

    tableActionDesc: '詳情',

    modalTitle: '錯誤詳情',

    fireVueError: '點擊觸發vue錯誤',
    fireResourceError: '點擊觸發資源加載錯誤',
    fireAjaxError: '點擊觸發ajax錯誤',

    enableMessage: '只在`/src/settings/projectSetting.ts` 內的useErrorHandle=true時生效.',
  },
  exception: {
    backLogin: '返回登錄',
    backHome: '返回首頁',
    subTitle403: '抱歉，您無權訪問此頁面。',
    subTitle404: '抱歉，您訪問的頁面不存在。',
    subTitle500: '抱歉，服務器報告錯誤。',
    noDataTitle: '當前頁無數據',
    networkErrorTitle: '網絡錯誤',
    networkErrorSubTitle: '抱歉，您的網絡連接已斷開，請檢查您的網絡！',
  },
  lock: {
    unlock: '點擊解鎖',
    alert: '鎖屏密碼錯誤',
    backToLogin: '返回登錄',
    entry: '進入系統',
    placeholder: '請輸入登錄密碼',
  },
  login: {
    welcome: '歡迎使用',
    subTitle: '通過帳號密碼登錄',
    subTitle1: '通過手機驗證碼登錄，或者切換為',
    subTitle2: '通過帳號密碼登錄，或者切換為',
    otherLogin: '其他登錄方式',
    backSignIn: '返回',
    signInFormTitle: '賬號密碼登錄',
    mobileSignInFormTitle: '手機驗證碼登錄',
    qrSignInFormTitle: '二維碼登錄',
    signUpFormTitle: '註冊',
    forgetFormTitle: '重置密碼',

    signInTitle: '開箱即用的中後臺管理系統',
    signInDesc: '輸入您的個人詳細信息開始使用！',
    policy: '我同意xxx隱私政策',
    scanSign: `掃碼後點擊"確認"，即可完成登錄`,

    loginButton: '登錄',
    registerButton: '註冊',
    rememberMe: '記住我',
    forgetPassword: '忘記密碼?',
    otherSignIn: '其他登錄方式',

    // notify
    lastLoginInfo: '上次登錄信息',

    // placeholder
    accountPlaceholder: '請輸入賬號',
    passwordPlaceholder: '請輸入密碼',
    smsPlaceholder: '請輸入驗證碼',
    mobilePlaceholder: '請輸入手機號碼',
    policyPlaceholder: '勾選後才能註冊',
    diffPwd: '兩次輸入密碼不一致',

    userName: '賬號',
    confirmPassword: '確認密碼',
    email: '郵箱',

    title: '賬戶密碼登錄',
    scanTitle: '掃碼登錄',
    codeTitle: '手機驗證登錄',
    logIn: '登錄',
    username: '請輸入賬號',
    password: '請輸入密碼',
    version: '版本',
    upper: '大寫已鎖定',
    scanTip: '掃碼登錄',
    accountTip: '請輸入賬號',
    passwordTip: '請輸入密碼',
    codeTip: '請輸入驗證碼',
    changeCode: '點擊切換驗證碼',
    mobile: '請輸入手機號',
    rightMobile: '請輸入正確的手機號',
    smsCode: '請輸入驗證碼',
    getCode: '獲取驗證碼',
    reSend: '重新發送',
    company: '請輸入公司名',
    contacts: '請輸入聯系人',
    rule: "子賬戶規則：手機號{'@'}賬戶 例：***********{'@'}101001",
  },
};
