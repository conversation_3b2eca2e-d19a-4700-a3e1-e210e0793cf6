export default {
  basic: {
    login: 'Login',
    errorLogList: 'Error Log',
    home: 'Home',
    externalLink: 'ExternalLink',
    workFlowDetail: 'WorkFlow Detail',
    emailDetail: 'Email Detail',
    previewModel: 'Model Preview',
  },
  onlineDev: 'OnlineDev',
  'onlineDev-webDesign': 'WebDesign',
  'onlineDev-appDesign': 'APPDesign',
  'onlineDev-dataReport': 'DataReport',
  'onlineDev-dataScreen': 'DataScreen',
  'onlineDev-visualPortal': 'VisualPortal',
  generator: 'Generator',
  'generator-webForm': 'WebForm',
  'generator-appForm': 'AppForm',
  'generator-flowForm': 'FlowForm',
  system: 'System',
  'system-sysConfig': 'SysConfig',
  'system-notice': 'Notice',
  'system-task': 'Task',
  'system-cache': 'CacheManage',
  'system-log': 'Log',
  'system-monitor': 'Monitor',
  'system-icons': 'Icons',
  'system-language': 'Language',
  'system-menu': 'Menu',
  'system-area': 'Area',
  'system-billRule': 'BillRule',
  'system-systemTemplate': 'SystemTemplate',
  'system-smsTemplate': 'SmsTemplate',
  'system-messageTemplate': 'MessageTemplate',
  'system-printDev': 'PrintTemplate',
  systemData: 'SystemData',
  'systemData-dataSource': 'DataSource',
  'systemData-dataModel': 'DataModel',
  'systemData-dataSync': 'DataSync',
  'systemData-dataBackup': 'DataBackup',
  'systemData-dataInterface': 'DataInterface',
  'systemData-interfaceAuth': 'InterfaceAuth',
  'systemData-dictionary': 'Dictionary',
  'systemData-map': 'Map',
  commonWords: 'CommonWords',
  weChat: 'WeChat',
  'weChat-mpConfig': 'MPConfig',
  'weChat-mpMenu': 'MPMenu',
  'weChat-mpUser': 'MPUser',
  'weChat-mpMessage': 'MPMessage',
  'weChat-mpMaterial': 'MPMaterial',
  'weChat-qyhConfig': 'QYHConfig',
  'weChat-qyDepartment': 'QYDepartment',
  'weChat-qyUser': 'QYUser',
  'weChat-qyMessage': 'QYMessage',
  permission: 'Permission',
  'permission-grade': 'Grade',
  'permission-organize': 'Organize',
  'permission-department': 'Department',
  'permission-position': 'Position',
  'permission-user': 'User',
  'permission-role': 'Role',
  'permission-group': 'Group',
  'permission-authorize': 'Authorize',
  'permission-userOnline': 'UserOnline',
  flowEngine: 'FlowEngine',
  formDesign: 'FlowForm',
  workFlow: 'WorkFlow',
  'workFlow-form': 'Form',
  'workFlow-flowEngine': 'FlowDesign',
  'workFlow-addFlow': 'AddFlow',
  'workFlow-flowLaunch': 'FlowLaunch',
  'workFlow-flowTodo': 'FlowTodo',
  'workFlow-flowDone': 'FlowDone',
  'workFlow-flowCirculate': 'FlowCirculate',
  'workFlow-entrust': 'Entrust',
  'workFlow-flowMonitor': 'FlowMonitor',
  msgCenter: 'MsgCenter',
  'msgCenter-accountConfig': 'AccountConfig',
  'msgCenter-accountConfig-mail': 'Mail',
  'msgCenter-accountConfig-shortMsg': 'ShortMsg',
  'msgCenter-accountConfig-weCom': 'WeCom',
  'msgCenter-accountConfig-ding': 'DingTalk',
  'msgCenter-accountConfig-webhook': 'Webhook',
  'msgCenter-accountConfig-mp': 'MP',
  'msgCenter-msgTemplate': 'MsgTemplate',
  'msgCenter-sendConfig': 'SendConfig',
  'msgCenter-msgMonitor': 'MsgMonitor',
  'extend-graphDemo': 'Graph Demo',
  'extend-graphDemo-echartsBar': 'E-Bar',
  'extend-graphDemo-echartsPie': 'E-Pie',
  'extend-graphDemo-echartsBarAcross': 'E-BarAcross',
  'extend-graphDemo-echartsGauge': 'E-Gauge',
  'extend-graphDemo-echartsLineArea': 'E-LineArea',
  'extend-graphDemo-echartsScatter': 'E-Scatter',
  'extend-graphDemo-echartsCandlestick': 'E-Candlestick',
  'extend-graphDemo-echartsLineBar': 'E-LineBar',
  'extend-graphDemo-echartsTree': 'E-Tree',
  'extend-graphDemo-echartsFunnel': 'E-Funnel',
  'extend-graphDemo-highchartsScatter': 'H-Scatter',
  'extend-graphDemo-highchartsWordcloud': 'H-Wordcloud',
  'extend-graphDemo-highchartsLine': 'H-Line',
  'extend-graphDemo-highchartsArea': 'H-Area',
  'extend-graphDemo-highchartsGauge': 'H-Gauge',
  'extend-graphDemo-highchartsBellcurve': 'H-Bellcurve',
  'extend-graphDemo-highchartsFunnel': 'H-Funnel',
  'extend-graphDemo-highchartsBullet': 'H-Bullet',
  'extend-graphDemo-highchartsColumn': 'H-Column',
  'extend-graphDemo-highchartsPie': 'H-Pie',
  'extend-tableDemo': 'Table Demo',
  'extend-tableDemo-commonTable': 'CommonTable',
  'extend-tableDemo-statisticsTable': 'StatisticsTable',
  'extend-tableDemo-lockTable': 'LockTable',
  'extend-tableDemo-groupingTable': 'GroupingTable',
  'extend-tableDemo-redactTable': 'RedactTable',
  'extend-tableDemo-complexHeader': 'ComplexHeader',
  'extend-tableDemo-mergeTable': 'MergeTable',
  'extend-tableDemo-printTable': 'PrintTable',
  'extend-tableDemo-extension': 'Extension',
  'extend-tableDemo-treeTable': 'TreeTable',
  'extend-tableDemo-postilTable': 'PostilTable',
  'extend-tableDemo-tableTree': 'TableTree',
  'extend-tableDemo-signTable': 'SignTable',
  'extend-formDemo': 'Form Demo',
  'extend-formDemo-verifyForm': 'VerifyForm',
  'extend-formDemo-verifyForm1': 'VerifyForm1',
  'extend-formDemo-fieldForm1': 'FieldForm1',
  'extend-formDemo-fieldForm2': 'FieldForm2',
  'extend-formDemo-fieldForm3': 'FieldForm3',
  'extend-formDemo-fieldForm4': 'FieldForm4',
  'extend-formDemo-fieldForm5': 'FieldForm5',
  'extend-formDemo-fieldForm6': 'FieldForm6',
  extend: 'Extend',
  'extend-functionDemo': 'Function Demo',
  'extend-portalDemo': 'Portal Demo',
  'extend-orderDemo': 'Order Demo',
  'extend-bigData': 'BigData',
  'extend-importAndExport': 'ImportAndExport',
  'extend-signet': 'Signet',
  'extend-signature': 'Signature',
  'extend-schedule': 'Schedule',
  'extend-email': 'Email',
  'extend-document': 'Document',
  'extend-documentPreview': 'Document Demo',
  'extend-barCode': 'BarCode',
  'extend-printData': 'PrintData',
  'extend-map': 'Map',
  'extend-order': 'Order',
  'extend-projectGantt': 'ProjectGantt',
  moreMenu: 'More...',
  dataReport: 'DataReport Demo',
  reportBI: 'ReportBI Demo',
};
