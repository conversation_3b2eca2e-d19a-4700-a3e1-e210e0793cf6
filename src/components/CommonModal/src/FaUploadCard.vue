<template>
  <div class="upload-card">
    <div class="upload-area">
      <div class="upload-icon">
        <CloudUploadOutlined />
      </div>
      <div class="upload-content">
        <p class="upload-title">上传填好的数据表</p>
        <p class="upload-tip">文件后缀名必须是xls或xlsx，文件大小不超过10MB，最多支持导入1000条数据</p>
        <a-upload
          v-model:file-list="fileList"
          :action="uploadAction"
          :headers="uploadHeaders"
          :before-upload="beforeUpload"
          :remove="handleRemove"
          :change="handleChange"
          accept=".xls,.xlsx"
          :max-count="1"
          class="upload-button-area"
        >
          <a-button type="primary">
            <UploadOutlined />
            选择文件
          </a-button>
        </a-upload>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { CloudUploadOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { useGlobSetting } from '/@/hooks/setting';
import { getToken } from '/@/utils/auth';
import type { UploadChangeParam, UploadFile } from 'ant-design-vue';

const props = defineProps<{
  value?: string;
}>();

const emit = defineEmits<{
  'update:value': [value: string];
}>();

const { createMessage } = useMessage();
const globSetting = useGlobSetting();

const fileList = ref<UploadFile[]>([]);

const uploadAction = computed(() => `${globSetting.apiUrl}/api/file/Uploader/UploadFile`);
const uploadHeaders = computed(() => ({
  Authorization: getToken(),
}));

function beforeUpload(file: UploadFile) {
  const isExcel = file.type === 'application/vnd.ms-excel' || 
                  file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
  if (!isExcel) {
    createMessage.error('只能上传Excel文件！');
    return false;
  }
  
  const isLt10M = file.size! / 1024 / 1024 < 10;
  if (!isLt10M) {
    createMessage.error('文件大小不能超过10MB！');
    return false;
  }
  
  return true;
}

function handleChange(info: UploadChangeParam) {
  if (info.file.status === 'done') {
    if (info.file.response && info.file.response.code === 200) {
      emit('update:value', info.file.response.data.fileId);
      createMessage.success('文件上传成功');
    } else {
      createMessage.error('文件上传失败');
    }
  } else if (info.file.status === 'error') {
    createMessage.error('文件上传失败');
  }
}

function handleRemove() {
  emit('update:value', '');
  return true;
}
</script>

<style scoped lang="less">
.upload-card {
  margin-bottom: 16px;
  
  .upload-area {
    display: flex;
    align-items: center;
    padding: 24px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    transition: border-color 0.3s;
    
    &:hover {
      border-color: #1890ff;
    }
    
    .upload-icon {
      font-size: 48px;
      color: #1890ff;
      margin-right: 24px;
    }
    
    .upload-content {
      flex: 1;
      
      .upload-title {
        margin: 0 0 8px;
        font-size: 16px;
        font-weight: 500;
        color: #262626;
      }
      
      .upload-tip {
        margin: 0 0 16px;
        font-size: 14px;
        color: #8c8c8c;
        line-height: 1.5;
      }
      
      .upload-button-area {
        :deep(.ant-upload) {
          display: inline-block;
        }
      }
    }
  }
}
</style>
