<template>
  <div class="download-card">
    <div class="download-area">
      <div class="download-icon">
        <FileExcelOutlined />
      </div>
      <div class="download-content">
        <p class="download-title">填写导入数据信息</p>
        <p class="download-tip">请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除</p>
        <a-button type="link" @click="handleDownload">
          <DownloadOutlined />
          下载模板
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FileExcelOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { downloadByUrl } from '/@/utils/file/download';
import { useGlobSetting } from '/@/hooks/setting';

const props = defineProps<{
  downloadUrl: string;
}>();

const { createMessage } = useMessage();
const globSetting = useGlobSetting();

function handleDownload() {
  try {
    const fullUrl = `${globSetting.apiUrl}${props.downloadUrl}`;
    downloadByUrl({ url: fullUrl });
    createMessage.success('模板下载成功');
  } catch (error) {
    createMessage.error('模板下载失败');
    console.error('下载失败:', error);
  }
}
</script>

<style scoped lang="less">
.download-card {
  .download-area {
    display: flex;
    align-items: center;
    padding: 24px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fff;
    
    .download-icon {
      font-size: 48px;
      color: #52c41a;
      margin-right: 24px;
    }
    
    .download-content {
      flex: 1;
      
      .download-title {
        margin: 0 0 8px;
        font-size: 16px;
        font-weight: 500;
        color: #262626;
      }
      
      .download-tip {
        margin: 0 0 16px;
        font-size: 14px;
        color: #8c8c8c;
        line-height: 1.5;
      }
    }
  }
}
</style>
