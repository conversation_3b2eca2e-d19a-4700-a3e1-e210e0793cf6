<template>
  <div class="header-steps">
    <a-steps :current="current" type="navigation" size="small">
      <a-step title="上传文件" disabled />
      <a-step title="数据预览" disabled />
      <a-step title="导入数据" disabled />
    </a-steps>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  current: number;
}>();

defineEmits<{
  'update:current': [value: number];
}>();
</script>

<style scoped lang="less">
.header-steps {
  margin-bottom: 24px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}
</style>
