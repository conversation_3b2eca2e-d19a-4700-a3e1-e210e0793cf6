@prefix-cls: ~'@{namespace}-basic-portal';

.@{prefix-cls} {
  .options-box {
    .btnOptions {
      padding: 0px 2px !important;
      i {
        font-size: 20px;
      }
    }
  }
  .divider {
    height: 28px;
    margin: 0 5px;
  }
  .components-menu {
    :deep(.components-item) {
      line-height: 35px;
      padding: 0 25px 0 20px;
    }
  }
  .portal-card-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    .ant-card-head {
      margin-bottom: unset;
      padding: unset;
      height: 55px;
      .ant-card-head-wrapper {
        height: 100%;
        .ant-card-head-title {
          height: 100%;
          padding: unset;
        }
      }
    }
    .portal-common-title {
      width: 100%;
      height: 100%;
      font-size: 16px;
      padding: 0 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      overflow: hidden;
      .title {
        flex: 1;
        display: flex;
        align-items: center;
        overflow: hidden;
        i {
          font-size: 18px;
        }
        span {
          padding-left: 5px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .button {
        padding-left: 10px;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: unset;
      }
    }
    .ant-card-body {
      width: 100%;
      overflow: hidden;
      flex: 1;
      padding: unset;
    }
    .portal-card-timeline {
      padding: 15px 20px;
      overflow: auto !important;
    }
    .portal-card-rankList {
      .rank-box {
        line-height: 35px;
        .rank-box-span {
          display: inline-block;
          width: 24px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          background-color: rgba(24, 144, 255, 0.39);
          border-radius: 50%;
          color: @component-background;
          opacity: 0.3;
        }
        img {
          width: 35px;
          height: 35px;
          margin: 0 auto;
        }
      }
      .cup-medal-box {
        height: 100%;
        display: flex;
        flex-direction: column;
        .cup-top-box {
          display: flex;
          justify-content: center;
          align-items: flex-end;
          overflow: hidden;
          padding: 10px 0 15px;
          .cup-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            img {
              width: 46px;
              margin-bottom: -6px;
              z-index: 20;
            }
            .top-bg {
              width: 230px;
              height: 0;
              border-bottom: 9px solid rgba(239, 233, 225, 0.39);
              border-left: 20px solid transparent;
              border-right: 10px solid transparent;
            }
            .cup-box-content {
              width: 230px;
              height: 81px;
              background: rgba(245, 241, 234, 0.59);
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              color: #6f89ac;
              p {
                width: 100%;
                margin-bottom: 4px;
                padding: 0 10px;
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
          .cup1-box {
            img {
              width: 63px;
            }
            .top-bg {
              width: 260px;
              border-right: 20px solid transparent;
              border-bottom: 9px solid rgba(250, 247, 242, 0.59);
            }
            .cup-box-content {
              width: 260px;
              height: 103px;
              background: rgba(250, 247, 242, 0.59);
              color: #ce7c1f;
            }
          }
          .cup3-box {
            .top-bg {
              border-bottom: 9px solid rgba(245, 241, 234, 0.59);
              border-left: 10px solid transparent;
              border-right: 20px solid transparent;
            }
            .cup-box-content {
              height: 65px;
              background: rgba(245, 241, 234, 0.59);
              color: #8d4112;
            }
          }
        }
      }
      .medal-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        .medal-top-box {
          margin: 60px 0 15px;
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          min-width: 70%;
          .medal-box {
            width: 160px;
            height: 91px;
            border-radius: 4px;
            border: 1px solid rgba(111, 137, 172, 0.59);
            flex-shrink: 0;
            position: relative;
            .medal-box-content {
              height: 100%;
              width: 100%;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              color: #6f89ac;
              p {
                width: 100%;
                margin-bottom: 4px;
                padding: 0 10px;
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
            img {
              width: 60px;
              margin-top: -30px;
            }
          }
          .medal1-box {
            width: 200px;
            height: 100px;
            margin: 0 10px;
            border: 1px solid rgba(206, 124, 31, 0.59);
            .medal-box-content {
              color: #ce7c1f;
            }
            img {
              width: 100px;
              margin-top: -65px;
            }
          }
          .medal3-box {
            border: 1px solid rgba(141, 65, 18, 0.59);
            .medal-box-content {
              color: #8d4112;
            }
          }
        }
      }
    }
    .portal-card-iframe {
      height: 100%;
      iframe {
        width: 100%;
        height: 100%;
      }
    }
    .portal-card-todoList,
    .portal-card-email {
      padding: 16px 20px;
      .item {
        display: block;
        line-height: 20px;
        font-size: 0;
        margin-bottom: 12px;
        cursor: pointer;
        &::after {
          content: '';
          clear: both;
          overflow: hidden;
        }
        .com-hover:hover {
          color: #1890ff !important;
        }
        .name {
          font-size: 14px;
          display: inline-block;
          width: calc(100% - 90px);
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          vertical-align: top;
          color: @text-color-base;
        }
        .time {
          font-size: 14px;
          display: inline-block;
          color: @text-color-secondary;
          width: 90px;
          text-align: right;
        }
      }
    }
    .portal-card-body {
      height: 100%;
      overflow: hidden;
      .portal-common-noData {
        margin: auto;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .noData-img {
          width: 100px;
          height: 100px;
          margin: 0 auto;
        }
        .noData-txt {
          font-size: 14px;
          color: #909399;
          line-height: 20px;
        }
      }
    }
  }
  .portal-box-tab,
  .portal-card-box {
    .portal-box-item {
      width: 100%;
      height: 100%;
      position: relative;
      border: 1px solid #f0f0f0;
      .active-item {
        border: 1px solid #409eff;
      }
    }
  }
}
.portal-design-box {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  .center-box {
    flex: 1;
    height: 100%;
    overflow: hidden;
    background-color: @component-background;
    margin-right: 10px;
    border-radius: 4px;
    .action-box {
      position: relative;
      height: 40px;
      padding: 0 15px;
      box-sizing: border-box;
      background: @component-background;
      display: flex;
      border-bottom: 1px solid @border-color-base1;
      align-items: center;
      .components-part {
        padding: 0 10px;
        .components-title {
          cursor: pointer;
          display: flex;
          align-items: center;
          i {
            font-size: 12px;
            line-height: 12px;
            margin-left: 4px;
          }
        }
      }
    }
    .empty-info {
      position: absolute;
      top: calc(50% - 200px);
      left: calc(50% - 200px);

      .empty-img {
        width: 400px;
        height: 400px;
      }
    }
    .layout-area {
      height: calc(100% - 42px);
      background: @component-background;
      position: relative;
      border: none;
      #ipad {
        height: calc(100% - 42px);
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 781px;
        .portal-card-body,
        .ant-tabs-content-holder,
        .ant-tabs-content {
          height: 250px !important;
        }
        .empty-info {
          top: calc(50% - 150px);
          left: calc(50% - 150px);
          .empty-img {
            width: 300px;
            height: 300px;
          }
        }
        .outer-ipad {
          background: url('../../../assets/images/iphoneBg.png');
          width: 389px;
          height: 711px;
          padding: 65px 40px;
          .ipadHead {
            background: #f7f8f9;
            text-align: center;
            .ipadHead-img {
              margin: 0 auto;
              height: 20px;
            }
          }
          .ipad-body {
            height: 98%;
            .ipad-name {
              text-align: center;
              margin-bottom: 5px;
              margin-top: 5px;
            }
            .center-scrollbar {
              height: 95.6672%;
              overflow: hidden;
            }
          }
        }
      }
      .drawing-board,
      .vue-grid-item,
      .item-box {
        position: relative;
        height: 100%;
        border: 1px dashed @border-color-base1;
        &.active-item {
          border: 1px solid @primary-color;
          & > .drawing-item-copy,
          & > .drawing-item-delete {
            display: block !important;
          }
        }
        .mask {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 9;
        }
        .vue-resizable-handle {
          z-index: 200;
        }
        .drawing-item-copy,
        .drawing-item-delete {
          display: none;
          position: absolute;
          top: -10px;
          width: 22px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-radius: 50%;
          font-size: 12px;
          border: 1px solid;
          cursor: pointer;
          z-index: 20;
          .anticon {
            vertical-align: top !important;
            margin-top: 5px;
          }
        }
        .drawing-item-copy {
          right: 56px;
          border-color: @primary-color;
          color: @primary-color;
          background: @component-background;
          &:hover {
            background: @primary-color;
            color: @component-background;
          }
        }
        .drawing-item-delete {
          right: 20px;
          border-color: #f56c6c;
          color: #f56c6c;
          background: @component-background;
          &:hover {
            background: #f56c6c;
            color: @component-background;
          }
        }
        &:hover {
          .drawing-item-copy,
          .drawing-item-delete {
            display: block;
          }
        }
      }
    }
  }
  .right-box {
    width: 340px;
    background-color: @component-background;
    border-radius: 4px;
    .filed-box {
      position: relative;
      height: calc(100% - 42px);
      box-sizing: border-box;
      overflow: hidden;
    }
    .ant-collapse {
      overflow-x: hidden;
      .ant-collapse-item {
        border-bottom: 1px solid @border-color-base1;
        .ant-collapse-header {
          padding: 12px 0 !important;
          .ant-collapse-arrow {
            right: 8px;
          }
        }
        .ant-collapse-content-box {
          padding: 10px 0 0;
        }
      }
    }
    .right-radio {
      .ant-radio-button-wrapper {
        padding: 0 11px;
      }
    }
    .right-radio-more {
      .ant-radio-button-wrapper {
        padding: 0 9px;
      }
    }
    .options-list {
      padding-bottom: 18px;
      .select-item {
        display: flex;
        border: 1px dashed @component-background;
        box-sizing: border-box;
        & .ant-input + .ant-input {
          margin-left: 4px;
        }
        .ant-select {
          width: 100%;
        }
        & + .select-item {
          margin-top: 4px;
        }
        &.sortable-chosen {
          border: 1px dashed @primary-color;
        }
        .select-line-icon {
          line-height: 31px;
          font-size: 22px;
          padding: 0 4px;
          color: #606266;
          .icon-ym-darg {
            font-size: 20px;
            line-height: 31px;
            display: inline-block;
          }
          .icon-ym-btn-clearn {
            font-size: 18px;
          }
        }
        .close-btn {
          cursor: pointer;
          color: @error-color;
          height: 32px;
          display: flex;
          align-items: center;
        }
        .option-drag {
          cursor: move;
        }
      }
      .add-btn {
        padding-left: 12px;
      }
    }
  }
}
.custom-table {
  .ant-table-header table {
    border-top: unset !important;
  }
}
.ant-modal.json-modal {
  .ant-modal-body {
    & > .scrollbar {
      padding: 20px;
    }
  }
  .ant-modal-body {
    .json-editor {
      height: 400px;
      height: 400px;
      border: 1px solid @border-color-base1;
      border-radius: 6px;
      overflow: hidden;
    }
  }
}
.portal-layout-nodata {
  text-align: center;
  position: absolute;
  top: calc(50% - 200px);
  left: calc(50% - 200px);

  .layout-nodata-img {
    width: 400px;
    height: 400px;
  }

  .layout-nodata-txt {
    margin-top: -60px;
    font-size: 20px;
    color: #909399;
    line-height: 30px;
  }
}
html[data-theme='dark'] {
  .portal-design-box .center-box .layout-area #ipad .outer-ipad {
    background: url('../../../assets/images/iphoneBg-dark.png');
  }
  .portal-design-box .right-box .filed-box .ant-collapse {
    border: unset;
  }
  .cup-box-content {
    background: rgba(51, 51, 51, 0.59) !important;
  }
  .top-bg {
    border-bottom: 9px solid rgba(51, 51, 51, 0.59) !important;
  }
  .jnpf-basic-portal {
    .portal-box-tab,
    .portal-card-box {
      .portal-box-item {
        border: 1px solid #303030;
      }
    }
  }
}
