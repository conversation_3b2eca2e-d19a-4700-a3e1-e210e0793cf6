<template>
  <a-card class="portal-card-box">
    <template #title v-if="activeData.title">
      <CardHeader :title="activeData.title" :card="activeData.card" />
    </template>
    <template #extra>
      <a-space>
        <router-link to="/extend/act/actItem">更多</router-link>
        <a-radio-group v-model:value="tabKey" button-style="solid">
          <a-radio-button value="1">我的行动项</a-radio-button>
          <a-radio-button value="2">未完成行动项</a-radio-button>
          <a-radio-button value="3">临期行动项</a-radio-button>
        </a-radio-group>
      </a-space>
    </template>
    <div class="todo-box-body">
      <MyActItemTable v-if="tabKey === '1'" />
      <DoingActItemTable v-if="tabKey === '2'" />
      <AlertActItemTable v-if="tabKey === '3'" />
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import CardHeader from '../CardHeader/index.vue';
import MyActItemTable from "./cube/MyActItemTable.vue";
import DoingActItemTable from "./cube/DoingActItemTable.vue";
import AlertActItemTable from "./cube/AlertActItemTable.vue";


const props = defineProps(['activeData']);
const tabKey = ref('1');
</script>

<style scoped>
</style>
