<template>
  <BasicTable @register="registerTable">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'urgentLevel'">
        <a-tag v-if="record.urgentLevel === '一般'" color="#2db7f5">一般</a-tag>
        <a-tag v-if="record.urgentLevel === '重要'" color="#FBAD14">重要</a-tag>
        <a-tag v-if="record.urgentLevel === '紧急'" color="#ff5500">紧急</a-tag>
      </template>

      <template v-if="column.key === 'actItemHisList'">
        <div v-for="his in record.actItemHisList" :key="his.id" class="fa-flex-row">
          <div class="fa-break-word fa-border-b" style="font-size: 14px; line-height: 16px; margin-bottom: 4px; padding-bottom: 4px;">{{formatToDate(his.actDate)}}: {{his.remark}}({{his.creatorUserName}})</div>
        </div>
        <a-space>
          <a-button type="link" size="small" @click="addHis(record)">新增进度</a-button>
          <a-button type="link" size="small" @click="addHisFlow(record)">新增进度更新申请</a-button>
        </a-space>
      </template>

      <template v-if="column.key === 'action'">
        <TableAction :actions="getTableActions(record)"/>
      </template>
    </template>

    <Form @register="registerForm" @reload="reload"/>
    <ActItemHisAddForm @register="registerHisForm" @reload="reload"/>
    <FaFlowCube ref="flowActItemHisAddRef" flow-en-code="act.actItemHisAdd" @reload="reload" />
    <FaFlowCube ref="flowActItemPostponeRef" flow-en-code="act.actItemFlowPostpone" @reload="reload" />
  </BasicTable>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from "/@/components/Table";
import { genDeleteBtn, genEditBtn, genQuerySearch } from "/@/utils/tableUtils";
import { DATE_TIME_FORMAT, formatToDate } from "/@/utils/dateUtil";
import { zzActItemApi as api } from "/@/api";
import { usePopup } from "/@/components/Popup";
import { useMessage } from "/@/hooks/web/useMessage";
import Form from "/@/views/extend/act/actItem/Form.vue";
import FaFlowCube from "/@/components/Fa/common/src/FaFlowCube.vue";
import ActItemHisAddForm from "/@/views/extend/act/actItem/ActItemHisAddForm.vue";
import dayjs from "dayjs";


const {createMessage} = useMessage();
const flowActItemHisAddRef = ref<any>();
const flowActItemPostponeRef = ref<any>();
const [registerForm, {openPopup: openFormModal}] = usePopup();
const [registerHisForm, {openPopup: openHisFormModal}] = usePopup();

const columns: BasicColumn[] = [
  {title: '编码', dataIndex: 'no', width: 100},
  {title: '来源', dataIndex: 'fromName', width: 100},
  {title: '紧急程度', dataIndex: 'urgentLevel', width: 80},
  {title: '主管部门', dataIndex: 'manageDeptName', width: 100},
  {title: '管理员', dataIndex: 'managerName', width: 100},
  {title: '提出时间', dataIndex: 'startDate', width: 100, format: 'date|YYYY-MM-DD'},
  {title: '完成时限要求', dataIndex: 'endReqDate', width: 100,
    customRender: ({record}) => record.longTrack ? '长期跟踪' : formatToDate(record.endReqDate),},
  {title: '名称', dataIndex: 'name', width: 300},
  {title: '进展情况', dataIndex: 'actItemHisList', width: 400},
  {title: '责任单位', dataIndex: 'name', width: 200,
    customRender: ({record}) => record.dutyDeptList.map(i => i.fullName).join(),},
  {title: '责任人', dataIndex: 'name', width: 200,
    customRender: ({record}) => record.dutyUserList.map(i => i.realName).join()},
  {title: '创建时间', dataIndex: 'creatorTime', width: 140, format: 'date|YYYY-MM-DD HH:mm'},
];

const start = dayjs().add(1, 'day').startOf('day').format(DATE_TIME_FORMAT)
const end = dayjs().add(3, 'day').endOf('day').format(DATE_TIME_FORMAT)
const searchInfo = reactive({
  status: '进行中',
  // 'endReqDate#$min': start,
  'endReqDate#$max': end,
  '_sorter': 'f_creator_time DESC', // 按照创建时间倒序排列
});
const [registerTable, {reload, setLoading, getForm}] = useTable({
  api: api.page,
  columns,
  searchInfo,
  useSearchForm: true,
  immediate: true,
  ellipsis: false,
  formConfig: {
    schemas: [
      genQuerySearch(),
    ],
  },
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record: any): ActionItem[] {
  return [
    genEditBtn(record, addOrUpdateHandle),
    {
      label: '延期',
      onClick: postponeHandle.bind(null, record),
      ifShow: () => !record.longTrack,
    },
    genDeleteBtn(record, handleDelete),
  ];
}

function addOrUpdateHandle(id = '') {
  openFormModal(true, {id});
}

function handleDelete(id: any) {
  api.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function addHis(record:any) {
  openHisFormModal(true, { actItemId: record.id});
}

// 新增进度更新申请
function addHisFlow(record:any) {
  flowActItemHisAddRef.value.handleAdd({ actItemId: record.id})
}

// 新增延期申请
function postponeHandle(record:any) {
  flowActItemPostponeRef.value.handleAdd({ actItemId: record.id})
}

</script>


<style scoped>

</style>
