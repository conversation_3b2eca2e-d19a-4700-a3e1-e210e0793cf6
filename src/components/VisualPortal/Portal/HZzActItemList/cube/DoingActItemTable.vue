<template>
  <BasicTable @register="registerTable">
  </BasicTable>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import { BasicColumn, BasicTable, useTable } from "/@/components/Table";
import { genQuerySearch } from "/@/utils/tableUtils";
import { formatToDate } from "/@/utils/dateUtil";
import { zzActItemApi as api } from "/@/api";

const columns: BasicColumn[] = [
  {title: '编码', dataIndex: 'no', width: 100},
  {title: '来源', dataIndex: 'fromName', width: 100},
  {title: '紧急程度', dataIndex: 'urgentLevel', width: 80},
  {title: '主管部门', dataIndex: 'manageDeptName', width: 100},
  {title: '管理员', dataIndex: 'managerName', width: 100},
  {title: '提出时间', dataIndex: 'startDate', width: 100, format: 'date|YYYY-MM-DD'},
  {title: '完成时限要求', dataIndex: 'endReqDate', width: 100,
    customRender: ({record}) => record.longTrack ? '长期跟踪' : formatToDate(record.endReqDate),},
  {title: '名称', dataIndex: 'name', width: 300},
  {title: '进展情况', dataIndex: 'actItemHisList', width: 400},
  {title: '责任单位', dataIndex: 'name', width: 200,
    customRender: ({record}) => record.dutyDeptList.map(i => i.fullName).join(),},
  {title: '责任人', dataIndex: 'name', width: 200,
    customRender: ({record}) => record.dutyUserList.map(i => i.realName).join()},
  {title: '创建时间', dataIndex: 'creatorTime', width: 140, format: 'date|YYYY-MM-DD HH:mm'},
];

const searchInfo = reactive({
  status: '进行中',
  '_sorter': 'f_creator_time DESC', // 按照创建时间倒序排列
});
const [registerTable, {reload, setLoading, getForm}] = useTable({
  api: api.page,
  columns,
  searchInfo,
  useSearchForm: true,
  immediate: true,
  ellipsis: false,
  formConfig: {
    schemas: [
      genQuerySearch(),
    ],
  },
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
  },
});

</script>


<style scoped>

</style>
