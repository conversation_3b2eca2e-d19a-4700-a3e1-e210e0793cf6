<script setup lang="ts">
import { ref, unref, useAttrs, watch } from 'vue';
import {Cascader, Form} from 'ant-design-vue';
import { useTree } from '/@/hooks/web/useTree';
import { FaTreeUtils } from '/@/utils/tree';
import { isEqual } from 'lodash-es';
import { watchImmediate } from '@vueuse/core';

interface Props {
  showRoot?: boolean;
  /** [外部定义]Tree节点标准API接口 */
  api: any;
  rootId?: KeyType;
  rootName?: string;
  disabledIds?: any[]; // 禁止选择的选项IDs
  maxLevel?: number; // 最大的展示层级，超过这个层级不展示
  value?: any;
}
const innerValue = ref<any[]>([]);

const props = defineProps<Props>();
const emit = defineEmits(['update:value', 'change']);
const { api, rootId, rootName, showRoot, disabledIds, maxLevel } = props;
const formItemContext = Form.useInjectFormItemContext();

defineOptions({ name: 'JnpfFaCascader', inheritAttrs: false });
const { value: attrValue, ...attrs } = useAttrs();

const { options } = useTree(api, { rootId, rootName, showRoot, disabledIds, maxLevel });

function getPathValue() {
  const path = FaTreeUtils.findPath(options.value, props.value, 'id');
  return path.map((d: any) => d.id);
}

function syncInnerValue() {
  const values = getPathValue();
  // console.log('isEqual', path, innerValue.value, values, isEqual(innerValue.value, values))
  if (!isEqual(innerValue.value, values)) {
    innerValue.value = values;
  }
}

watch(innerValue, (newValue) => {
  const lastValue = newValue && newValue.length > 0 ? newValue[newValue.length - 1] : undefined;
  // const lastItem = selectedOptions && selectedOptions.length > 0? selectedOptions[selectedOptions.length - 1] as Fa.TreeNode<RecordType, KeyType> : undefined;
  // console.log('FaCascader.vue innerValue change to', newValue, 'lastValue', lastValue)
  emit('update:value', lastValue);
  emit('change', lastValue);
  formItemContext.onFieldChange();
});

watch(options, syncInnerValue);

watchImmediate(() => props.value, syncInnerValue);
</script>

<template>
  <Cascader :options="options" :field-names="{ value: 'id', label: 'name' }" change-on-select v-bind="attrs" v-model:value="innerValue" />
</template>

<style scoped></style>
