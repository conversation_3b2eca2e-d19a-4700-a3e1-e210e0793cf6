import { withInstall } from '/@/utils';
import OrganizeSelect from './src/OrganizeSelect.vue';
import DepSelect from './src/DepSelect.vue';
import PosSelect from './src/PosSelect.vue';
import GroupSelect from './src/GroupSelect.vue';
import RoleSelect from './src/RoleSelect.vue';
import UserSelect from './src/UserSelect.vue';
import UsersSelect from './src/UsersSelect.vue';

export const JnpfOrganizeSelect = withInstall(OrganizeSelect);
export const JnpfDepSelect = withInstall(DepSelect);
export const JnpfPosSelect = withInstall(PosSelect);
export const JnpfGroupSelect = withInstall(GroupSelect);
export const JnpfRoleSelect = withInstall(RoleSelect);
export const JnpfUserSelect = withInstall(UserSelect);
export const JnpfUsersSelect = withInstall(UsersSelect);
