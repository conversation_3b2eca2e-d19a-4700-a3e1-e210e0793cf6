<template>
  <div class="upload-file-container" :class="$attrs.class">
    <template v-if="!detailed">
      <div style="display: flex;flex-direction: column;">
        <a-space>
          <a-upload ref="fileUploaderRef" v-bind="getBindValue" :before-upload="onFileAdded" :custom-request="customRequest" :show-upload-list="false">
            <a-button ref="uploaderBtnRef" preIcon="icon-ym icon-ym-btn-upload" :disabled="disabled">{{ buttonText }}（内网）</a-button>
          </a-upload>
          <a-upload ref="fileUploaderRef" v-bind="getBindValue" :before-upload="onFileAdded" :custom-request="customRequestQiniu" :show-upload-list="false">
            <a-button ref="uploaderBtnRef" preIcon="icon-ym icon-ym-btn-upload" :disabled="disabled">{{ buttonText }}（外网）</a-button>
          </a-upload>
        </a-space>
<!--        <a target="_blank" href="http://doc.dward.cn/open/dm/doc/view/lghk?chapter=380">内网需配置host进行七牛云直传</a>-->

        <div class="fa-flex-row">
          <div v-for="item in imgList" :key="item.fileId">
            <a-image :src="item.url" :height="100" :width="100" />
          </div>
        </div>
      </div>
    </template>

    <div class="upload-file-list" :class="{ 'upload-file-list__simple': simple }" v-if="showUploadList">
      <a-progress v-if="percentRef > 0 && percentRef < 100" :percent="percentRef" :stroke-width="2" />
      <span>{{ (percentRef === 100 && serverSendingRef) ? '服务器转存云存储种，大文件转存耗时较长，请耐心等待，不要关闭页面...' : '' }}</span>
      <div class="upload-file-list__item" v-for="(file, index) in fileList" :key="file.fileId" :class="{ 'upload-file-list__item_detail': detailed }">
        <a
          class="upload-file-list__item-name"
          :title="file.name + (file.fileSize && !simple ? '(' + toFileSize(file.fileSize) + ')' : '')"
          @click="handlePreview(file)">
          <PaperClipOutlined v-if="showIcon"/>
          {{ file.name }}{{ file.fileSize && !simple ? `(${toFileSize(file.fileSize)})` : '' }}
        </a>
        <span class="upload-file-list__item-actions">
          <EyeOutlined title="查看" @click="handlePreview(file)" v-if="showView"/>
          <DownloadOutlined title="下载" @click="handleDownload(file)" v-if="showDownload"/>
          <CloseOutlined title="删除" @click="handleRemove(index)" v-show="!disabled && !detailed"/>
        </span>
      </div>
    </div>
    <FaFilePreview ref="filePreviewRef" :type="type" :showDownload="simple"/>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, unref, watch } from 'vue';
import { Form } from 'ant-design-vue';
import { CloseOutlined, DownloadOutlined, EyeOutlined, PaperClipOutlined } from '@ant-design/icons-vue';
import { fileItem, uploadFileQiniuProps } from './props';
import { useMessage } from '/@/hooks/web/useMessage';
import { toFileSize } from '/@/utils/jnpf';
import FaFilePreview from './FaFilePreview.vue';
import { fetchUploadFileLocal, fetchUploadFileQiniu, imgTypeList, zipTypeList } from './helper';
import { fileSaveApi } from '/@/api';


defineOptions({name: 'JnpfUploadFileQiniu', inheritAttrs: false});
const props = defineProps(uploadFileQiniuProps);
const emit = defineEmits(['update:value', 'change']);
const {createMessage} = useMessage();
const fileList = ref<fileItem[]>([]);
const percentRef = ref<number>(-1);
const serverSendingRef = ref<boolean>(false);

const filePreviewRef = ref<any>(null);
const fileUploaderRef = ref<any>(null);
const uploaderBtnRef = ref<any>(null);
const formItemContext = Form.useInjectFormItemContext();

const getBindValue = computed(() => ({...props}));

watch(
  () => props.value,
  val => {
    fileList.value = val && Array.isArray(val) ? val : [];
  },
  {
    deep: true,
    immediate: true,
  },
);

defineExpose({uploadFile});

const imgList = computed(() => {
  return fileList.value.filter(i => imgTypeList.includes(i.ext || ''))
})

function tryToFixed(value: any, num = 2): number {
  if (value === undefined) {
    return 0;
  }
  if (typeof value === 'number') {
    return Number(value.toFixed(num));
  }
  if (typeof value === 'string') {
    return Number(Number(value).toFixed(num));
  }
  return Number(value);
}

function onFileAdded(file: any) {
  console.log('onFileAdded', file)
  // if (file.name.indexOf(' ') > -1) {
  //   createMessage.error('文件名不能包含空格');
  //   return false;
  // }
  if (file.size > (500 * 1024 * 1024)) {
    createMessage.info('文件大小超过500M，可能影响app端正常播放，建议压缩后上传');
    // return false;
  }
}

function customRequest(req: any) {
  // console.log('file', req)
  const {file, onError, onProgress, onSuccess} = req;
  // setLoading(true);
  serverSendingRef.value = true;
  fetchUploadFileLocal(
    file,
    props.prefix,
    file.name,
    (path, res) => {
      console.log('UploadFileQiniu.vue#onSuccess', path, res)
      serverSendingRef.value = false;
      // if (onChange) onChange(path);
      onSuccess({...res, path}, file);

      fileList.value.push({
        id: res.id,
        fileId: res.id,
        name: file.name,
        url: fileSaveApi.getFileLocal(res.id),
        fullUrl: res.url,
        fileSize: file.size,
        duration: res.audioDuration,
        ext: res.ext,
      });
      emit('update:value', unref(fileList));
      emit('change', unref(fileList));
      formItemContext.onFieldChange();
    },
    (res) => {
      const percent = res.progress * 100;
      onProgress({percent}, file);
      percentRef.value = tryToFixed(percent, 0);
    },
    (res) => {
      onError(new Error(res), file);
    }
  );
}


function customRequestQiniu(req: any) {
  // console.log('file', req)
  const {file, onError, onProgress, onSuccess} = req;
  // setLoading(true);
  fetchUploadFileQiniu(
    false,
    file,
    props.prefix,
    file.name,
    (path, res) => {
      console.log('UploadFileQiniu.vue#onSuccess', path, res)
      // if (onChange) onChange(path);
      onSuccess({...res, path}, file);

      // save file record by url
      fileSaveApi.uploadFromUrlQiniu(path).then(res => {
        fileList.value.push({
          id: res.data.id,
          fileId: res.data.id,
          name: file.name,
          url: fileSaveApi.getFileLocal(res.data.id),
          fullUrl: res.data.url,
          fileSize: file.size,
          duration: res.data.audioDuration,
          ext: res.data.ext,
        });
        emit('update:value', unref(fileList));
        emit('change', unref(fileList));
        formItemContext.onFieldChange();
      })
    },
    (res) => {
      const {percent} = res.total;
      onProgress({percent}, file);
      percentRef.value = tryToFixed(percent, 0);
    },
    (res) => {
      onError(new Error(res), file);
    }
  );
}

function handleSimplePreview(file: fileItem) {
  if (!props.simple) return;
  handlePreview(file);
}

function handlePreview(file: fileItem) {
  // console.log('handlePreview', file)
  if (zipTypeList.includes(file.fileExtension || '')) {
    createMessage.error(`压缩包不能预览`);
    return;
  }
  // 文件预览
  filePreviewRef.value?.init(file);
}

function handleDownload(file: fileItem) {
  window.open(file.url, '_blank')
}

function handleRemove(index: number) {
  fileList.value.splice(index, 1);
  emit('update:value', unref(fileList));
  emit('change', unref(fileList));
  formItemContext.onFieldChange();
}

function uploadFile() {
  console.log('uploadFile.1111')
  uploaderBtnRef.value.$el.click();
}
</script>

<style lang="less" scoped>
.upload-file-container {
  .link-text {
    float: right;
    line-height: 32px;

    .anticon {
      margin-right: 2px;
    }
  }

  .upload-file__tip {
    line-height: 1.2;
    color: @text-color-secondary;
    margin-top: 5px;
    font-size: 12px;
    word-break: break-all;
  }

  .upload-file-list {
    &.upload-file-list__simple {
      .upload-file-list__item {
        color: @primary-color;

        &:first-child {
          margin-top: 0 !important;
        }

        &:hover {
          background-color: unset !important;
        }

        .upload-file-list__item-name {
          margin-right: 0;
          padding-left: 0;

          .anticon {
            display: none;
          }
        }

        .anticon-eye,
        .anticon-download,
        .anticon-close {
          display: none;
        }
      }
    }

    .upload-file-list__item {
      font-size: 14px;
      color: @text-color-label;
      line-height: 26px;
      margin-top: 5px;
      position: relative;
      box-sizing: border-box;
      border-radius: 4px;
      width: 100%;

      a {
        color: inherit;
      }

      &:first-child {
        margin-top: 10px;
      }

      &:hover {
        background-color: @selected-hover-bg;

        .upload-file-list__item-name {
          color: @primary-color;
        }
      }

      &.upload-file-list__item_detail:first-child {
        margin-top: 0 !important;
      }

      .upload-file-list__item-name {
        margin-right: 70px;
        display: block;
        overflow: hidden;
        padding-left: 4px;
        text-overflow: ellipsis;
        white-space: nowrap;

        .anticon {
          height: 100%;
          margin-right: 5px;
          color: #909399;
        }
      }

      .anticon-eye {
        display: inline-block;
        position: absolute;
        top: 6px;
        right: 45px;
        cursor: pointer;
        opacity: 0.75;
        color: @text-color-label;
      }

      .anticon-download {
        display: inline-block;
        position: absolute;
        top: 6px;
        right: 25px;
        cursor: pointer;
        opacity: 0.75;
        color: @text-color-label;
      }

      .anticon-close {
        display: inline-block;
        position: absolute;
        top: 6px;
        right: 5px;
        cursor: pointer;
        opacity: 0.75;
        color: @text-color-label;
      }
    }
  }

  .list-enter-active,
  .list-leave-active {
    transition: all 1s ease;
  }

  .list-enter-from,
  .list-leave-to {
    opacity: 0;
    transform: translateY(-30px);
  }
}
</style>
