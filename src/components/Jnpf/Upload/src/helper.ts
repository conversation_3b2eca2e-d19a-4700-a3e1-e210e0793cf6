// import { get, isNil } from 'lodash-es';
import dayjs from 'dayjs';
import { fileSaveApi } from '/@/api'


export function checkFileType(file: File, accepts: string[]) {
  const newTypes = accepts.join('|');
  // const reg = /\.(jpg|jpeg|png|gif|txt|doc|docx|xls|xlsx|xml)$/i;
  const reg = new RegExp('\\.(' + newTypes + ')$', 'i');

  return reg.test(file.name);
}

export function checkImgType(file: File) {
  return isImgTypeByName(file.name);
}

export function isImgTypeByName(name: string) {
  return /\.(jpg|jpeg|png|gif|webp)$/i.test(name);
}

export function getBase64WithFile(file: File) {
  return new Promise<{
    result: string;
    file: File;
  }>((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve({ result: reader.result as string, file });
    reader.onerror = error => reject(error);
  });
}

/**
 * 上传文件到七牛云（这个方法上传后台服务器，后台服务器再上传到七牛云）
 * @param file
 * @param prefix
 * @param fileName
 * @param onFinish
 * @param onProgress
 * @param onError
 */
export async function fetchUploadFileLocal(
  file: any,
  prefix?: string,
  fileName?: string,
  onFinish?: (path: string, res: any) => void,
  onProgress?: (res: any) => void,
  onError?: (res: any) => void,
) {
  fileSaveApi.uploadFile(file, progressEvent => {
    // console.log('progressEvent', progressEvent)
    if (onProgress) onProgress(progressEvent)
  })
    .then(res => {
      if (onFinish) onFinish(fileSaveApi.getFileLocal(res.data.id), res.data);
    })
    .catch(e => {
      if (onError) onError(e)
    })
}

/**
 * 上传文件到七牛云（这个使用前端直接上传到七牛云服务器，不走后台服务器中转）
 * @param {*} file
 * @param {*} prefix 自定义前缀，如：avatar（注：结尾不要加斜杠/）
 * @param {*} fileName
 * @param {*} onFinish
 * @param {*} onProgress
 * @param {*} onError
 */
export async function fetchUploadFileQiniu(
  update:boolean,
  file: any,
  prefix: string,
  fileName: string,
  onFinish?: (path: string, res: any) => void,
  onProgress?: (res: any) => void,
  onError?: (res: any) => void,
) {
  const response = await fileSaveApi.getUploadTokenQiniu();
  // if (!response || response.status !== Fa.RES_CODE.OK) {
  //   message.error('上传文件失败，获取上传token失败，请联系管理员！');
  //   return;
  // }
  const { token, domain, basePath,updatePath } = response.data;
  const day = dayjs().format('YYYY-MM-DD');
  const time = dayjs().format('YYYYMMDDHHmmss');

  let fName = fileName;
  if (fileName.indexOf('.') > -1) {
    const dotIndex = fileName.lastIndexOf('.');
    fName = fileName.substr(0, dotIndex) + '_' + time + fileName.substr(dotIndex);
  } else {
    fName = fileName + '_' + time;
  }

  let key: string = '';
  if (update){
    key = `${updatePath}${prefix}/${day}/${fName}`;
  }else {
    key = `${basePath}${prefix}/${day}/${fName}`;
  }
  const putExtra = { fname: file.name, params: {}, mimeType: null };

  const config = {
    // useCdnDomain: true,
    // region: region,
  };
  const observable = window.qiniu.upload(file, key, token, putExtra, config);
  observable.subscribe(
    (res: any) => {
      // console.log('next', res);
      if (onProgress) onProgress(res);
    },
    (res: any) => {
      // console.log('error', res);
      if (onError) onError(res);
    },
    (res: any) => {
      // console.log('complete', res);
      const path = `${domain}${res.key}`;
      if (onFinish) onFinish(path, res);
    },
  );
}

// const videoTypeList = ['avi', 'wmv', 'mpg', 'mpeg', 'mov', 'rm', 'ram', 'swf', 'flv', 'mp4', 'mp3', 'wma', 'avi', 'rm', 'rmvb', 'flv', 'mpg', 'mkv'];
export const videoTypeList = ['avi', 'wmv', 'mpg', 'mpeg', 'mov', 'rm', 'ram', 'swf', 'flv', 'mp3', 'wma', 'avi', 'rm', 'rmvb', 'flv', 'mpg', 'mkv'];
export const imgTypeList = ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp'];
export const zipTypeList = ['rar', 'zip', 'arj', 'z', '7z'];
