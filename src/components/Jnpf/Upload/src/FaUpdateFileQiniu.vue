<template>
  <div class="upload-file-container" :class="$attrs.class">
    <template v-if="!detailed">
      <div style="display: flex;flex-direction: column;">
        <a-space>
          <a-upload ref="fileUploaderRef" v-bind="getBindValue" :before-upload="onFileAdded" :custom-request="customRequestQiniu" :show-upload-list="false">
            <a-button ref="uploaderBtnRef" preIcon="icon-ym icon-ym-btn-upload" :disabled="disabled">{{ buttonText }}</a-button>
          </a-upload>
        </a-space>
      </div>
    </template>

    <div class="upload-file-list" :class="{ 'upload-file-list__simple': simple }" v-if="showUploadList">
      <a-progress v-if="percentRef > 0 && percentRef < 100" :percent="percentRef" :stroke-width="2" />
      <span>{{ (percentRef === 100 && serverSendingRef) ? '服务器转存云存储种，大文件转存耗时较长，请耐心等待，不要关闭页面...' : '' }}</span>
      <div class="upload-file-list__item" v-for="(file, index) in fileList" :key="file.fileId" :class="{ 'upload-file-list__item_detail': detailed }">
        <a
          class="upload-file-list__item-name"
          :title="file.name + (file.fileSize && !simple ? '(' + toFileSize(file.fileSize) + ')' : '')"
          @click="handlePreview(file)">
          <PaperClipOutlined v-if="showIcon"/>
          {{ file.name }}{{ file.fileSize && !simple ? `(${toFileSize(file.fileSize)})` : '' }}
        </a>
        <span class="upload-file-list__item-actions">
          <EyeOutlined title="查看" @click="handlePreview(file)" v-if="showView"/>
          <DownloadOutlined title="下载" @click="handleDownload(file)" v-if="showDownload"/>
          <CloseOutlined title="删除" @click="handleRemove(index)" v-show="!disabled && !detailed"/>
        </span>
      </div>
    </div>
    <FaFilePreview ref="filePreviewRef" :type="type" :showDownload="simple"/>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, unref, watch } from 'vue';
import { isNil } from 'lodash-es';
import { Form } from 'ant-design-vue';
import { CloseOutlined, DownloadOutlined, EyeOutlined, PaperClipOutlined } from '@ant-design/icons-vue';
import { fileItem, uploadFileQiniuProps } from './props';
import { useMessage } from '/@/hooks/web/useMessage';
import { toFileSize } from '/@/utils/jnpf';
import FaFilePreview from './FaFilePreview.vue';
import { fetchUploadFileLocal, fetchUploadFileQiniu, imgTypeList, zipTypeList } from './helper';
import { fileSaveApi } from '/@/api';


defineOptions({name: 'JnpfFaUpdateFileQiniu', inheritAttrs: false});
const props = defineProps(uploadFileQiniuProps);
const emit = defineEmits(['update:value', 'change', 'success']);
const {createMessage} = useMessage();
const fileList = ref<fileItem[]>([]);
const percentRef = ref<number>(-1);
const serverSendingRef = ref<boolean>(false);

const filePreviewRef = ref<any>(null);
const fileUploaderRef = ref<any>(null);
const uploaderBtnRef = ref<any>(null);
const formItemContext = Form.useInjectFormItemContext();

const { value, ...restProps} = props
const getBindValue = computed(() => ({...restProps}));
const buttonText = ref('更新视频')

watch(
  () => props.value,
  val => {
    if (isNil(val)) {
      fileList.value = [];
      return;
    }
    const ids = Array.isArray(val) ? val : [ val ];
    fileSaveApi.getByIds(ids).then(res => {
      fileList.value = res.data.map(i => ({
        id: i.id,
        fileId: i.id,
        name: i.originalFilename,
        url: fileSaveApi.getFileLocal(i.id),
        fileSize: i.size,
        ext: i.ext,
      }))
    })
  },
  {
    deep: true,
    immediate: true,
  },
);

defineExpose({uploadFile});

const imgList = computed(() => {
  return fileList.value.filter(i => imgTypeList.includes(i.ext || ''))
})

function tryToFixed(value: any, num = 2): number {
  if (value === undefined) {
    return 0;
  }
  if (typeof value === 'number') {
    return Number(value.toFixed(num));
  }
  if (typeof value === 'string') {
    return Number(Number(value).toFixed(num));
  }
  return Number(value);
}

function onFileAdded(file: any) {
  // console.log('onFileAdded', file)
  // if (file.name.indexOf(' ') > -1) {
  //   createMessage.error('文件名不能包含空格');
  //   return false;
  // }
}

function customRequestQiniu(req: any) {
  // console.log('file', req)
  const {file, onError, onProgress, onSuccess} = req;
  // setLoading(true);
  fetchUploadFileQiniu(
    true,
    file,
    props.prefix,
    file.name,
    (path, res) => {
      console.log('UploadFileQiniu.vue#qiniu#onSuccess', path, res)
      // if (onChange) onChange(path);
      onSuccess({...res, path}, file);
      // save file record by url
      fileSaveApi.uploadFromUrlQiniu(path).then(res => {
          // if (props.multiple) {
          //   fileList.value.push({
          //     id: res.data.id,
          //     fileId: res.data.id,
          //     name: file.name,
          //     url: fileSaveApi.getFileLocal(res.data.id),
          //     fullUrl: res.data.url,
          //     fileSize: file.size,
          //     duration: res.data.audioDuration,
          //   });
          // } else {
            fileList.value = [{
              id: res.data.id,
              fileId: res.data.id,
              name: file.name,
              url: fileSaveApi.getFileLocal(res.data.id),
              fullUrl: res.data.url,
              fileSize: file.size,
              duration: res.data.audioDuration,
            }];
        emit('success', fileList.value);

        // }
          // //   handleFileListChange()
        })
    },
    (res) => {
      const {percent} = res.total;
      onProgress({percent}, file);
      percentRef.value = tryToFixed(percent, 0);
    },
    (res) => {
      onError(new Error(res), file);
    }
  );
}

function handleSimplePreview(file: fileItem) {
  if (!props.simple) return;
  handlePreview(file);
}

function handlePreview(file: fileItem) {
  if (zipTypeList.includes(file.fileExtension || '')) {
    createMessage.error(`压缩包不能预览`);
    return;
  }
  // 文件预览
  filePreviewRef.value?.init(file);
}

function handleDownload(file: fileItem) {
  window.open(file.url, '_blank')
}

function handleRemove(index: number) {
  fileList.value.splice(index, 1);
  handleFileListChange()
}

function handleFileListChange() {
  if (props.multiple) {
    const ids = fileList.value.map(i => i.fileId)
    emit('update:value', unref(ids));
    emit('change', unref(ids));
  } else {
    const id = fileList.value && fileList.value.length > 0 ? fileList.value[0].fileId : undefined;
    emit('update:value', unref(id));
    emit('change', unref(id));
  }
  formItemContext.onFieldChange();
}

function uploadFile() {
  uploaderBtnRef.value.$el.click();
}

</script>

<style lang="less" scoped>
.upload-file-container {
  .link-text {
    float: right;
    line-height: 32px;

    .anticon {
      margin-right: 2px;
    }
  }

  .upload-file__tip {
    line-height: 1.2;
    color: @text-color-secondary;
    margin-top: 5px;
    font-size: 12px;
    word-break: break-all;
  }

  .upload-file-list {
    &.upload-file-list__simple {
      .upload-file-list__item {
        color: @primary-color;

        &:first-child {
          margin-top: 0 !important;
        }

        &:hover {
          background-color: unset !important;
        }

        .upload-file-list__item-name {
          margin-right: 0;
          padding-left: 0;

          .anticon {
            display: none;
          }
        }

        .anticon-eye,
        .anticon-download,
        .anticon-close {
          display: none;
        }
      }
    }

    .upload-file-list__item {
      font-size: 14px;
      color: @text-color-label;
      line-height: 26px;
      margin-top: 5px;
      position: relative;
      box-sizing: border-box;
      border-radius: 4px;
      width: 100%;

      a {
        color: inherit;
      }

      &:first-child {
        margin-top: 10px;
      }

      &:hover {
        background-color: @selected-hover-bg;

        .upload-file-list__item-name {
          color: @primary-color;
        }
      }

      &.upload-file-list__item_detail:first-child {
        margin-top: 0 !important;
      }

      .upload-file-list__item-name {
        margin-right: 70px;
        display: block;
        overflow: hidden;
        padding-left: 4px;
        text-overflow: ellipsis;
        white-space: nowrap;

        .anticon {
          height: 100%;
          margin-right: 5px;
          color: #909399;
        }
      }

      .anticon-eye {
        display: inline-block;
        position: absolute;
        top: 6px;
        right: 45px;
        cursor: pointer;
        opacity: 0.75;
        color: @text-color-label;
      }

      .anticon-download {
        display: inline-block;
        position: absolute;
        top: 6px;
        right: 25px;
        cursor: pointer;
        opacity: 0.75;
        color: @text-color-label;
      }

      .anticon-close {
        display: inline-block;
        position: absolute;
        top: 6px;
        right: 5px;
        cursor: pointer;
        opacity: 0.75;
        color: @text-color-label;
      }
    }
  }

  .list-enter-active,
  .list-leave-active {
    transition: all 1s ease;
  }

  .list-enter-from,
  .list-leave-to {
    opacity: 0;
    transform: translateY(-30px);
  }
}
</style>
