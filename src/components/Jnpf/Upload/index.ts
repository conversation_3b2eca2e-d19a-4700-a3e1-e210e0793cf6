import { withInstall } from '/@/utils';
import UploadBtn from './src/UploadBtn.vue';
import UploadFile from './src/UploadFile.vue';
import UploadImg from './src/UploadImg.vue';
import UploadImgSingle from './src/UploadImgSingle.vue';
import UploadFileQiniu from './src/UploadFileQiniu.vue';
import FaUploadFileQiniu from './src/FaUploadFileQiniu.vue';

export const JnpfUploadBtn = withInstall(UploadBtn);
export const JnpfUploadFile = withInstall(UploadFile);
export const JnpfUploadImg = withInstall(UploadImg);
export const JnpfUploadImgSingle = withInstall(UploadImgSingle);
export const JnpfUploadFileQiniu = withInstall(UploadFileQiniu);
export const JnpfFaUploadFileQiniu = withInstall(FaUploadFileQiniu);
