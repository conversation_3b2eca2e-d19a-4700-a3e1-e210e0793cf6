<script setup lang="ts">
import { onMounted, ref, useAttrs } from 'vue';
import { Select } from 'ant-design-vue';
import { useBaseStore } from '/@/store/modules/base';
import { watchImmediate } from "@vueuse/core";
import { trim } from "lodash-es";

interface Props {
  dictName: string; // 字典名称
  value?: any;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:value', 'change']);
const { dictName } = props;

defineOptions({ name: 'JnpfFaDictSelect', inheritAttrs: false });
const { value: attrValue, ...attrs } = useAttrs();

const baseStore = useBaseStore();

const innerValue = ref<string>();
const options = ref<any[]>([]);

async function init() {
  options.value = (await baseStore.getDictionaryData(dictName)) as any[];
}

watchImmediate(() => props.value, setValue);

function setValue(value) {
  innerValue.value = trim(value) !== '' ? `${value}` : undefined;
}
function onChange(val, option) {
  emit('update:value', val);
  emit('change', val, option);
}

onMounted(() => {
  init()
})
</script>

<template>
  <Select :options="options" :field-names="{ value: 'enCode', label: 'fullName' }" v-bind="attrs" v-model:value="innerValue" @change="onChange" />
</template>

<style scoped></style>
