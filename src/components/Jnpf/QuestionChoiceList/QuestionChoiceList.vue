<template>
  <div class="fa-question-choice-list">
    <draggable v-model="innerValue" :animation="300" group="selectItem" handle=".option-drag" itemKey="value">
      <template #item="{ element, index }">
        <div class="fa-select-item">
          <div class="select-line-icon option-drag">
            <i class="icon-ym icon-ym-darg" />
          </div>
          <a-input v-model:value="element.value" placeholder="选项名" class="fa-question-choice-item-value" />
          <a-input v-model:value="element.option" placeholder="选项值" class="fa-question-choice-item-option" />
          <div class="close-btn select-line-icon" @click="delItem(index)">
            <i class="icon-ym icon-ym-btn-clearn" />
          </div>
        </div>
      </template>
    </draggable>

    <div class="add-btn">
      <a-button type="link" preIcon="icon-ym icon-ym-btn-add" @click="addItem" class="!px-0">添加新选项</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import draggable from 'vuedraggable';
import { ref, watch } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { Form } from 'ant-design-vue';
import { QuestionChoice, QuestionChoiceProps } from "./props";

defineOptions({ name: 'JnpfQuestionChoiceList', inheritAttrs: false });
const props = defineProps(QuestionChoiceProps);
const emit = defineEmits(['update:value', 'change']);
const innerValue = ref<QuestionChoice[]>([]);
const formItemContext = Form.useInjectFormItemContext();

const debounceOnChange = useDebounceFn(value => {
  emit('change', value);
}, 200);


watch(
  () => props.value,
  val => {
    innerValue.value = val;
  },
  { immediate: true },
);

/**
 * 添加新选项
 */
function addItem() {
  const nextId = innerValue.value.length + 1;
  // const newValue = [
  //   ...innerValue.value,
  //   { value: `${nextId}`, option: `选项${nextId}` }
  // ]

  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'; // 所有大写英文字母
  // 使用 nextId - 1 作为索引来获取对应的大写英文字母
  const letterId = alphabet[nextId - 1].toUpperCase(); // 确保字母是大写的

  const newValue = [
    ...innerValue.value,
    { value: letterId, option: `选项${nextId}` }
  ]

  emit('update:value', newValue);
  emit('change', newValue);
  formItemContext.onFieldChange();
  debounceOnChange(newValue);
}

function delItem(index) {
  innerValue.value.splice(index, 1)
  emit('update:value', innerValue.value);
  emit('change', innerValue.value);
  formItemContext.onFieldChange();
}
</script>

<style lang="less" scoped>
@import url('./faSelect.less');
</style>
