<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @ok="handleSubmit" title="二维码签名">
    <div class="container">
      <canvas id="qrcode" ref="qrCodeRef"></canvas>
      <p class="tips">使用数智协同App扫一扫</p>
      <p class="tips">在App上添加签名成功后，再关闭此弹框</p>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal';
import { toCanvas } from "qrcode";
import { ref } from "vue";

const emit = defineEmits(['register', 'reload']); // 需要暴露的事件
const [registerModal, { closeModal }] = useModalInner(init);
const qrCodeRef = ref();

function init(data: any) {
  // 打印外部传给modal的参数
  console.log('init', data)
  getQrcode()
}

function getQrcode() {
  const qrcode = JSON.stringify({ t:'addSign' })
  toCanvas(qrCodeRef.value, qrcode, {
    margin: 0,
    width: 265,
  });
}

async function handleSubmit() {
  closeModal();
  emit('reload'); // 发布reload事件，外部组件接受此事件
}
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
#qrcode {
  width: 265px;
  height: 265px;
  border: 1px solid @border-color-base1;
}
.tips {
  padding: 8px 0;
  color: @text-color-label;
}
</style>
