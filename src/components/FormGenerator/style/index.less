@prefix-cls: ~'@{namespace}-basic-generator';

.@{prefix-cls} {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  .common-board {
    height: 100%;
    border-radius: 4px;
    overflow: hidden;
  }
  .left-board {
    width: 250px;
    flex-shrink: 0;
    .components-list {
      box-sizing: border-box;
      height: 100%;

      .components-part {
        background-color: @component-background;
        border-radius: 4px;
        padding: 10px 10px 0;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }
      }
      .components-title {
        font-size: 14px;
        line-height: 30px;
        margin-bottom: 10px;
        font-weight: bold;
      }

      .components-draggable {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }

      .components-item {
        width: 110px;
        margin-bottom: 10px;
        transition: transform 0ms !important;

        &.disabled {
          .components-body {
            cursor: not-allowed;
            color: @text-color-secondary;
            &:hover {
              border: 1px dashed @primary-1;
              color: @text-color-secondary;
            }
          }
        }
        .components-body {
          padding-left: 8px;
          background: @primary-1;
          font-size: 12px;
          height: 36px;
          cursor: move;
          border: 1px dashed @primary-1;
          border-radius: 3px;
          line-height: 36px;
          display: flex;
          align-items: center;
          color: @text-color;
          i {
            line-height: 16px;
            height: 16px;
            margin-right: 4px;
          }
          &:hover {
            border: 1px dashed @primary-color;
            color: @primary-color;
          }
        }
      }
    }
  }
  .center-board {
    flex: 1;
    margin: 0 10px;
    background-color: @component-background;
    box-sizing: border-box;
    .action-bar {
      position: relative;
      height: 42px;
      text-align: center;
      padding: 0 15px;
      box-sizing: border-box;
      border-bottom: 1px solid @border-color-base1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .ant-btn {
        padding: 0;
        margin-left: 15px;
      }

      .unActive-btn {
        color: @text-color !important;

        &:hover {
          color: @primary-color !important;
        }
      }
      .action-bar-left,
      .action-bar-right {
        display: flex;
        align-items: center;
      }
      .undo-btn {
        margin-right: 15px;
        .ant-btn {
          margin-left: 0;
          .icon-ym {
            font-size: 20px;
          }
        }
      }
    }
  }
  .center-board-main {
    height: calc(100% - 42px);
    overflow: hidden;
    box-sizing: border-box;
    .scrollbar__view {
      padding: 10px;
    }
    .center-board-row {
      & > .ant-form {
        height: calc(100vh - 150px);
        width: 100%;
      }
    }
    .empty-info {
      position: absolute;
      top: 20%;
      left: calc(50% - 250px);
      pointer-events: none;
      &.app-empty-info {
        top: calc(50% - 150px);
        left: calc(50% - 150px);
        .empty-img {
          width: 300px;
          height: 300px;
        }
      }
      .empty-img {
        width: 500px;
        height: 500px;
      }
    }
    .drawing-board {
      height: 100%;
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      .components-body {
        padding: 0;
        margin: 0;
        font-size: 0;
      }
      .sortable-ghost {
        position: relative;
        display: block;
        overflow: hidden;
        i {
          display: none;
        }
        &::before {
          content: ' ';
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          height: 3px;
          background: @primary-color;
          z-index: 2;
        }
        .drawing-item-copy,
        .drawing-item-delete {
          display: none !important;
        }
      }
      .components-item.sortable-ghost {
        width: 100%;
        height: 60px;
        background-color: @primary-1;
      }
      .drawing-item {
        position: relative;
        cursor: move;
        .ant-form-item {
          border: 1px dashed @border-color-base;
          padding: 10px;
          margin-bottom: 10px !important;
        }
      }
      .drawing-row-item {
        position: relative;
        cursor: move;
        box-sizing: border-box;
        border: 1px dashed @border-color-base;
        padding: 0 2px;
        margin-bottom: 10px;
        &.drawing-row-item-table-grid {
          padding-top: 10px;
          padding-bottom: 10px;
        }
        .drawing-item-copy {
          right: 48px !important;
        }
        .drawing-item-delete {
          right: 16px !important;
        }
        .ant-card {
          width: 100%;
          .ant-card-body {
            & > .ant-row {
              width: 100%;
            }
          }
        }
        .ant-tabs {
          width: 100%;
          .ant-tabs-nav {
            margin-bottom: 0;
          }
        }
        .ant-collapse {
          width: 100%;
        }
        .child-drawing-row {
          position: relative;
        }
        &.drawing-row-item-row {
          position: relative;
          & > .ant-col {
            width: 100%;
          }
          .row-tip {
            top: 50px;
          }
          .drag-wrapper {
            min-height: 100px;
          }
        }
        &.drawing-row-item-table {
          .row-tip {
            top: 50px;
          }
          .ant-form {
            width: 100%;
          }
          .drag-wrapper {
            min-height: 100px;
            padding-top: 30px;
          }
          .table-wrapper-web {
            overflow: auto hidden;
            display: flex;
            width: 100%;
            flex-wrap: nowrap;
            padding-bottom: 10px;

            & > .ant-col {
              width: 200px !important;
              flex: none;
              flex-shrink: 0;
              height: auto;
              .ant-row {
                display: block;
              }
              .ant-form-item {
                margin-bottom: 0 !important;
                .ant-form-item-label {
                  width: auto !important;
                }
              }
            }
          }
        }
        .drawing-row-item {
          margin-bottom: 2px;
        }
        .drag-wrapper {
          width: 100%;
          min-height: 80px;
          display: flex;
          flex-wrap: wrap;
          align-content: flex-start;
          &.tableGrid-app-wrapper {
            .row-tip {
              margin-top: 20px;
            }
          }
        }
        &.active-from-item {
          border: 1px solid @primary-color;
        }
        .component-name {
          position: absolute;
          top: 0;
          left: 0;
          font-size: 18px;
          color: @text-color-secondary;
          display: inline-block;
          padding: 0 6px;
        }
      }
      .drawing-item,
      .drawing-row-item {
        &:hover {
          & > .ant-form-item {
            background: @primary-1;
          }

          & > .drawing-item-copy,
          & > .drawing-item-delete,
          & > .drawing-item-add-row,
          & > .drawing-item-add-col,
          & > .drawing-item-cell {
            display: block;
          }
        }

        & > .drawing-item-copy,
        & > .drawing-item-delete,
        & > .drawing-item-add-row,
        & > .drawing-item-add-col,
        & > .drawing-item-cell {
          display: none;
          position: absolute;
          top: -10px;
          width: 22px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-radius: 50%;
          font-size: 12px;
          border: 1px solid;
          cursor: pointer;
          z-index: 1;
          .anticon {
            vertical-align: top !important;
            margin-top: 5px;
          }
        }

        & > .drawing-item-copy,
        & > .drawing-item-add-row,
        & > .drawing-item-add-col,
        & > .drawing-item-cell {
          right: var(--rightDistance);
          border-color: @primary-color;
          color: @primary-color;
          background-color: @component-background;

          &:hover {
            background: @primary-color;
            color: @component-background;
          }
        }

        & > .drawing-item-delete {
          right: var(--rightDistance);
          border-color: @error-color;
          color: @error-color;
          background-color: @component-background;

          &:hover {
            background: @error-color;
            color: @component-background;
          }
        }
        & > .drawing-item-add-row {
          right: 108px;
        }

        & > .drawing-item-add-col {
          right: 78px;
        }

        & > .drawing-item-cell {
          top: unset;
          bottom: 0;
          right: 0;
          border-radius: unset;
          background: @primary-color;

          i {
            color: #fff;
          }
        }
      }
      .active-from-item {
        & > .ant-form-item {
          background-color: @primary-1;
          border: 1px solid @primary-color;
        }

        & > .drawing-item-copy,
        & > .drawing-item-delete,
        & > .drawing-item-add-row,
        & > .drawing-item-add-col,
        & > .drawing-item-cell {
          display: block;
        }

        & > .component-name {
          color: @primary-color;
        }
      }
      .row-tip {
        width: 100%;
        color: @text-color-secondary;
        text-align: center;
        position: absolute;
        top: 30px;
        font-size: 14px;
      }
      .table-grid {
        width: 100%;
        text-align: center;
        border-collapse: collapse;
        table-layout: fixed;
        .table-cell {
          min-height: 75px !important;
          overflow: auto;
          padding-top: 10px;
        }
        .drawing-row-item {
          padding: 0 2px;
          text-align: left;
        }
      }
    }
    .ipad {
      height: calc(100% - 42px);
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 711px;

      .outerIpad {
        background: url('../../../assets/images/iphoneBg.png');
        width: 389px;
        height: 711px;
        padding: 65px 40px;

        .ipadBody {
          height: 100%;
          .center-board-row > .ant-form {
            height: 550px !important;
          }
        }
      }
    }
  }
}
html[data-theme='dark'] {
  .@{prefix-cls} {
    .center-board-main .ipad .outerIpad {
      background: url('../../../assets/images/iphoneBg-dark.png');
    }
  }
}
