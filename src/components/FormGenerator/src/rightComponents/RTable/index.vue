<template>
  <a-divider>功能配置</a-divider>
  <a-form-item label="显示标题">
    <a-switch v-model:checked="activeData.__config__.showTitle" />
  </a-form-item>
  <a-form-item label="删除按钮">
    <a-switch v-model:checked="activeData.showDeleteBtn" />
  </a-form-item>
  <a-form-item label="添加按钮">
    <a-switch v-model:checked="activeData.showAddBtn" />
  </a-form-item>
  <template v-if="activeData.showAddBtn">
    <a-form-item label="动作文字">
      <a-input v-model:value="activeData.actionText" placeholder="请输入动作文字" />
    </a-form-item>
    <a-form-item label="动作设置">
      <jnpf-switch v-model:value="activeData.addType" />
    </a-form-item>
    <a-form-item label="动作表单" v-if="activeData.addType == 1">
      <a-button block @click="editConf()">配置表单</a-button>
    </a-form-item>
  </template>
  <a-form-item label="合计设置">
    <jnpf-switch v-model:value="activeData.showSummary" />
  </a-form-item>
  <a-form-item label="合计字段" v-if="activeData.showSummary">
    <jnpf-select v-model:value="activeData.summaryField" placeholder="请选择合计字段" :options="childrenList" allowClear showSearch multiple />
  </a-form-item>
  <a-form-item label="复杂表头" v-show="showType === 'pc'">
    <a-button block @click="handleComplexHeader">{{ activeData.__config__.complexHeaderList?.length ? '编辑复杂表头' : '复杂表头配置' }}</a-button>
  </a-form-item>
  <AddConfigModal @register="registerModal" @confirm="updateConf" />
  <ComplexHeaderModal @register="registerComplexHeaderModal" @confirm="updateComplexHeaderList" />
</template>
<script lang="ts" setup>
  import { inject, computed } from 'vue';
  import { useModal } from '/@/components/Modal';
  import AddConfigModal from './AddConfigModal.vue';
  import ComplexHeaderModal from '/@/components/ColumnDesign/src/components/ComplexHeaderModal.vue';

  const defaultAddTableConf = {
    popupTitle: '选择数据',
    popupType: 'dialog',
    popupWidth: '800px',
    interfaceId: '',
    interfaceName: '',
    templateJson: [],
    hasPage: true,
    pageSize: 20,
    columnOptions: [],
    relationOptions: [],
  };

  defineOptions({ inheritAttrs: false });
  const props = defineProps(['activeData']);
  const [registerModal, { openModal }] = useModal();
  const [registerComplexHeaderModal, { openModal: openComplexHeaderModal }] = useModal();

  const childrenList = computed(() => {
    const list = props.activeData.__config__.children.filter(o => ['input', 'inputNumber', 'calculate'].includes(o.__config__.jnpfKey) && o.__vModel__);
    return list.map(o => ({ id: o.__vModel__, fullName: o.__config__.label }));
  });

  const getShowType: (() => string | undefined) | undefined = inject('getShowType');
  const showType = computed(() => (getShowType as () => string | undefined)());

  function editConf() {
    if (!props.activeData.addTableConf) {
      props.activeData.addTableConf = JSON.parse(JSON.stringify(defaultAddTableConf));
    }
    let addTableConf = JSON.parse(JSON.stringify(props.activeData.addTableConf));
    openModal(true, { addTableConf, children: props.activeData.__config__.children });
  }
  function handleComplexHeader() {
    let columnOptions = props.activeData.__config__.children.map(o => ({
      fullName: o.__config__.label,
      id: o.__vModel__,
      ...o,
    }));
    columnOptions = columnOptions.filter(o => o.id);
    openComplexHeaderModal(true, { list: props.activeData.__config__.complexHeaderList, columnOptions: columnOptions });
  }
  function updateConf(data) {
    props.activeData.addTableConf = data;
  }
  function updateComplexHeaderList(data) {
    props.activeData.__config__.complexHeaderList = data;
  }
</script>
