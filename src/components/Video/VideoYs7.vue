<template>
  <div class="video-ys7-wrapper">
    <!-- 视频播放区域 -->
    <div class="video-player-container">
      <div :id="playerId" class="video-player-element" />
    </div>

    <!-- 控制按钮区域 -->
    <div class="control-panel">
      <div class="control-title">云台控制</div>
      <div class="control-row">
        <div @click="startPTZ(4)" class="control-btn" title="左上">
          <i class="ym-custom ym-custom-arrow-top-left"></i>
        </div>
        <div @click="startPTZ(0)" class="control-btn" title="上">
          <i class="ym-custom ym-custom-arrow-up"></i>
        </div>
        <div @click="startPTZ(6)" class="control-btn" title="右上">
          <i class="ym-custom ym-custom-arrow-top-right"></i>
        </div>
      </div>
      <div class="control-row">
        <div @click="startPTZ(2)" class="control-btn" title="左">
          <i class="ym-custom ym-custom-arrow-left"></i>
        </div>
        <div @click="startPTZ(16)" class="control-btn" title="自动控制">
          <i class="ym-custom ym-custom-image-filter-center-focus"></i>
        </div>
        <div @click="startPTZ(3)" class="control-btn" title="右">
          <i class="ym-custom ym-custom-arrow-right"></i>
        </div>
      </div>
      <div class="control-row">
        <div @click="startPTZ(5)" class="control-btn" title="左下">
          <i class="ym-custom ym-custom-arrow-bottom-left"></i>
        </div>
        <div @click="startPTZ(1)" class="control-btn" title="下">
          <i class="ym-custom ym-custom-arrow-down"></i>
        </div>
        <div @click="startPTZ(7)" class="control-btn" title="右下">
          <i class="ym-custom ym-custom-arrow-bottom-right"></i>
        </div>
      </div>

      <!-- 全屏控制按钮 -->
      <div class="control-row">
        <div @click="toggleFullscreen" class="control-btn fullscreen-btn" title="全屏/退出全屏">
          <span v-if="!isFullscreen">⛶</span>
          <span v-else>⛷</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import EZUIKit from "ezuikit-js";

interface Props {
  accessToken: string; // 字典名称
  deviceSerial: any; // 字典值
  url: any; // 字典值
}

const props = defineProps<Props>();

const { accessToken, deviceSerial, url } = props;
const player = ref<any>(null);
const isFullscreen = ref(false);
// 为每个组件实例生成唯一的ID
const playerId = `ezuikit-player-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

function init() {
  // 确保在创建新实例前，先销毁可能存在的旧实例
  if (document.getElementById(playerId)) {
    document.getElementById(playerId).innerHTML = '';
  }

  // 获取实际容器宽度
  const container = document.getElementById(playerId);
  const parentElement = container?.parentElement?.parentElement; // 获取更外层的容器
  let containerWidth = parentElement?.offsetWidth || window.innerWidth - 64;

  // 确保最小宽度
  containerWidth = Math.max(containerWidth, 300);
  const containerHeight = Math.floor(containerWidth * 9 / 16); // 严格16:9比例

  console.log('初始化播放器尺寸:', { containerWidth, containerHeight });

  player.value = new EZUIKit.EZUIKitPlayer({
    id: playerId,
    url,
    accessToken,
    template: 'security',
    width: containerWidth,
    height: containerHeight,
    autoplay: true,
    controls: true,
    // 禁用EZUIKit自带的全屏功能，避免冲突
    fullscreen: false,
    // 设置播放器的z-index
    zIndex: 1,
    // 禁用自动调整大小
    autoResize: false,
  });

  // 播放器创建后，强制设置CSS样式
  setTimeout(() => {
    forceResizePlayer();
    window.addEventListener('resize', debounceResize);

    // 添加全屏事件监听
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
  }, 500);
}

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

const debounceResize = debounce(handleResize, 300);

function handleResize() {
  console.log('窗口大小变化，重新调整播放器');
  forceResizePlayer();
}

// 切换全屏状态
function toggleFullscreen() {
  if (isFullscreen.value) {
    exitFullscreen();
  } else {
    enterFullscreen();
  }
}

// 进入全屏
function enterFullscreen() {
  const container = document.getElementById(playerId);
  if (!container) return;

  try {
    if (container.requestFullscreen) {
      container.requestFullscreen();
    } else if (container.webkitRequestFullscreen) {
      container.webkitRequestFullscreen();
    } else if (container.mozRequestFullScreen) {
      container.mozRequestFullScreen();
    } else if (container.msRequestFullscreen) {
      container.msRequestFullscreen();
    }
  } catch (e) {
    console.error('进入全屏失败:', e);
  }
}

// 退出全屏
function exitFullscreen() {
  try {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
  } catch (e) {
    console.error('退出全屏失败:', e);
  }
}

// 处理全屏状态变化
function handleFullscreenChange() {
  const fullscreenElement = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement
  );

  isFullscreen.value = fullscreenElement;
  console.log('全屏状态变化:', isFullscreen.value);

  if (isFullscreen.value) {
    // 进入全屏时的处理
    handleEnterFullscreen();
  } else {
    // 退出全屏时的处理
    handleExitFullscreen();
  }
}

// 进入全屏的处理
function handleEnterFullscreen() {
  console.log('进入全屏模式');

  // 停止自动调整大小，避免全屏时重新计算尺寸
  window.removeEventListener('resize', debounceResize);

  // 设置全屏样式
  const playerElement = document.getElementById(playerId);
  if (playerElement && player.value) {
    // 强制设置全屏尺寸
    try {
      if (typeof player.value.resize === 'function') {
        player.value.resize(window.screen.width, window.screen.height);
      }
    } catch (e) {
      console.warn('调整播放器全屏尺寸失败:', e);
    }

    // 设置DOM样式
    playerElement.style.position = 'fixed';
    playerElement.style.top = '0';
    playerElement.style.left = '0';
    playerElement.style.width = '100vw';
    playerElement.style.height = '100vh';
    playerElement.style.zIndex = '9999';
    playerElement.style.background = '#000';
  }
}

// 退出全屏的处理
function handleExitFullscreen() {
  console.log('退出全屏模式');

  const playerElement = document.getElementById(playerId);
  if (playerElement) {
    // 清除全屏样式
    playerElement.style.position = '';
    playerElement.style.top = '';
    playerElement.style.left = '';
    playerElement.style.width = '';
    playerElement.style.height = '';
    playerElement.style.zIndex = '';
    playerElement.style.background = '';
  }

  // 延迟恢复正常尺寸和事件监听
  setTimeout(() => {
    // 重新计算并设置正常尺寸
    forceResizePlayer();

    // 重新添加resize监听
    window.addEventListener('resize', debounceResize);

    // 确保播放器控制栏可见
    if (player.value && typeof player.value.resize === 'function') {
      const container = document.getElementById(playerId);
      if (container) {
        const parentElement = container.parentElement?.parentElement;
        let containerWidth = parentElement?.offsetWidth || 500;
        containerWidth = Math.max(containerWidth, 300);
        const containerHeight = Math.floor(containerWidth * 9 / 16);

        try {
          player.value.resize(containerWidth, containerHeight);
        } catch (e) {
          console.warn('恢复播放器尺寸失败:', e);
        }
      }
    }
  }, 200);
}

function forceResizePlayer() {
  // 检查是否处于全屏状态，如果是则不调整尺寸
  const isFullscreen = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement
  );

  if (isFullscreen) {
    console.log('当前处于全屏状态，跳过尺寸调整');
    return;
  }

  const container = document.getElementById(playerId);
  if (!container) return;

  const parentElement = container.parentElement?.parentElement;
  let containerWidth = parentElement?.offsetWidth || window.innerWidth - 64;
  containerWidth = Math.max(containerWidth, 300);
  const containerHeight = Math.floor(containerWidth * 9 / 16);

  console.log('调整播放器尺寸:', { containerWidth, containerHeight });

  // 方法1: 尝试使用播放器的resize方法
  try {
    if (player.value && typeof player.value.resize === 'function') {
      player.value.resize(containerWidth, containerHeight);
    }
  } catch (e) {
    console.warn('播放器resize方法失败:', e);
  }

  // 方法2: 直接修改DOM元素样式
  try {
    // 查找播放器内部的所有相关元素并强制设置尺寸
    const playerElements = container.querySelectorAll('*');
    playerElements.forEach(element => {
      if (element.tagName === 'VIDEO' ||
          element.tagName === 'CANVAS' ||
          element.className.includes('ezuikit') ||
          element.style.width ||
          element.style.height) {
        element.style.width = containerWidth + 'px';
        element.style.height = containerHeight + 'px';
        element.style.maxWidth = '100%';
        element.style.maxHeight = '100%';
      }
    });

    // 设置容器本身的尺寸
    container.style.width = containerWidth + 'px';
    container.style.height = containerHeight + 'px';

  } catch (e) {
    console.warn('强制设置DOM样式失败:', e);
  }

  // 方法3: 如果以上都失败，重新创建播放器
  if (player.value && containerWidth !== player.value.width) {
    console.log('尺寸差异过大，重新创建播放器');
    setTimeout(() => {
      cleanup();
      init();
    }, 100);
  }
}

/**
 * 开始云台控制（需HTTPS）
 * @param direction 操作命令：0-上，1-下，2-左，3-右，4-左上，5-左下，6-右上，7-右下，8-放大，9-缩小，10-近焦距，11-远焦距，16-自动控制
 */
const startPTZ = async (direction: number) => {
  const response = await fetch('https://open.ys7.com/api/lapp/device/ptz/start', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      accessToken,
      deviceSerial,
      channelNo: 1,
      direction, // 0=上 1=下 2=左 3=右
      speed: 1, // 速度
    }),
  });
  console.log(await response.json());
  setTimeout(() => {
    stopPTZ(direction);
  }, 1000);
};

// 停止控制（必须调用）
const stopPTZ = (direction = 0) => {
  fetch('https://open.ys7.com/api/lapp/device/ptz/stop', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      accessToken,
      deviceSerial,
      channelNo: 1,
      direction, // 0=上 1=下 2=左 3=右
    }),
  });
};

// 清理播放器实例
function cleanup() {
  console.log('清理播放器实例');

  // 移除窗口大小变化监听器
  window.removeEventListener('resize', debounceResize);

  // 移除全屏事件监听器
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange);

  if (player.value) {
    try {
      // 尝试停止播放
      if (typeof player.value.stop === 'function') {
        player.value.stop();
      }
      // 尝试销毁播放器
      if (typeof player.value.destroy === 'function') {
        player.value.destroy();
      }
      player.value = null;
    } catch (e) {
      console.error('清理播放器实例失败:', e);
    }
  }

  // 清空播放器容器
  const container = document.getElementById(playerId);
  if (container) {
    container.innerHTML = '';
    container.style.width = '';
    container.style.height = '';
  }
}

onMounted(() => {
  // 延迟初始化，确保DOM已经渲染完成
  setTimeout(() => {
    init();
  }, 100);
});
onUnmounted(() => cleanup());
</script>

<style lang="less">
.video-ys7-wrapper {
  width: 100%;
  background: transparent;
  border-radius: 8px;
  overflow: hidden;

  .video-player-container {
    width: 100%;
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;

    .video-player-element {
      width: 100%;
      min-height: 300px;

      // 强制EZUIKit播放器自适应
      :deep(.ezuikit-player) {
        width: 100% !important;
        height: auto !important;
        max-width: 100% !important;
      }

      :deep(.ezuikit-player > div) {
        width: 100% !important;
        height: auto !important;
      }

      :deep(video) {
        width: 100% !important;
        height: auto !important;
        max-width: 100% !important;
        object-fit: contain;
      }

      :deep(canvas) {
        width: 100% !important;
        height: auto !important;
        max-width: 100% !important;
      }

      // 覆盖所有可能的固定尺寸设置
      :deep(*) {
        max-width: 100% !important;
      }

      // 全屏相关样式
      :deep(.ezuikit-player) {
        position: relative;

        // 全屏按钮样式
        .ezuikit-fullscreen-btn {
          z-index: 10;
        }

        // 全屏状态样式
        &.ezuikit-fullscreen {
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          width: 100vw !important;
          height: 100vh !important;
          z-index: 9999 !important;
          background: #000 !important;

          video, canvas {
            width: 100vw !important;
            height: 100vh !important;
            object-fit: contain !important;
          }
        }
      }
    }
  }

  .control-panel {
    background: #f8f9fa;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    border-radius: 0 0 8px 8px;
    border-top: 1px solid #e9ecef;

    .control-title {
      font-size: 14px;
      font-weight: 600;
      color: #495057;
      margin-bottom: 8px;
      text-align: center;
    }

    .control-row {
      display: flex;
      gap: 12px;

      .control-btn {
        width: 36px;
        height: 36px;
        border: 1px solid #ddd;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        background: #fff;
        color: #333;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:hover {
          background: #f5f5f5;
          border-color: #bbb;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          transform: translateY(-1px);
        }

        &:active {
          background: #e9e9e9;
          transform: translateY(0);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        i {
          font-size: 14px;
        }

        &.fullscreen-btn {
          background: #1890ff;
          color: #fff;
          border-color: #1890ff;

          &:hover {
            background: #40a9ff;
            border-color: #40a9ff;
          }

          &:active {
            background: #096dd9;
            border-color: #096dd9;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .video-ys7-wrapper {
    .video-player-container {
      .video-player-element {
        min-height: 240px;
      }
    }

    .control-panel {
      padding: 12px;

      .control-row {
        gap: 6px;

        .control-btn {
          width: 32px;
          height: 32px;

          i {
            font-size: 12px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .video-ys7-wrapper {
    .video-player-container {
      .video-player-element {
        min-height: 200px;
      }
    }

    .control-panel {
      padding: 8px;

      .control-row {
        gap: 4px;

        .control-btn {
          width: 28px;
          height: 28px;

          i {
            font-size: 10px;
          }
        }
      }
    }
  }
}
</style>
