<template>
  <a-tag :color="tagColor">{{ tagText }}</a-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
  VEHICLE_PROCESS_STATE_MAP, 
  VEHICLE_PROCESS_STATE_COLOR_MAP,
  getVehicleProcessStateColor,
  getVehicleProcessStateText
} from '/@/enums/zzEnums';

interface Props {
  status: string | number;
  type?: 'vehicle' | 'application' | 'process';
}

const props = withDefaults(defineProps<Props>(), {
  type: 'vehicle'
});

const statusConfig = {
  vehicle: {
    '1': { color: 'success', text: '可用' },
    '2': { color: 'warning', text: '维修中' },
    '3': { color: 'error', text: '报废' }
  },
  application: {
    '0': { color: 'blue', text: '待队长审核' },
    '1': { color: 'cyan', text: '待工程部审核' },
    '2': { color: 'purple', text: '已分配车辆' },
    '3': { color: 'orange', text: '使用中' },
    '4': { color: 'success', text: '已完成' },
    '5': { color: 'error', text: '已拒绝' }
  },
  process: {
    '0': { color: 'default', text: '编制' },
    '1': { color: 'processing', text: '待审核' },
    '2': { color: 'error', text: '退回' },
    '3': { color: 'success', text: '已接受' },
    '4': { color: 'success', text: '已确认' }
  }
};

const config = computed(() => {
  const statusStr = String(props.status);
  
  if (props.type === 'process') {
    return {
      color: getVehicleProcessStateColor(statusStr),
      text: getVehicleProcessStateText(statusStr)
    };
  }
  
  return statusConfig[props.type][statusStr] || { color: 'default', text: '未知' };
});

const tagColor = computed(() => config.value.color);
const tagText = computed(() => config.value.text);
</script>
