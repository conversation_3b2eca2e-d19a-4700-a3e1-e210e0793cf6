<template>
  <div class="unsuccess">
    <a-alert message="错误提醒：导入失败数据展示" type="warning" show-icon/>
    <div class="upload error-show">
      <div class="up_left">
        <img src="../../../../assets/images/tip.png"/>
      </div>
      <div class="up_right">
        <p class="tip success-tip">
          正常数量条数：<span>{{ successNum }}条</span>
        </p>
        <p class="tip error-tip">
          异常数量条数：<span>{{ failNum }}条</span>
        </p>
      </div>
    </div>
    <div class="conTips">
      <p>以下文件数据为导入异常数据</p>
    </div>
    <slot />
  </div>
</template>

<script lang="ts" setup>
defineOptions({name: 'FaImportFailCard', inheritAttrs: false});

defineProps(['successNum', 'failNum']);
</script>
