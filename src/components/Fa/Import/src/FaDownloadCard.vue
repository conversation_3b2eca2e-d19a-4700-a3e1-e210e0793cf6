<template>
  <div class="upload">
    <div class="up_left">
      <img src="../../../../assets/images/import.png"/>
    </div>
    <div class="up_right">
      <p class="title">填写导入数据信息</p>
      <p class="tip">请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除</p>
      <a :href="downloadUrl">下载模板</a>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'FaDownloadCard', inheritAttrs: false });
const props = defineProps(['downloadUrl']);

const {downloadUrl} = props
</script>
