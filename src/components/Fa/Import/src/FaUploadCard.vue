<template>
  <div class="upload">
    <div class="up_left">
      <img src="../../../../assets/images/upload.png"/>
    </div>
    <div class="up_right">
      <p class="title">上传填好的数据表</p>
      <p class="tip">文件后缀名必须是xls或xlsx，文件大小不超过500KB，最多支持导入1000条数据</p>
      <JnpfFaUploadFileQiniu v-bind="$attrs"/>
    </div>
  </div>
</template>

<script lang="ts" setup>
import JnpfFaUploadFileQiniu from "/@/components/Jnpf/Upload/src/FaUploadFileQiniu.vue";

defineOptions({name: 'FaUploadCard', inheritAttrs: false});
</script>
