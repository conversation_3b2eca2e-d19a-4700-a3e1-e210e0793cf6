import { withInstall } from '/@/utils';
import faDownloadCard from './src/FaDownloadCard.vue';
import faImportHeaderStep from './src/FaImportHeaderStep.vue';
import faImportFailCard from './src/FaImportFailCard.vue';
import faImportSuccessCard from './src/FaImportSuccessCard.vue';
import faUploadCard from './src/FaUploadCard.vue';

export const FaDownloadCard = withInstall(faDownloadCard);
export const FaImportHeaderStep = withInstall(faImportHeaderStep);
export const FaImportFailCard = withInstall(faImportFailCard);
export const FaImportSuccessCard = withInstall(faImportSuccessCard);
export const FaUploadCard = withInstall(faUploadCard);
