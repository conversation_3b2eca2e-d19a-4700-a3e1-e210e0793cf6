<template>
  <FlowParser @register="registerFlowParser" @reload="reload" />
  <BasicModal v-bind="$attrs" @register="registerFlowListModal" title="请选择流程" :footer="null" :width="400" destroyOnClose class="jnpf-flow-list-modal">
    <div class="template-list">
      <ScrollContainer>
        <div class="template-item" v-for="item in flowList" :key="item.id" @click="selectFlow(item)">
          {{ item.fullName }}
        </div>
      </ScrollContainer>
    </div>
  </BasicModal>
</template>

<!-- 流程组件抽取，简化流程页面代码 -->
<!-- 注意：此组件不可全局导入，会导致一直组件不生效 -->
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useMessage } from "/@/hooks/web/useMessage";
import { usePopup } from "/@/components/Popup";
import { BasicModal, useModal } from "/@/components/Modal";
import FlowParser from "/@/views/workFlow/components/FlowParser.vue";
import { ScrollContainer } from "/@/components/Container";
import { getFlowIdByCode, getFlowList } from "/@/api/workFlow/flowEngine";
import { getFlowTask, getFlowTaskNode, getFlowTaskOperator } from "/@/api/workFlow/workFlowForm";

defineOptions({ name: 'FaFlowCube', inheritAttrs: true });
const props = defineProps(['flowEnCode'])
const emit = defineEmits(['reload']);

defineExpose({handleAdd, toDetail}); // 暴露给外部调用的方法

const flowTemplateId = ref('');
const flowList = ref<any[]>([]);
const { createMessage } = useMessage();

const [registerFlowParser, { openPopup: openFlowParser }] = usePopup();
const [registerFlowListModal, { openModal: openFlowListModal, closeModal: closeFlowListModal }] = useModal();

function getFlowTemplateId() {
  getFlowIdByCode(props.flowEnCode).then(res => {
    flowTemplateId.value = res.data;
    getFlowList(flowTemplateId.value, '1').then(res => {
      flowList.value = res.data;
    });
  });
}
const extraData = ref<any>()
function handleAdd(data:any) {
  extraData.value = data;
  if (!flowList.value.length) return createMessage.error('流程不存在');
  if (flowList.value.length === 1) return selectFlow(flowList.value[0]);
  openFlowListModal(true);
}
function selectFlow(flowTypeItem:any) {
  closeFlowListModal();
  const data = {
    id: '',
    flowId: flowTypeItem.id,
    opType: '-1',
    extra: extraData.value,
  };
  openFlowParser(true, data);
}

/**
 * 跳转业务详情
 * @param record 业务数据
 * @param opType
 * @param operate 是否进行编辑操作
 */
async function toDetail(record, opType, operate = false) {
  let taskNodeId = undefined;
  let taskId = undefined;
  if (operate) {
    try {
      const res0 = await getFlowTask(record.id);
      if (res0.data) {
        const res = await getFlowTaskNode(record.id); // 如果返回了数据，则表示已经在后台创建了流程节点。没有返回流程数据，则表示是草稿状态的数据
        taskNodeId = res?.data?.id;

        if (res.data?.nodeCode) {
          const operatorRes = await getFlowTaskOperator(record.id, res.data?.nodeCode);
          taskId = operatorRes?.data?.id;
        }
      }
    } catch (e) {
      console.error(e)
    }
  }

  const data = {
    id: record.id,
    flowId: record.flowId,
    opType,
    status: record.currentState,
    // 已经进行的流程节点信息
    taskNodeId,
    taskId,
  };
  openFlowParser(true, data);
}

function reload() {
  emit('reload')
}

onMounted(() => {
  getFlowTemplateId();
});
</script>
