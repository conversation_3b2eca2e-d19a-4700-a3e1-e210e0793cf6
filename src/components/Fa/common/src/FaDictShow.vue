<script setup lang="ts">
import { onMounted, ref } from "vue";
import { watchImmediate } from "@vueuse/core";
import { find, trim } from "lodash-es";
import { useBaseStore } from "/@/store/modules/base";

defineOptions({ name: 'FaDictShow', inheritAttrs: false });
interface Props {
  dictName: string; // 字典名称
  value: any; // 字典值
}

const props = defineProps<Props>();

const { dictName, value } = props;

const baseStore = useBaseStore();

const dictShowName = ref<string>('');

async function refresh() {
  const options = (await baseStore.getDictionaryData(dictName)) as any[];
  const o = find(options, i => trim(i.enCode) === trim(value))
  dictShowName.value = o?.fullName
}

watchImmediate(() => props.value, refresh);

onMounted(() => {
  refresh()
})
</script>

<template>
  <span>
    {{dictShowName}}
  </span>
</template>

<style scoped>

</style>
