<template>
  <div>
    <a-tag color="processing" v-if="status == 1">等待审核</a-tag>
    <a-tag color="success" v-else-if="status == 2">审核通过</a-tag>
    <a-tag color="error" v-else-if="status == 3">审核退回</a-tag>
    <a-tag v-else-if="status == 4">流程撤回</a-tag>
    <a-tag v-else-if="status == 5">审核终止</a-tag>
    <a-tag color="error" v-else-if="status == 6">已被挂起</a-tag>
    <a-tag color="warning" v-else>等待提交</a-tag>
  </div>
</template>

<script setup lang="ts">
defineOptions({ name: 'FaFlowStatus', inheritAttrs: true });
defineProps(['status'])
</script>
