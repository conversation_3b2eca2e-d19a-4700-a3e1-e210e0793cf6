<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    width="600px"
    :minHeight="100"
    :showContinueBtn="showContinueBtn"
    :continueText="continueText"
    cancelText="取消"
    okText="确定"
    @ok="handleSubmit(0)"
    @continue="handleSubmit(1)"
    :closeFunc="onClose">
    <template #insertFooter>
      <a-space :size="10" v-if="dataForm.id" class="float-left">
        <a-button :disabled="getPrevDisabled" @click="handlePrev">上一条</a-button>
        <a-button :disabled="getNextDisabled" @click="handleNext">下一条</a-button>
      </a-space>
    </template>
    <a-row class="dynamic-form">
      <a-form
        :colon="false"
        size="default"
        layout="horizontal"
        labelAlign="right"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <!-- <a-col :span="24" class="ant-col-item">
            <a-form-item name="sourceUnit">
              <template #label>来源单位</template>
              <JnpfInput
                v-model:value="dataForm.sourceUnit"
                @change="changeData('sourceUnit', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col> -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="realName">
              <template #label>姓名</template>
              <JnpfInput
                v-model:value="dataForm.realName"
                @change="changeData('realName', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="gender">
              <template #label>性别</template>
              <JnpfSelect
                v-model:value="dataForm.gender"
                @change="changeData('gender', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                :options="optionsObj.genderOptions"
                :fieldNames="optionsObj.genderProps">
              </JnpfSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="organizeId">
              <template #label>部门</template>
              <JnpfDepSelect
                v-model:value="dataForm.organizeId"
                @change="changeData('organizeId', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                selectType="all">
              </JnpfDepSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="positionId">
              <template #label>岗位</template>
              <JnpfPosSelect
                v-model:value="dataForm.positionId"
                @change="changeData('positionId', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                selectType="all">
              </JnpfPosSelect>
            </a-form-item>
          </a-col>

          <a-col :span="24" class="ant-col-item">
            <a-form-item name="seniority">
              <template #label>工龄</template>
              <JnpfInput
                v-model:value="dataForm.seniority"
                @change="changeData('seniority', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="interiorSeniority">
              <template #label>内部工龄</template>
              <JnpfInput
                v-model:value="dataForm.interiorSeniority"
                @change="changeData('interiorSeniority', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>

          <!-- <a-col :span="24" class="ant-col-item">
            <a-form-item name="identificationNumber">
              <template #label>身份证号</template>
              <JnpfInput
                v-model:value="dataForm.identificationNumber"
                @change="changeData('identificationNumber', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col> -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="mobilePhone">
              <template #label>手机</template>
              <JnpfInput
                v-model:value="dataForm.mobilePhone"
                @change="changeData('mobilePhone', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>

          <a-col :span="24" class="ant-col-item">
            <a-form-item name="trainingTime">
              <template #label>培训时间</template>
              <JnpfDatePicker
                v-model:value="dataForm.trainingTime"
                @change="changeData('trainingTime', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                format="yyyy-MM-dd"
                :startTime="getRelationDate(false, 1, 1, '', '')"
                :endTime="getRelationDate(false, 1, 1, '', '')">
              </JnpfDatePicker>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="trainingStatus">
              <template #label>培训状态</template>
              <JnpfInput
                v-model:value="dataForm.trainingStatus"
                @change="changeData('trainingStatus', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>

          <a-col :span="24" class="ant-col-item">
            <a-form-item name="birthday">
              <template #label>生日</template>
              <JnpfDatePicker
                v-model:value="dataForm.birthday"
                @change="changeData('birthday', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                format="yyyy-MM-dd"
                :startTime="getRelationDate(false, 1, 1, '', '')"
                :endTime="getRelationDate(false, 1, 1, '', '')">
              </JnpfDatePicker>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="team">
              <template #label>班组</template>
              <JnpfDepSelect
                v-model:value="dataForm.team"
                @change="changeData('team', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                selectType="all">
              </JnpfDepSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="age">
              <template #label>年龄</template>
              <JnpfInput v-model:value="dataForm.age" @change="changeData('age', -1)" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="nation">
              <template #label>民族</template>
              <JnpfSelect
                v-model:value="dataForm.nation"
                @change="changeData('nation', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                :options="optionsObj.nationOptions"
                :fieldNames="optionsObj.nationProps">
              </JnpfSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="education">
              <template #label>学历</template>
              <JnpfSelect
                v-model:value="dataForm.education"
                @change="changeData('education', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                :showSearch="false"
                :options="optionsObj.educationOptions"
                :fieldNames="optionsObj.educationProps">
              </JnpfSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="nativePlace">
              <template #label>籍贯</template>
              <JnpfInput
                v-model:value="dataForm.nativePlace"
                @change="changeData('nativePlace', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="categoryPersonnel">
              <template #label>人员类别</template>
              <JnpfInput
                v-model:value="dataForm.categoryPersonnel"
                @change="changeData('categoryPersonnel', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>

          <a-col :span="24" class="ant-col-item">
            <a-form-item name="goProjectTime">
              <template #label>进入项目时间</template>
              <JnpfDatePicker
                v-model:value="dataForm.goProjectTime"
                @change="changeData('goProjectTime', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                format="yyyy-MM-dd"
                :startTime="getRelationDate(false, 1, 1, '', '')"
                :endTime="getRelationDate(false, 1, 1, '', '')">
              </JnpfDatePicker>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="bloodType">
              <template #label>血型</template>
              <JnpfInput
                v-model:value="dataForm.bloodType"
                @change="changeData('bloodType', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="formEmployment">
              <template #label>用工形式</template>
              <JnpfInput
                v-model:value="dataForm.formEmployment"
                @change="changeData('formEmployment', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="natureAccount">
              <template #label>户口性质</template>
              <JnpfInput
                v-model:value="dataForm.natureAccount"
                @change="changeData('natureAccount', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="positionLevel">
              <template #label>岗位层级</template>
              <JnpfInput
                v-model:value="dataForm.positionLevel"
                @change="changeData('positionLevel', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="positionSequence">
              <template #label>岗位序列</template>
              <JnpfInput
                v-model:value="dataForm.positionSequence"
                @change="changeData('positionSequence', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="project">
              <template #label>项目</template>
              <JnpfInput
                v-model:value="dataForm.project"
                @change="changeData('project', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="politicalOutlook">
              <template #label>政治面貌</template>
              <JnpfInput
                v-model:value="dataForm.politicalOutlook"
                @change="changeData('politicalOutlook', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="graduationSchool">
              <template #label>毕业学校</template>
              <JnpfInput
                v-model:value="dataForm.graduationSchool"
                @change="changeData('graduationSchool', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="specialty">
              <template #label>所学专业</template>
              <JnpfInput
                v-model:value="dataForm.specialty"
                @change="changeData('specialty', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="graduationTime">
              <template #label>毕业时间</template>
              <JnpfDatePicker
                v-model:value="dataForm.graduationTime"
                @change="changeData('graduationTime', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                format="yyyy-MM-dd"
                :startTime="getRelationDate(false, 1, 1, '', '')"
                :endTime="getRelationDate(false, 1, 1, '', '')">
              </JnpfDatePicker>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="joinWorkTime">
              <template #label>参加工作时间</template>
              <JnpfDatePicker
                v-model:value="dataForm.joinWorkTime"
                @change="changeData('joinWorkTime', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                format="yyyy-MM-dd"
                :startTime="getRelationDate(false, 1, 1, '', '')"
                :endTime="getRelationDate(false, 1, 1, '', '')">
              </JnpfDatePicker>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="joinFiveFirmTime">
              <template #label>进入五公司时间</template>
              <JnpfDatePicker
                v-model:value="dataForm.joinFiveFirmTime"
                @change="changeData('joinFiveFirmTime', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                format="yyyy-MM-dd"
                :startTime="getRelationDate(false, 1, 1, '', '')"
                :endTime="getRelationDate(false, 1, 1, '', '')">
              </JnpfDatePicker>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="technicalTitle">
              <template #label>专业技术职称</template>
              <JnpfInput
                v-model:value="dataForm.technicalTitle"
                @change="changeData('technicalTitle', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="workerSkillLevel">
              <template #label>工人技能等级</template>
              <JnpfInput
                v-model:value="dataForm.workerSkillLevel"
                @change="changeData('workerSkillLevel', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="professionalQual">
              <template #label>职业资格</template>
              <JnpfInput
                v-model:value="dataForm.professionalQual"
                @change="changeData('professionalQual', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="sourceClassify">
              <template #label>来源分类</template>
              <JnpfInput
                v-model:value="dataForm.sourceClassify"
                @change="changeData('sourceClassify', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="constructionExperience">
              <template #label>核电建设经历</template>
              <JnpfInput
                v-model:value="dataForm.constructionExperience"
                @change="changeData('constructionExperience', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="officeLocation">
              <template #label>办公地点</template>
              <JnpfInput
                v-model:value="dataForm.officeLocation"
                @change="changeData('officeLocation', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="officePhone">
              <template #label>办公电话</template>
              <JnpfInput
                v-model:value="dataForm.officePhone"
                @change="changeData('officePhone', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="email">
              <template #label>邮箱</template>
              <JnpfInput v-model:value="dataForm.email" @change="changeData('email', -1)" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="emergencyContacts">
              <template #label>紧急联系人</template>
              <JnpfInput
                v-model:value="dataForm.emergencyContacts"
                @change="changeData('emergencyContacts', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="emergencyContactsPhone">
              <template #label>紧急联系人电话</template>
              <JnpfInput
                v-model:value="dataForm.emergencyContactsPhone"
                @change="changeData('emergencyContactsPhone', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="maritalStatus">
              <template #label>婚姻状况</template>
              <JnpfInput
                v-model:value="dataForm.maritalStatus"
                @change="changeData('maritalStatus', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="number">
              <template #label>人数</template>
              <JnpfInput v-model:value="dataForm.number" @change="changeData('number', -1)" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <!-- <a-col :span="24" class="ant-col-item">
            <a-form-item name="homeAddress">
              <template #label>家庭住址</template>
              <JnpfInput
                v-model:value="dataForm.homeAddress"
                @change="changeData('homeAddress', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col> -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="phase">
              <template #label>一二期</template>
              <JnpfInput v-model:value="dataForm.phase" @change="changeData('phase', -1)" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="remark">
              <template #label>备注</template>
              <JnpfInput v-model:value="dataForm.remark" @change="changeData('remark', -1)" placeholder="请输入" :allowClear="true" :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>

          <a-col :span="24" class="ant-col-item">
            <a-form-item name="removeTime">
              <template #label>撤场时间</template>
              <JnpfDatePicker
                v-model:value="dataForm.removeTime"
                @change="changeData('removeTime', -1)"
                placeholder="请选择"
                :allowClear="true"
                :style="{ width: '100%' }"
                format="yyyy-MM-dd"
                :startTime="getRelationDate(false, 1, 1, '', '')"
                :endTime="getRelationDate(false, 1, 1, '', '')">
              </JnpfDatePicker>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="removeRemark">
              <template #label>撤场原因</template>
              <JnpfInput
                v-model:value="dataForm.removeRemark"
                @change="changeData('removeRemark', -1)"
                placeholder="请输入"
                :allowClear="true"
                :style="{ width: '100%' }">
              </JnpfInput>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { create, update, getInfo } from './helper/api';
  import { reactive, toRefs, nextTick, ref, unref, computed, toRaw } from 'vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { JnpfRelationForm } from '/@/components/Jnpf';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';
  import type { FormInstance } from 'ant-design-vue';
  import { thousandsFormat, getDateTimeUnit, getTimeUnit } from '/@/utils/jnpf';
  import { getDictionaryDataSelector } from '/@/api/systemData/dictionary';
  import { getDataInterfaceRes } from '/@/api/systemData/dataInterface';
  import dayjs from 'dayjs';
  // 表单权限
  import { usePermission } from '/@/hooks/web/usePermission';

  interface State {
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }

  const emit = defineEmits(['reload']);
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps }] = useModal();
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      sourceUnit: undefined,
      realName: undefined,
      gender: '',
      organizeId: undefined,
      positionId: undefined,
      seniority: undefined,
      interiorSeniority: undefined,
      identificationNumber: undefined,
      mobilePhone: undefined,
      trainingTime: undefined,
      trainingStatus: undefined,
      birthday: undefined,
      team: undefined,
      age: undefined,
      nation: '54bad287aa084f81b40c6bc35c7f2545',
      education: '',
      nativePlace: undefined,
      categoryPersonnel: undefined,
      goProjectTime: undefined,
      bloodType: undefined,
      formEmployment: undefined,
      natureAccount: undefined,
      remark: undefined,
      removeTime: undefined,
      removeRemark: undefined,
      positionLevel: undefined,
      positionSequence: undefined,
      project: undefined,
      politicalOutlook: undefined,
      graduationSchool: undefined,
      specialty: undefined,
      graduationTime: undefined,
      joinWorkTime: undefined,
      joinFiveFirmTime: undefined,
      technicalTitle: undefined,
      workerSkillLevel: undefined,
      professionalQual: undefined,
      sourceClassify: undefined,
      constructionExperience: undefined,
      officeLocation: undefined,
      officePhone: undefined,
      email: undefined,
      emergencyContacts: undefined,
      emergencyContactsPhone: undefined,
      maritalStatus: undefined,
      number: undefined,
      homeAddress: undefined,
      phase: undefined,
    },

    tableRows: {},

    dataRule: {
      identificationNumber: [
        {
          pattern: /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
          message: '请输入正确的身份证号码',
          trigger: 'blur',
        },
      ],
      mobilePhone: [
        {
          pattern: /^1[3456789]\d{9}$/,
          message: '请输入正确的手机号码',
          trigger: 'blur',
        },
      ],
    },

    optionsObj: {
      genderOptions: [
        { fullName: '男', id: 1 },
        { fullName: '女', id: 2 },
        { fullName: '保密', id: 3 },
      ],
      genderProps: { label: 'fullName', value: 'id' },
      nationOptions: [],
      nationProps: { label: 'fullName', value: 'id' },
      educationOptions: [],
      educationProps: { label: 'fullName', value: 'id' },
    },

    childIndex: -1,
    isEdit: false,
    interfaceRes: {
      birthday: [],
      organizeId: [],
      education: [],
      gender: [],
      nation: [],
      remark: [],
      team: [],
      removeTime: [],
      bloodType: [],
      natureAccount: [],
      realName: [],
      formEmployment: [],
      removeRemark: [],
      positionId: [],
      mobilePhone: [],
      trainingTime: [],
      trainingStatus: [],
      identificationNumber: [],
      seniority: [],
      interiorSeniority: [],
      nativePlace: [],
      categoryPersonnel: [],
      goProjectTime: [],
      sourceUnit: [],
      age: [],
      positionLevel: [],
      positionSequence: [],
      project: [],
      politicalOutlook: [],
      graduationSchool: [],
      specialty: [],
      graduationTime: [],
      joinWorkTime: [],
      joinFiveFirmTime: [],
      technicalTitle: [],
      workerSkillLevel: [],
      professionalQual: [],
      sourceClassify: [],
      constructionExperience: [],
      officeLocation: [],
      officePhone: [],
      email: [],
      emergencyContacts: [],
      emergencyContactsPhone: [],
      maritalStatus: [],
      number: [],
      homeAddress: [],
      phase: [],
    },
    //可选范围默认值
    ableAll: {},

    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll } = toRefs(state);

  const getPrevDisabled = computed(() => state.currIndex === 0);
  const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    state.isContinue = false;
    state.title = !data.id ? '新增' : '编辑';
    state.continueText = !data.id ? '确定并新增' : '确定并继续';
    setFormProps({ continueLoading: false });
    state.dataForm.id = data.id;
    openModal();
    state.allList = data.allList;
    state.currIndex = state.allList.length && data.id ? state.allList.findIndex(item => item.id === data.id) : 0;
    nextTick(() => {
      getForm().resetFields();
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      //初始化options
      getnationOptions();
      geteducationOptions();

      // 设置默认值
      state.dataForm = {
        sourceUnit: undefined,
        realName: undefined,
        gender: '',
        organizeId: undefined,
        positionId: undefined,
        identificationNumber: undefined,
        seniority: undefined,
        interiorSeniority: undefined,
        mobilePhone: undefined,
        trainingTime: undefined,
        trainingStatus: undefined,
        birthday: undefined,
        team: undefined,
        age: undefined,
        nation: '54bad287aa084f81b40c6bc35c7f2545',
        education: '',
        nativePlace: undefined,
        categoryPersonnel: undefined,
        goProjectTime: undefined,
        bloodType: undefined,
        formEmployment: undefined,
        natureAccount: undefined,
        remark: undefined,
        removeTime: undefined,
        removeRemark: undefined,
        positionLevel: undefined,
        positionSequence: undefined,
        project: undefined,
        politicalOutlook: undefined,
        graduationSchool: undefined,
        specialty: undefined,
        graduationTime: undefined,
        joinWorkTime: undefined,
        joinFiveFirmTime: undefined,
        technicalTitle: undefined,
        workerSkillLevel: undefined,
        professionalQual: undefined,
        sourceClassify: undefined,
        constructionExperience: undefined,
        officeLocation: undefined,
        officePhone: undefined,
        email: undefined,
        emergencyContacts: undefined,
        emergencyContactsPhone: undefined,
        maritalStatus: undefined,
        number: undefined,
        homeAddress: undefined,
        phase: undefined,
      };
      state.childIndex = -1;
      changeLoading(false);
    }
  }
  function getForm() {
    const form = unref(formRef);
    if (!form) {
      throw new Error('form is null!');
    }
    return form;
  }
  function getData(id) {
    getInfo(id).then(res => {
      state.dataForm = res.data || {};
      getnationOptions();
      geteducationOptions();

      state.childIndex = -1;
      changeLoading(false);
    });
  }
  async function handleSubmit(type) {
    try {
      const values = await getForm()?.validate();
      if (!values) return;
      state.submitType = type;
      state.submitType === 1 ? setFormProps({ continueLoading: true }) : setFormProps({ confirmLoading: true });
      const formMethod = state.dataForm.id ? update : create;
      formMethod(state.dataForm)
        .then(res => {
          createMessage.success(res.msg);
          state.submitType === 1 ? setFormProps({ continueLoading: false }) : setFormProps({ confirmLoading: false });
          if (state.submitType == 1) {
            initData();
            state.isContinue = true;
          } else {
            setFormProps({ visible: false });
            emit('reload');
          }
        })
        .catch(() => {
          state.submitType === 1 ? setFormProps({ continueLoading: false }) : setFormProps({ confirmLoading: false });
        });
    } catch (_) {}
  }
  function handlePrev() {
    state.currIndex--;
    handleGetNewInfo();
  }
  function handleNext() {
    state.currIndex++;
    handleGetNewInfo();
  }
  function handleGetNewInfo() {
    changeLoading(true);
    getForm().resetFields();
    const id = state.allList[state.currIndex].id;
    getData(id);
  }
  function setFormProps(data) {
    setModalProps(data);
  }
  function changeLoading(loading) {
    setModalProps({ loading });
  }
  async function onClose() {
    if (state.isContinue) emit('reload');
    return true;
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
  function changeDataFormData(type, data, model, index, defaultValue) {
    if (!state.isEdit) {
      if (type == 2) {
        for (let i = 0; i < state.dataForm[data].length; i++) {
          if (index == -1) {
            state.dataForm[data][i][model] = defaultValue;
          } else if (index == i) {
            state.dataForm[data][i][model] = defaultValue;
          }
        }
      } else {
        state.dataForm[data] = defaultValue;
      }
    }
  }
  //数据选项--数据字典初始化方法
  function getnationOptions() {
    getDictionaryDataSelector('b6cd65a763fa45eb9fe98e5057693e40').then(res => {
      state.optionsObj.nationOptions = res.data.list;
    });
  }
  //数据选项--数据字典初始化方法
  function geteducationOptions() {
    getDictionaryDataSelector('6a6d6fb541b742fbae7e8888528baa16').then(res => {
      state.optionsObj.educationOptions = res.data.list;
    });
  }
  function getRelationDate(timeRule, timeType, timeTarget, timeValueData, dataValue) {
    let timeDataValue: any = null;
    let timeValue = Number(timeValueData);
    if (timeRule) {
      if (timeType == 1) {
        timeDataValue = timeValue;
      } else if (timeType == 2) {
        timeDataValue = dataValue;
      } else if (timeType == 3) {
        timeDataValue = new Date().getTime();
      } else if (timeType == 4 || timeType == 5) {
        const type = getTimeUnit(timeTarget);
        const method = timeType == 4 ? 'subtract' : 'add';
        timeDataValue = dayjs()[method](timeValue, type).valueOf();
      }
    }
    return timeDataValue;
  }
  function getRelationTime(timeRule, timeType, timeTarget, timeValue, formatType, dataValue) {
    let format = formatType == 'HH:mm' ? 'HH:mm:00' : formatType;
    let timeDataValue: any = null;
    if (timeRule) {
      if (timeType == 1) {
        timeDataValue = timeValue || '00:00:00';
        if (timeDataValue.split(':').length == 3) {
          timeDataValue = timeDataValue;
        } else {
          timeDataValue = timeDataValue + ':00';
        }
      } else if (timeType == 2) {
        timeDataValue = dataValue;
      } else if (timeType == 3) {
        timeDataValue = dayjs().format(format);
      } else if (timeType == 4 || timeType == 5) {
        const type = getTimeUnit(timeTarget + 3);
        const method = timeType == 4 ? 'subtract' : 'add';
        timeDataValue = dayjs()[method](timeValue, type).format(format);
      }
    }
    return timeDataValue;
  }
</script>
