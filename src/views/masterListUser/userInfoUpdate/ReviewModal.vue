<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="审核人员信息"
    :width="500"
    @ok="handleSubmit"
    destroyOnClose
  >
    <div class="review-content">
      <!-- 审核操作 -->
      <div class="review-section">
        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="审核结果" required>
                <a-select
                  v-model:value="reviewForm.status"
                  placeholder="请选择审核结果"
                  :options="reviewStatusOptions"
                  :field-names="{ label: 'fullName', value: 'id' }"
                />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="审核意见">
                <a-textarea
                  v-model:value="reviewForm.reviewComment"
                  placeholder="请输入审核意见"
                  :rows="4"
                  :maxlength="500"
                  show-count
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useMessage } from '/@/hooks/web/useMessage';
import baseUserInfoRegistryApi from '/@/api/example/BaseUserInfoRegistryApi';
import { useUserStore } from '/@/store/modules/user';
import dayjs from 'dayjs';
import { update as masterListUserUpdate } from '/@/views/masterListUser/helper/api';

defineOptions({ name: 'masterListUser-userInfoUpdate-reviewModal' });

const emit = defineEmits(['register', 'reload']);
const { createMessage } = useMessage();
const userStore = useUserStore();
const recordData = ref<any>({});

// 审核表单数据
const reviewForm = ref({
  status: undefined,
  reviewComment: ''
});

// 人员信息更新状态枚举
const USER_INFO_REGISTRY_STATUS = {
  APPROVED: 1,     // 已审核通过
  REJECTED: 2,     // 已拒绝
} as const;

// 审核状态选项
const reviewStatusOptions = [
  { fullName: '审核通过', id: USER_INFO_REGISTRY_STATUS.APPROVED },
  { fullName: '审核拒绝', id: USER_INFO_REGISTRY_STATUS.REJECTED },
];

// 弹窗注册
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  // 重置审核表单
  reviewForm.value = {
    status: undefined,
    reviewComment: ''
  };

  recordData.value = data?.record || {};
});

// 提交审核
async function handleSubmit() {
  try {
    // 表单验证
    if (!reviewForm.value.status) {
      createMessage.warning('请选择审核结果');
      return;
    }

    // 构建更新数据，包含原有数据和审核信息
    const updateData = {
      ...recordData.value, // 保留原有数据
      status: reviewForm.value.status, // 更新状态
      reviewComment: reviewForm.value.reviewComment || '', // 审核意见
      reviewTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 当前时间作为审核时间
      reviewerId: userStore.userInfo?.userId || '', // 当前用户ID作为审核人ID
      reviewerName: userStore.userInfo?.realName || userStore.userInfo?.userName || '', // 当前用户姓名作为审核人姓名
    };

    // 无论审核通过还是不通过，都要调用baseUserInfoRegistryApi.update接口
    await baseUserInfoRegistryApi.update(updateData);

    // 如果审核通过，额外调用masterListUser的update接口
    if (reviewForm.value.status === USER_INFO_REGISTRY_STATUS.APPROVED) {
      // 审核通过：将originalUserId替换为id，调用masterListUser的update接口
      const masterListUpdateData = {
        ...updateData,
        id: recordData.value.originalUserId // 将originalUserId替换为id
      };

      // 移除originalUserId字段，避免冲突
      delete masterListUpdateData.originalUserId;

      await masterListUserUpdate(masterListUpdateData);
    }

    const statusText = reviewForm.value.status === USER_INFO_REGISTRY_STATUS.APPROVED ? '审核通过' : '审核拒绝';
    createMessage.success(`${statusText}成功`);

    closeModal();
    emit('reload');
  } catch (error) {
    console.error('审核失败:', error);
    createMessage.error('审核失败，请重试');
  }
}
</script>

<style scoped lang="less">
.review-content {
  padding: 20px 0;

  .review-section {
    :deep(.ant-form-item-label) {
      font-weight: 500;
      color: #333;
    }

    :deep(.ant-select) {
      width: 100%;
    }

    :deep(.ant-input) {
      border-radius: 6px;
    }

    :deep(.ant-select-selector) {
      border-radius: 6px;
    }
  }
}
</style>
