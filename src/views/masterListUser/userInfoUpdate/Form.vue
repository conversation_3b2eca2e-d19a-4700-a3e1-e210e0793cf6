<template>
  <BasicPopup
    v-bind="$attrs"
    @register="registerPopup"
    :title="getTitle"
    :width="1000"
    :showOkBtn="true"
    @ok="handleSubmit"
    destroyOnClose
  >
    <BasicForm @register="registerForm" />
  </BasicPopup>
</template>

<script setup lang="ts">
import { ref, computed, unref, onUnmounted } from 'vue';
import { BasicPopup, usePopupInner } from '/@/components/Popup';
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import {
  genInput,
  genSelect,
  genInputNumber,
  genTextarea,
  genDictSelect
} from '/@/utils/formUtils';
import formValidate from '/@/utils/formValidate';
import baseUserInfoRegistryApi from '/@/api/example/BaseUserInfoRegistryApi';
import { getDictionaryDataSelector } from '/@/api/systemData/dictionary';
import dayjs from 'dayjs';

defineOptions({ name: 'masterListUser-userInfoUpdate-form' });

const emit = defineEmits(['register', 'reload']);
const { createMessage } = useMessage();
const id = ref('');

const getTitle = computed(() => (!unref(id) ? '新增人员信息' : '编辑人员信息'));

// 组件销毁标志
const isDestroyed = ref(false);

// 动态选项数据
const nationOptions = ref<any[]>([]);
const educationOptions = ref<any[]>([]);

// 组件销毁时的清理
onUnmounted(() => {
  isDestroyed.value = true;
});

// 性别选项
const genderOptions = [
  { fullName: '男', id: 1 },
  { fullName: '女', id: 2 },
];

// 政治面貌选项
const politicalOutlookOptions = [
  { fullName: '中共党员', id: '中共党员' },
  { fullName: '中共预备党员', id: '中共预备党员' },
  { fullName: '共青团员', id: '共青团员' },
  { fullName: '积极分子', id: '积极分子' },
  { fullName: '群众', id: '群众' },
];

// 学历选项将从数据字典获取

// 婚姻状况选项
const maritalStatusOptions = [
  { fullName: '未婚', id: '未婚' },
  { fullName: '已婚', id: '已婚' },
  { fullName: '离异', id: '离异' },
  { fullName: '丧偶', id: '丧偶' },
];

// 动态表单配置
const formSchemas = computed(() => [
    // 基本信息
    {
      field: 'divider1',
      component: 'Divider',
      label: '基本信息',
      colProps: { span: 24 },
    },
    {
      ...genInput('身份证号', 'identificationNumber', true),
      rules: [
        { required: true, message: '身份证号不能为空', trigger: 'blur' },
        { validator: formValidate('idCard'), trigger: 'blur' },
      ],
      colProps: { span: 12 },
    },
    {
      ...genInput('姓名', 'realName', true),
      colProps: { span: 12 },
    },
    {
      ...genInputNumber('年龄', 'age', false),
      colProps: { span: 12 },
    },
    {
      ...genSelect('性别', 'gender', false, 'number', genderOptions),
      colProps: { span: 12 },
    },
    {
      ...genInput('联系方式', 'mobilePhone', false),
      rules: [
        { validator: formValidate('iphone'), trigger: 'blur' },
      ],
      colProps: { span: 12 },
    },
    {
      ...genInput('紧急联系人', 'emergencyContacts', false),
      colProps: { span: 12 },
    },
    {
      ...genInput('紧急联系人电话', 'emergencyContactsPhone', false),
      rules: [
        { validator: formValidate('iphone'), trigger: 'blur' },
      ],
      colProps: { span: 12 },
    },
    
    // 个人详情
    {
      field: 'divider2',
      component: 'Divider',
      label: '个人详情',
      colProps: { span: 24 },
    },
    {
      ...genInput('籍贯', 'nativePlace', false),
      colProps: { span: 12 },
    },
    {
      field: 'nation',
      label: '民族',
      component: 'Select',
      componentProps: {
        placeholder: '请选择民族',
        options: nationOptions.value,
        fieldNames: { label: 'fullName', value: 'id' }
      },
      colProps: { span: 12 },
    },
    {
      ...genInput('家庭住址', 'homeAddress', false),
      colProps: { span: 24 },
    },
    {
      ...genSelect('政治面貌', 'politicalOutlook', false, 'string', politicalOutlookOptions),
      colProps: { span: 12 },
    },
    {
      ...genSelect('婚姻状况', 'maritalStatus', false, 'string', maritalStatusOptions),
      colProps: { span: 12 },
    },
    
    // 教育信息
    {
      field: 'divider3',
      component: 'Divider',
      label: '教育信息',
      colProps: { span: 24 },
    },
    {
      field: 'education',
      label: '学历',
      component: 'Select',
      componentProps: {
        placeholder: '请选择学历',
        options: educationOptions.value,
        fieldNames: { label: 'fullName', value: 'id' }
      },
      colProps: { span: 12 },
    },
    {
      ...genInput('所学专业', 'specialty', false),
      colProps: { span: 12 },
    },
    {
      ...genInput('毕业学校', 'graduationSchool', false),
      colProps: { span: 12 },
    },
    {
      field: 'graduationTime',
      label: '毕业时间',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择毕业时间',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      colProps: { span: 12 },
    },

    // 工作信息
    {
      field: 'divider4',
      component: 'Divider',
      label: '工作信息',
      colProps: { span: 24 },
    },
    {
      field: 'joinWorkTime',
      label: '首次参加工作时间',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择首次参加工作时间',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      colProps: { span: 12 },
    },
    {
      ...genInputNumber('本工种工作年限', 'seniority', false, '年'),
      colProps: { span: 12 },
    },
    {
      ...genInput('岗位序列', 'positionSequence', false),
      colProps: { span: 12 },
    },
    {
      ...genInput('人员类别', 'categoryPersonnel', false),
      colProps: { span: 12 },
    },
    {
      ...genInput('用工方式', 'formEmployment', false),
      colProps: { span: 12 },
    },
    {
      ...genInput('来源单位', 'sourceUnit', false),
      colProps: { span: 12 },
    },
    {
      field: 'goProjectTime',
      label: '进项目时间',
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择进项目时间',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      colProps: { span: 12 },
    },

    // 备注
    {
      field: 'divider5',
      component: 'Divider',
      label: '备注信息',
      colProps: { span: 24 },
    },
    {
      ...genTextarea('备注', 'remark', false),
      colProps: { span: 24 },
    },
]);

// 表单配置
const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  schemas: formSchemas,
  labelWidth: 120,
  baseColProps: { span: 24 },
});

// 加载民族数据字典
function loadNationOptions() {
  if (isDestroyed.value) return Promise.resolve();

  return getDictionaryDataSelector('b6cd65a763fa45eb9fe98e5057693e40').then(res => {
    if (!isDestroyed.value) {
      nationOptions.value = res.data.list || [];
      console.log('民族数据字典加载完成:', nationOptions.value);
    }
  }).catch(error => {
    if (!isDestroyed.value) {
      console.error('加载民族数据字典失败:', error);
      nationOptions.value = [];
    }
  });
}

// 加载学历数据字典
function loadEducationOptions() {
  if (isDestroyed.value) return Promise.resolve();

  return getDictionaryDataSelector('6a6d6fb541b742fbae7e8888528baa16').then(res => {
    if (!isDestroyed.value) {
      educationOptions.value = res.data.list || [];
      console.log('学历数据字典加载完成:', educationOptions.value);
    }
  }).catch(error => {
    if (!isDestroyed.value) {
      console.error('加载学历数据字典失败:', error);
      educationOptions.value = [];
    }
  });
}

// 弹窗注册
const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);

async function init(data: any) {
  resetFields();
  id.value = data?.id || '';

  // 先加载数据字典数据，等待完成后再处理表单数据
  await Promise.all([
    loadNationOptions(),
    loadEducationOptions()
  ]);

  if (unref(id)) {
    changeLoading(true);
    try {
      // 编辑模式，加载数据
      const res = await baseUserInfoRegistryApi.getById(unref(id));
      let record = res.data || res; // 兼容不同的返回格式
      console.log('加载的记录数据:', record);

      // 确保 record 是一个可写的对象
      record = { ...record };

      // 将日期字符串转换为 dayjs 对象
      if (record.graduationTime) {
        record.graduationTime = dayjs(record.graduationTime);
      }
      if (record.joinWorkTime) {
        record.joinWorkTime = dayjs(record.joinWorkTime);
      }
      if (record.goProjectTime) {
        record.goProjectTime = dayjs(record.goProjectTime);
      }

      setFieldsValue(record);
    } catch (error) {
      console.error('加载数据失败:', error);
      createMessage.error('加载数据失败');
    } finally {
      changeLoading(false);
    }
  }
}

// 提交表单
async function handleSubmit() {
  try {
    changeOkLoading(true);
    const values = await validate();
    
    console.log('提交的表单数据:', values);

    if (unref(id)) {
      // 编辑
      await baseUserInfoRegistryApi.update(unref(id), values);
      createMessage.success('更新成功');
    } else {
      // 新增
      await baseUserInfoRegistryApi.create(values);
      createMessage.success('创建成功');
    }

    closePopup();
    emit('reload');
  } catch (error) {
    console.error('表单提交失败:', error);
    createMessage.error('提交失败，请检查表单数据');
  } finally {
    changeOkLoading(false);
  }
}
</script>
