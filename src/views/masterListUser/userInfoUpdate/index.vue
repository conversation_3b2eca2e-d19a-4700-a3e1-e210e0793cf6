<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
<!--            <a-button @click="addOrUpdateHandle()" type="primary" preIcon="icon-ym icon-ym-btn-add">-->
<!--              新增人员信息-->
<!--            </a-button>-->
            <a-button
              v-if="selectedRowKeys.length > 0"
              @click="handleBatchApprove()"
              type="primary"
              class="ml-2"
            >
              批量审核通过
            </a-button>
            <a-button
              v-if="selectedRowKeys.length > 0"
              @click="handleBatchReject()"
              type="primary"
              danger
              class="ml-2"
            >
              批量审核拒绝
            </a-button>
            <a-button @click="handleExport" class="ml-2" preIcon="icon-ym icon-ym-btn-export">
              导出
            </a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'gender'">
              <span>{{ getGenderText(record.gender) }}</span>
            </template>
            <template v-if="column.key === 'nation'">
              <span>{{ getNationText(record.nation) }}</span>
            </template>
            <template v-if="column.key === 'education'">
              <span>{{ getEducationText(record.education) }}</span>
            </template>
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-if="column.key === 'creatorTime'">
              <span>{{ formatDateTimeSafely(record.creatorTime) }}</span>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

    <!-- 表单弹窗 -->
    <Form @register="registerForm" @reload="reload" />

    <!-- 审核弹窗 -->
    <ReviewModal @register="registerReviewModal" @reload="reload" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ActionItem, BasicTable, TableAction, useTable } from '/@/components/Table';
import { usePopup } from '/@/components/Popup';
import { useModal } from '/@/components/Modal';
import { useMessage } from '/@/hooks/web/useMessage';
import {
  genQueryInput,
  genQuerySelect,
  genQuerySearch,
  genEditBtn,
  genDeleteBtn
} from '/@/utils/tableUtils';
import baseUserInfoRegistryApi from '/@/api/example/BaseUserInfoRegistryApi';
import { getDictionaryDataSelector } from '/@/api/systemData/dictionary';
import { update as masterListUserUpdate } from '/@/views/masterListUser/helper/api';
import * as XLSX from 'xlsx';
import dayjs from 'dayjs';
import Form from './Form.vue';
import ReviewModal from './ReviewModal.vue';

defineOptions({ name: 'masterListUser-userInfoUpdate' });

const { createMessage } = useMessage();
const selectedRowKeys = ref<string[]>([]);

// 数据字典映射
const nationMap = ref<Record<string, string>>({});
const educationMap = ref<Record<string, string>>({});

// 人员信息更新状态枚举
const USER_INFO_REGISTRY_STATUS = {
  PENDING: 0,      // 待审核
  APPROVED: 1,     // 已审核通过
  REJECTED: 2,     // 已拒绝
  APPLIED: 3,      // 已应用
} as const;

const USER_INFO_REGISTRY_STATUS_MAP = {
  [USER_INFO_REGISTRY_STATUS.PENDING]: '待审核',
  [USER_INFO_REGISTRY_STATUS.APPROVED]: '已审核通过',
  [USER_INFO_REGISTRY_STATUS.REJECTED]: '已拒绝',
  [USER_INFO_REGISTRY_STATUS.APPLIED]: '已应用',
};

const USER_INFO_REGISTRY_STATUS_COLOR_MAP = {
  [USER_INFO_REGISTRY_STATUS.PENDING]: 'orange',
  [USER_INFO_REGISTRY_STATUS.APPROVED]: 'green',
  [USER_INFO_REGISTRY_STATUS.REJECTED]: 'red',
  [USER_INFO_REGISTRY_STATUS.APPLIED]: 'blue',
};

// 性别枚举
const GENDER = {
  MALE: 1,    // 男
  FEMALE: 2,  // 女
} as const;

const GENDER_MAP = {
  [GENDER.MALE]: '男',
  [GENDER.FEMALE]: '女',
};

// 状态选项
const statusOptions = [
  { fullName: '待审核', id: USER_INFO_REGISTRY_STATUS.PENDING },
  { fullName: '已审核通过', id: USER_INFO_REGISTRY_STATUS.APPROVED },
  { fullName: '已拒绝', id: USER_INFO_REGISTRY_STATUS.REJECTED },
  { fullName: '已应用', id: USER_INFO_REGISTRY_STATUS.APPLIED },
];

// 性别选项
const genderOptions = [
  { fullName: '男', id: GENDER.MALE },
  { fullName: '女', id: GENDER.FEMALE },
];

// 表格列配置
const columns = [
  { title: '身份证号', dataIndex: 'identificationNumber', width: 180 },
  { title: '姓名', dataIndex: 'realName', width: 100 },
  { title: '年龄', dataIndex: 'age', width: 80 },
  { title: '性别', dataIndex: 'gender', width: 80 },
  { title: '民族', dataIndex: 'nation', width: 80 },
  { title: '学历', dataIndex: 'education', width: 80 },
  { title: '联系方式', dataIndex: 'mobilePhone', width: 120 },
  { title: '状态', dataIndex: 'status', width: 100 },
];

// 表格配置
const [registerTable, { reload, getForm }] = useTable({
  api: baseUserInfoRegistryApi.page,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('身份证号', 'identificationNumber'),
      genQueryInput('姓名', 'realName'),
      genQueryInput('部门', 'organizeName'),
      genQuerySelect('状态', 'status', statusOptions),
    ],
  },
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
  rowSelection: {
    onChange: (selectedKeys: string[]) => {
      selectedRowKeys.value = selectedKeys;
    },
  },
  searchInfo: {
    _sorter: 'f_creator_time DESC',
  },
});

// 弹窗注册
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerReviewModal, { openModal: openReviewModal }] = useModal();

// 初始化数据字典映射
loadDictionaryMaps();

// 状态相关方法
function getStatusColor(status: number) {
  return USER_INFO_REGISTRY_STATUS_COLOR_MAP[status] || 'default';
}

function getStatusText(status: number) {
  return USER_INFO_REGISTRY_STATUS_MAP[status] || '未知';
}

function getGenderText(gender: number) {
  return GENDER_MAP[gender] || '未知';
}

function getNationText(nationId: string) {
  return nationMap.value[nationId] || nationId || '未知';
}

function getEducationText(educationId: string) {
  return educationMap.value[educationId] || educationId || '未知';
}

// 安全的日期格式化函数
function formatDateSafely(dateStr: string | null | undefined): string {
  if (!dateStr) return '';

  try {
    let cleanDateStr = dateStr.trim();

    // 处理各种可能的日期格式
    if (cleanDateStr.includes(' ')) {
      // 如果包含空格，取日期部分
      cleanDateStr = cleanDateStr.split(' ')[0];
    }

    // 验证并格式化日期
    if (cleanDateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
      // 标准格式，直接返回
      return cleanDateStr;
    } else if (cleanDateStr.match(/^\d{4}-\d{1,2}-\d{1,2}$/)) {
      // 非标准格式，补零
      const parts = cleanDateStr.split('-');
      return `${parts[0]}-${parts[1].padStart(2, '0')}-${parts[2].padStart(2, '0')}`;
    } else {
      // 尝试用dayjs解析
      const parsed = dayjs(cleanDateStr);
      return parsed.isValid() ? parsed.format('YYYY-MM-DD') : '';
    }
  } catch (error) {
    console.warn('日期格式化失败:', dateStr, error);
    return '';
  }
}

// 安全的日期时间格式化函数
function formatDateTimeSafely(dateStr: string | null | undefined): string {
  if (!dateStr) return '';

  try {
    const parsed = dayjs(dateStr);
    return parsed.isValid() ? parsed.format('YYYY-MM-DD HH:mm:ss') : '';
  } catch (error) {
    console.warn('日期时间格式化失败:', dateStr, error);
    return '';
  }
}

// 加载数据字典映射
function loadDictionaryMaps() {
  // 加载民族数据字典
  getDictionaryDataSelector('b6cd65a763fa45eb9fe98e5057693e40').then(res => {
    const list = res.data.list || [];
    const map: Record<string, string> = {};
    list.forEach(item => {
      map[item.id] = item.fullName;
    });
    nationMap.value = map;
  }).catch(error => {
    console.error('加载民族数据字典失败:', error);
  });

  // 加载学历数据字典
  getDictionaryDataSelector('6a6d6fb541b742fbae7e8888528baa16').then(res => {
    const list = res.data.list || [];
    const map: Record<string, string> = {};
    list.forEach(item => {
      map[item.id] = item.fullName;
    });
    educationMap.value = map;
  }).catch(error => {
    console.error('加载学历数据字典失败:', error);
  });
}

// 表格操作按钮
function getTableActions(record: any): ActionItem[] {
  const actions: ActionItem[] = [];

  // 编辑按钮 - 只有待审核状态可以编辑
  if (record.status === USER_INFO_REGISTRY_STATUS.PENDING) {
    actions.push(genEditBtn(record, addOrUpdateHandle));
  }

  // 审核按钮 - 只有待审核状态可以审核
  if (record.status === USER_INFO_REGISTRY_STATUS.PENDING) {
    actions.push({
      label: '审核',
      onClick: () => handleReview(record),
    });
  }

  // 应用到用户表按钮 - 只有已审核通过状态可以应用
  if (record.status === USER_INFO_REGISTRY_STATUS.APPROVED) {
    actions.push({
      label: '应用到用户表',
      onClick: () => handleApplyToUser(record.id),
    });
  }

  // 删除按钮 - 待审核和已拒绝状态可以删除
  if ([USER_INFO_REGISTRY_STATUS.PENDING, USER_INFO_REGISTRY_STATUS.REJECTED].includes(record.status)) {
    actions.push(genDeleteBtn(record, handleDelete));
  }

  return actions;
}

// 操作方法
function addOrUpdateHandle(id = '') {
  openFormPopup(true, { id });
}

function handleDelete(id: string) {
  baseUserInfoRegistryApi.remove(id).then(res => {
    createMessage.success(res.msg || '删除成功');
    reload();
  }).catch(error => {
    console.error('删除失败:', error);
    createMessage.error('删除失败');
  });
}

function handleReview(record: any) {
  openReviewModal(true, { record });
}

async function handleApplyToUser(id: string) {
  try {
    // 先获取记录详情
    const recordDetail = await baseUserInfoRegistryApi.getDetailById(id);

    // 将originalUserId替换为id，调用masterListUser的update接口
    const masterListUpdateData = {
      ...recordDetail.data,
      id: recordDetail.data.originalUserId // 将originalUserId替换为id
    };

    // 移除originalUserId字段，避免冲突
    delete masterListUpdateData.originalUserId;

    // 调用masterListUser的update接口
    await masterListUserUpdate(masterListUpdateData);


    createMessage.success('应用成功');
    reload();
  } catch (error) {
    console.error('应用失败:', error);
    createMessage.error('应用失败');
  }
}

function handleBatchApprove() {
  if (selectedRowKeys.value.length === 0) {
    createMessage.warning('请选择要审核的记录');
    return;
  }

  baseUserInfoRegistryApi.batchApprove(selectedRowKeys.value).then(res => {
    createMessage.success(res.msg || '批量审核通过成功');
    selectedRowKeys.value = [];
    reload();
  }).catch(error => {
    console.error('批量审核失败:', error);
    createMessage.error('批量审核失败');
  });
}

function handleBatchReject() {
  if (selectedRowKeys.value.length === 0) {
    createMessage.warning('请选择要审核的记录');
    return;
  }

  baseUserInfoRegistryApi.batchReject(selectedRowKeys.value).then(res => {
    createMessage.success(res.msg || '批量审核拒绝成功');
    selectedRowKeys.value = [];
    reload();
  }).catch(error => {
    console.error('批量审核失败:', error);
    createMessage.error('批量审核失败');
  });
}

async function handleExport() {
  try {
    createMessage.loading('正在导出数据...', 0);

    // 获取搜索参数
    const searchParams = getForm().getFieldsValue();

    // 获取所有数据（不分页）
    const response = await baseUserInfoRegistryApi.page({
      ...searchParams,
      currentPage: 1,
      pageSize: 10000, // 设置一个较大的数值获取所有数据
    });

    const data = response.data?.list || [];

    if (data.length === 0) {
      createMessage.warning('没有数据可导出');
      return;
    }

    // 处理数据映射和格式化
    const processedData = data.map(item => ({
      '身份证号': item.identificationNumber || '',
      '姓名': item.realName || '',
      '年龄': item.age || '',
      '性别': getGenderText(item.gender),
      '民族': getNationText(item.nation),
      '学历': getEducationText(item.education),
      '联系方式': item.mobilePhone || '',
      '紧急联系人': item.emergencyContacts || '',
      '紧急联系人电话': item.emergencyContactsPhone || '',
      '籍贯': item.nativePlace || '',
      '家庭住址': item.homeAddress || '',
      '政治面貌': item.politicalOutlook || '',
      '婚姻状况': item.maritalStatus || '',
      '所学专业': item.specialty || '',
      '毕业学校': item.graduationSchool || '',
      '毕业时间': formatDateSafely(item.graduationTime),
      '首次参加工作时间': formatDateSafely(item.joinWorkTime),
      '本工种工作年限': item.seniority ? `${item.seniority}年` : '',
      '部门': item.organizeName || '',
      '岗位': item.positionName || '',
      '岗位序列': item.positionSequence || '',
      '人员类别': item.categoryPersonnel || '',
      '用工方式': item.formEmployment || '',
      '来源单位': item.sourceUnit || '',
      '进项目时间': formatDateSafely(item.goProjectTime),
      '状态': getStatusText(item.status),
      '审核人': item.reviewerName || '',
      '审核时间': formatDateTimeSafely(item.reviewTime),
      '审核意见': item.reviewComment || '',
      '备注': item.remark || '',
      '创建时间': formatDateTimeSafely(item.creatorTime),
    }));

    // 使用XLSX导出Excel
    const worksheet = XLSX.utils.json_to_sheet(processedData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '人员信息更新登记表');

    // 设置列宽
    const colWidths = [
      { wch: 20 }, // 身份证号
      { wch: 10 }, // 姓名
      { wch: 8 },  // 年龄
      { wch: 8 },  // 性别
      { wch: 10 }, // 民族
      { wch: 10 }, // 学历
      { wch: 15 }, // 联系方式
      { wch: 12 }, // 紧急联系人
      { wch: 15 }, // 紧急联系人电话
      { wch: 15 }, // 籍贯
      { wch: 30 }, // 家庭住址
      { wch: 12 }, // 政治面貌
      { wch: 10 }, // 婚姻状况
      { wch: 20 }, // 所学专业
      { wch: 20 }, // 毕业学校
      { wch: 12 }, // 毕业时间
      { wch: 18 }, // 首次参加工作时间
      { wch: 15 }, // 本工种工作年限
      { wch: 20 }, // 部门
      { wch: 15 }, // 岗位
      { wch: 12 }, // 岗位序列
      { wch: 12 }, // 人员类别
      { wch: 12 }, // 用工方式
      { wch: 20 }, // 来源单位
      { wch: 12 }, // 进项目时间
      { wch: 10 }, // 状态
      { wch: 12 }, // 审核人
      { wch: 18 }, // 审核时间
      { wch: 30 }, // 审核意见
      { wch: 30 }, // 备注
      { wch: 18 }, // 创建时间
    ];
    worksheet['!cols'] = colWidths;

    // 生成文件名
    const fileName = `人员信息更新登记表_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;

    // 导出文件
    XLSX.writeFile(workbook, fileName);
    createMessage.success('导出成功');

  } catch (error) {
    console.error('导出失败:', error);
    createMessage.error('导出失败');
  } finally {
    createMessage.destroy();
  }
}
</script>

<style scoped lang="less">
.jnpf-content-wrapper {
  height: 100%;

  &-center {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  &-content {
    flex: 1;
    overflow: hidden;
  }
}
</style>
