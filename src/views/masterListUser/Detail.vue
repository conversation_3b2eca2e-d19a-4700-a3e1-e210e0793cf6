<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>
    <!-- 表单 -->
    <a-row class="dynamic-form">
      <a-form :colon="false" size="default" layout="horizontal" labelAlign="right" :labelCol="{ style: { width: '100px' } }" :model="dataForm" ref="formRef">
        <a-row :gutter="15">
          <!-- 具体表单 -->
          <!-- <a-col :span="24" class="ant-col-item">
            <a-form-item name="sourceUnit">
              <template #label>来源单位</template>
              <p>{{ dataForm.sourceUnit }}</p>
            </a-form-item>
          </a-col> -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="realName">
              <template #label>姓名</template>
              <p>{{ dataForm.realName }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="gender">
              <template #label>性别</template>
              <p>{{ dataForm.gender }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="organizeId">
              <template #label>部门</template>
              <p>{{ dataForm.organizeId }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="positionId">
              <template #label>岗位</template>
              <p>{{ dataForm.positionId }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="seniority">
              <template #label>工龄</template>
              <p>{{ dataForm.seniority }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="interiorSeniority">
              <template #label>内部工龄</template>
              <p>{{ dataForm.interiorSeniority }}</p>
            </a-form-item>
          </a-col>
          <!-- <a-col :span="24" class="ant-col-item">
            <a-form-item name="identificationNumber">
              <template #label>身份证号</template>
              <p>{{ dataForm.identificationNumber }}</p>
            </a-form-item>
          </a-col> -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="mobilePhone">
              <template #label>手机</template>
              <p>{{ dataForm.mobilePhone }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="trainingTime">
              <template #label>培训时间</template>
              <p>{{ dataForm.trainingTime }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="trainingStatus">
              <template #label>培训状态</template>
              <p>{{ dataForm.trainingStatus }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="birthday">
              <template #label>生日</template>
              <p>{{ dataForm.birthday }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="team">
              <template #label>班组</template>
              <p>{{ dataForm.team }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="age">
              <template #label>年龄</template>
              <p>{{ dataForm.age }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="nation">
              <template #label>民族</template>
              <p>{{ dataForm.nation }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="education">
              <template #label>学历</template>
              <p>{{ dataForm.education }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="nativePlace">
              <template #label>籍贯</template>
              <p>{{ dataForm.nativePlace }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="categoryPersonnel">
              <template #label>人员类别</template>
              <p>{{ dataForm.categoryPersonnel }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="goProjectTime">
              <template #label>进入项目时间</template>
              <p>{{ dataForm.goProjectTime }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="bloodType">
              <template #label>血型</template>
              <p>{{ dataForm.bloodType }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="formEmployment">
              <template #label>用工形式</template>
              <p>{{ dataForm.formEmployment }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="natureAccount">
              <template #label>户口性质</template>
              <p>{{ dataForm.natureAccount }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="positionLevel">
              <template #label>岗位层级</template>
              <p>{{ dataForm.positionLevel }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="positionSequence">
              <template #label>岗位序列</template>
              <p>{{ dataForm.positionSequence }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="project">
              <template #label>项目</template>
              <p>{{ dataForm.project }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="politicalOutlook">
              <template #label>政治面貌</template>
              <p>{{ dataForm.politicalOutlook }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="graduationSchool">
              <template #label>毕业学校</template>
              <p>{{ dataForm.graduationSchool }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="specialty">
              <template #label>所学专业</template>
              <p>{{ dataForm.specialty }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="graduationTime">
              <template #label>毕业时间</template>
              <p>{{ dataForm.graduationTime }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="joinFiveFirmTime">
              <template #label>进入五公司时间</template>
              <p>{{ dataForm.joinFiveFirmTime }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="joinWorkTime">
              <template #label>参加工作时间</template>
              <p>{{ dataForm.joinWorkTime }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="technicalTitle">
              <template #label>专业技术职称</template>
              <p>{{ dataForm.technicalTitle }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="workerSkillLevel">
              <template #label>工人技能等级</template>
              <p>{{ dataForm.workerSkillLevel }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="professionalQual">
              <template #label>职业资格</template>
              <p>{{ dataForm.professionalQual }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="sourceClassify">
              <template #label>来源分类</template>
              <p>{{ dataForm.sourceClassify }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="constructionExperience">
              <template #label>核电建设经历</template>
              <p>{{ dataForm.constructionExperience }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="officeLocation">
              <template #label>办公地点</template>
              <p>{{ dataForm.officeLocation }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="officePhone">
              <template #label>办公电话</template>
              <p>{{ dataForm.officePhone }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="email">
              <template #label>邮箱</template>
              <p>{{ dataForm.email }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="emergencyContacts">
              <template #label>紧急联系人</template>
              <p>{{ dataForm.emergencyContacts }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="emergencyContactsPhone">
              <template #label>紧急联系人电话</template>
              <p>{{ dataForm.emergencyContactsPhone }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="maritalStatus">
              <template #label>婚姻状况</template>
              <p>{{ dataForm.maritalStatus }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="number">
              <template #label>人数</template>
              <p>{{ dataForm.number }}</p>
            </a-form-item>
          </a-col>
          <!-- <a-col :span="24" class="ant-col-item">
            <a-form-item name="homeAddress">
              <template #label>家庭住址</template>
              <p>{{ dataForm.homeAddress }}</p>
            </a-form-item>
          </a-col> -->
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="phase">
              <template #label>一二期</template>
              <p>{{ dataForm.phase }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="removeTime">
              <template #label>撤场时间</template>
              <p>{{ dataForm.removeTime }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="removeRemark">
              <template #label>撤场原因</template>
              <p>{{ dataForm.removeRemark }}</p>
            </a-form-item>
          </a-col>
          <a-col :span="24" class="ant-col-item">
            <a-form-item name="remark">
              <template #label>备注</template>
              <p>{{ dataForm.remark }}</p>
            </a-form-item>
          </a-col>
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
  <!-- 有关联表单详情：开始 -->
  <RelationDetail ref="relationDetailRef" />
  <!-- 有关联表单详情：结束 -->
</template>
<script lang="ts" setup>
  import { getDetailInfo } from './helper/api';
  import { getConfigData } from '/@/api/onlineDev/visualDev';
  import { reactive, toRefs, nextTick, ref, computed, unref, toRaw } from 'vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  // 有关联表单详情
  import RelationDetail from '/@/views/common/dynamicModel/list/detail/index.vue';
  // 表单权限
  import { usePermission } from '/@/hooks/web/usePermission';
  import { useMessage } from '/@/hooks/web/useMessage';

  interface State {
    dataForm: any;
    title: string;
  }

  defineOptions({ name: 'Detail' });
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();

  const relationDetailRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: '详情',
  });
  const { title, dataForm } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }
  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function toDetail(modelId, id) {
    if (!id) return;
    getConfigData(modelId).then(res => {
      if (!res.data || !res.data.formData) return;
      const formConf = JSON.parse(res.data.formData);
      formConf.popupType = 'general';
      const data = { id, formConf, modelId };
      relationDetailRef.value?.init(data);
    });
  }
  function setFormProps(data) {
    setModalProps(data);
  }
  function changeLoading(loading) {
    setFormProps({ loading });
  }
</script>
