<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="批量导入"
    :width="1000"
    :show-cancel-btn="false"
    :show-ok-btn="false"
    destroyOnClose
    class="jnpf-import-modal">
    <FaImportHeaderStep v-model:current="activeStep" />

    <div class="import-main" v-show="activeStep == 0">
      <FaUploadCard v-model:value="fileId" />
      <FaDownloadCard download-url="/file/撤场导入.xlsx" />
    </div>

    <div class="import-main" v-show="activeStep == 1">
      <a-table :data-source="list" :columns="columns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '440px' }" class="import-preview-table">
        <template #bodyCell="{ column, record, index }">
          <template v-for="item in tableData">
            <template v-if="column.key === item.dataIndex">
              <a-input v-model:value="record[column.key]" />
            </template>
          </template>
          <template v-if="column.key === 'action'">
            <a-button class="action-btn" type="link" color="error" @click="handleDelItem(index)" size="small">删除</a-button>
          </template>
        </template>
      </a-table>
    </div>

    <div class="import-main" v-show="activeStep == 2">
      <FaImportSuccessCard v-if="!result.resultType" :success-num="result.snum" />
      <FaImportFailCard v-if="result.resultType" :success-num="result.snum" :fail-num="result.fnum">
        <a-table :data-source="resultList" :columns="resultColumns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '205px' }" />
      </FaImportFailCard>
    </div>

    <template #insertFooter>
      <a-button @click="handleClose()" v-if="activeStep == 0">{{ t('common.cancelText') }}</a-button>
      <a-button @click="handlePrev" v-if="activeStep === 1">{{ t('common.prev') }}</a-button>
      <a-button type="primary" @click="handleNext" :loading="btnLoading" v-if="activeStep < 2" :disabled="activeStep === 0 && !fileId">{{
        t('common.next')
      }}</a-button>
      <a-button type="primary" @click="handleClose(true)" v-else>关闭</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { reactive, toRefs } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { removeImportData, removeImportPreview } from './helper/api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';

  interface State {
    activeStep: number;
    fileId?: string;
    btnLoading: boolean;
    list: any[];
    result: any;
    resultList: any[];
  }

  const emit = defineEmits(['register', 'reload']);
  const [registerModal, { closeModal }] = useModalInner(init);
  const { createMessage } = useMessage();
  const { t } = useI18n();
  const tableData = [
    // { title: '序号', dataIndex: 'id', width: 50 },
    { title: '姓名', dataIndex: 'realName', width: 80 },
    { title: '身份证号', dataIndex: 'identificationNumber', width: 80 },
  ];
  const columns: any[] = [
    // {width: 50, title: '序号', align: 'center', customRender: ({index}) => index + 1},
    ...tableData,
    { title: '操作', dataIndex: 'action', key: 'action', width: 50, fixed: 'right' },
  ];
  const resultColumns: any[] = [
    // {width: 50, title: '序号', align: 'center', customRender: ({index}) => index + 1},
    ...tableData,
  ];

  const state = reactive<State>({
    activeStep: 0,
    fileId: undefined,
    btnLoading: false,
    list: [],
    result: {},
    resultList: [],
  });
  const { activeStep, fileId, btnLoading, list, result, resultList } = toRefs(state);

  function init() {
    state.activeStep = 0;
    state.fileId = undefined;
    state.btnLoading = false;
  }

  function handlePrev() {
    if (state.activeStep == 0) return;
    state.activeStep -= 1;
  }

  function handleNext() {
    if (state.activeStep == 0) {
      if (!state.fileId) return createMessage.warning('请先上传文件');
      state.btnLoading = true;
      removeImportPreview({ fileId: state.fileId })
        .then(res => {
          state.list = res.data || [];
          state.btnLoading = false;
          state.activeStep += 1;
        })
        .catch(() => {
          state.btnLoading = false;
        });
      return;
    }
    if (state.activeStep == 1) {
      if (!state.list.length) return createMessage.warning('导入数据为空');
      state.btnLoading = true;
      removeImportData({ list: state.list })
        .then(res => {
          state.result = res.data;
          state.resultList = res.data.failResult;
          state.btnLoading = false;
          state.activeStep += 1;
        })
        .catch(() => {
          state.btnLoading = false;
        });
    }
  }

  function handleDelItem(index) {
    state.list.splice(index, 1);
  }

  function handleClose(reload = false) {
    closeModal();
    if (reload) emit('reload');
  }
</script>
