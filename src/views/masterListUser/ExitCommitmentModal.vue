<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @ok="handleSubmit" title="上传人员资料附件">
    <a-row class="dynamic-form" style="min-height: 500px">
      <a-form
        :colon="false"
        size="default"
        layout="horizontal"
        labelAlign="right"
        :labelCol="{ style: { width: '100px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">
<!--          <a-col :span="24" class="ant-col-item">-->
<!--            <div>附件批量支持同时上传多个附件，系统会根据附件后缀名自动解析对应到相应的文件上。</div>-->
<!--          </a-col>-->

          <a-col :span="24" class="ant-col-item">
            <a-form-item label="退场承诺书">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.exitCommitment" prefix="contract" accept=".pdf" />
            </a-form-item>
          </a-col>
          
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, toRefs } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import {getFile, uploadExitCommitment, uploadSubmitFile} from './helper/api';
import JnpfFaUploadFileQiniu from '/@/components/Jnpf/Upload/src/FaUploadFileQiniu.vue';
import { useMessage } from "/@/hooks/web/useMessage";
import { each } from "lodash-es";


const emit = defineEmits(['register', 'reload']);
const [registerModal, {changeLoading, closeModal, changeOkLoading}] = useModalInner(init);
const {createMessage} = useMessage();

interface State {
  dataForm: {
    exitCommitment?: string,
  };
  dataRule: any;
}

const state = reactive<State>({
  dataForm: {
    exitCommitment: undefined,
  },
  dataRule: {},
});

const {dataRule, dataForm} = toRefs(state);

function init(id) {
  // 打印外部传给modal的参数
  console.log('init', id);
  state.dataForm.exitCommitment = undefined;
  changeLoading(true);
  getFile(id).then(res => {
    // 打印返回数据
    console.log('res', res);
    state.dataForm = res.data;
    changeLoading(false);
  }).catch(() => changeLoading(false))
}

/**
 * 判断文件结尾字符串，自动分配到指定文件字段
 * @param file
 */
function handleFileListSuccess(file: any[]) {
  console.log('handleFileListSuccess', file)
  const fileName = file.originalFilename.toLocaleLowerCase()
  if (fileName.endsWith('退场承诺书.pdf')) {
    dataForm.value.exitCommitment = file.id;
  }
}

async function handleSubmit() {
  // 打印提交信息
  const params = {
    id: state.dataForm.id,
    exitCommitment: state.dataForm.exitCommitment,
  }

  changeOkLoading(true);
  uploadExitCommitment(params).then(res => {
    // 打印返回数据
    console.log('res', res);
    createMessage.success("上传附件成功")
    changeOkLoading(false);
    closeModal();
    emit('reload'); // 发布reload事件，外部组件接受此事件
  }).catch(() => changeOkLoading(false))
}
</script>
