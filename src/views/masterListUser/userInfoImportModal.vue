<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="批量导入"
    :width="1000"
    :show-cancel-btn="false"
    :show-ok-btn="false"
    destroyOnClose
    class="jnpf-import-modal">
    <FaImportHeaderStep v-model:current="activeStep" />

    <div class="import-main" v-show="activeStep == 0">
      <FaUploadCard v-model:value="fileId" />
      <FaDownloadCard download-url="/file/人员信息更新.xlsx" />
    </div>

    <div class="import-main" v-show="activeStep == 1">
      <a-alert
        v-if="hasErrors"
        message="数据校验失败"
        description="存在校验错误的记录无法保存，请修正错误后重新上传文件。"
        type="warning"
        show-icon
        style="margin-bottom: 16px;"
      />
      <a-table :data-source="list" :columns="columns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '440px' }" class="import-preview-table">
        <template #bodyCell="{ column, record, index }">
          <template v-for="item in tableData">
            <template v-if="column.key === item.dataIndex && item.dataIndex !== 'errorsInfo'">
              <a-input v-model:value="record[column.key]"/>
            </template>
          </template>
          <template v-if="column.key === 'errorsInfo'">
            <a-tooltip v-if="record.errorsInfo" :title="record.errorsInfo" placement="topLeft">
              <span style="color: #ff4d4f; cursor: help;">
                {{ record.errorsInfo.length > 20 ? record.errorsInfo.substring(0, 20) + '...' : record.errorsInfo }}
              </span>
            </a-tooltip>
            <span v-else style="color: #ff4d4f;">正常</span>
          </template>
          <template v-if="column.key === 'action'">
            <a-button class="action-btn" type="link" color="error" @click="handleDelItem(index)" size="small">删除</a-button>
          </template>
        </template>
      </a-table>
    </div>

    <div class="import-main" v-show="activeStep == 2">
      <FaImportSuccessCard v-if="!result.resultType" :success-num="result.snum" />
      <FaImportFailCard v-if="result.resultType" :success-num="result.snum" :fail-num="result.fnum">
        <a-table :data-source="resultList" :columns="resultColumns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '205px' }"/>
      </FaImportFailCard>
    </div>

    <template #insertFooter>
      <a-button @click="handleClose()" v-if="activeStep == 0">{{ t('common.cancelText') }}</a-button>
      <a-button @click="handlePrev" v-if="activeStep === 1">{{ t('common.prev') }}</a-button>

      <!-- 第一步：上传文件到数据预览 -->
      <a-button
        v-if="activeStep === 0"
        type="primary"
        @click="handleNext"
        :loading="btnLoading"
        :disabled="!fileId">
        {{ t('common.next') }}
      </a-button>

      <!-- 第二步：数据预览到数据导入，需要管控 -->
      <a-tooltip v-if="activeStep === 1 && hasErrors" title="存在校验错误，请修正后重新上传">
        <a-button
          type="primary"
          @click="handleNext"
          :loading="btnLoading"
          :disabled="hasErrors">
          {{ t('common.next') }}
        </a-button>
      </a-tooltip>
      <a-button
        v-if="activeStep === 1 && !hasErrors"
        type="primary"
        @click="handleNext"
        :loading="btnLoading">
        {{ t('common.next') }}
      </a-button>

      <a-button type="primary" @click="handleClose(true)" v-if="activeStep === 2">关闭</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
import {reactive, toRefs, computed} from 'vue';
import {BasicModal, useModalInner} from '/@/components/Modal';
import {useMessage} from '/@/hooks/web/useMessage';
import {useI18n} from '/@/hooks/web/useI18n';
import * as MasterUserApi from '/@/api/flowApi/masterUser';

interface State {
  activeStep: number;
  fileId?: string;
  btnLoading: boolean;
  list: any[];
  result: any;
  resultList: any[];
}

const emit = defineEmits(['register', 'reload']);
const [registerModal, {closeModal}] = useModalInner(init);
const {createMessage} = useMessage();
const {t} = useI18n();
const tableData = [
  {title: '序号', dataIndex: 'id', width: 50},
  {title: '项目', dataIndex: 'project', width: 100},
  {title: '部门', dataIndex: 'organizeId', width: 100},
  {title: '姓名', dataIndex: 'realName', width: 100},
  {title: '性别', dataIndex: 'gender', width: 50},
  {title: '身份证号', dataIndex: 'identificationNumber', width: 200},
  {title: '籍贯', dataIndex: 'nativePlace', width: 100},
  {title: '政治面貌', dataIndex: 'politicalOutlook', width: 100},
  {title: '毕业学校', dataIndex: 'graduationSchool', width: 100},
  {title: '所学专业', dataIndex: 'specialty', width: 100},
  {title: '学历', dataIndex: 'education', width: 50},
  {title: '毕业时间', dataIndex: 'graduationTime', width: 150},
  {title: '参加工作时间', dataIndex: 'joinWorkTime', width: 150},
  {title: '进入五公司时间', dataIndex: 'joinFiveFirmTime', width: 100},
  {title: '进入项目时间', dataIndex: 'goProjectTime', width: 150},
  {title: '年龄', dataIndex: 'age', width: 50},
  {title: '工龄', dataIndex: 'seniority', width: 100},
  {title: '内部工龄', dataIndex: 'interiorSeniority', width: 100},
  {title: '岗位', dataIndex: 'positionId', width: 100},
  {title: '岗位层级', dataIndex: 'positionLevel', width: 100},
  {title: '岗位序列', dataIndex: 'positionSequence', width: 100},
  {title: '人员类别', dataIndex: 'categoryPersonnel', width: 100},
  {title: '专业技术职称', dataIndex: 'technicalTitle', width: 100},
  {title: '工人技能等级', dataIndex: 'workerSkillLevel', width: 100},
  {title: '职业资格', dataIndex: 'professionalQual', width: 100},
  {title: '来源分类', dataIndex: 'sourceClassify', width: 100},
  {title: '用工形式', dataIndex: 'formEmployment', width: 100},
  {title: '核电建设经历', dataIndex: 'constructionExperience', width: 100},
  {title: '来源单位', dataIndex: 'sourceUnit', width: 100},
  {title: '办公地点', dataIndex: 'officeLocation', width: 100},
  {title: '办公电话', dataIndex: 'officePhone', width: 100},
  {title: '手机', dataIndex: 'mobilePhone', width: 150},
  {title: '邮箱', dataIndex: 'email', width: 100},
  {title: '紧急联系人', dataIndex: 'emergencyContacts', width: 100},
  {title: '紧急联系人电话', dataIndex: 'emergencyContactsPhone', width: 100},
  {title: '婚姻状况', dataIndex: 'maritalStatus', width: 100},
  {title: '民族', dataIndex: 'nation', width: 50},
  {title: '人数', dataIndex: 'number', width: 100},
  {title: '家庭住址', dataIndex: 'homeAddress', width: 100},
  {title: '一二期', dataIndex: 'phase', width: 100},
  {title: '培训时间', dataIndex: 'trainingTime', width: 150},
  {title: '生日', dataIndex: 'birthday', width: 150},
  {title: '班组', dataIndex: 'team', width: 100},
  {title: '血型', dataIndex: 'bloodType', width: 100},
  {title: '户口性质', dataIndex: 'natureAccount', width: 100},
  {title: '备注', dataIndex: 'remark', width: 100},
  {
    title: '错误信息',
    dataIndex: 'errorsInfo',
    width: 200,
    customHeaderCell: () => ({
      style: { color: '#ff4d4f !important', fontWeight: 'bold' }
    }),
    customCell: () => ({
      style: { color: '#ff4d4f' }
    })
  },
];
const columns: any[] = [
  // {width: 50, title: '序号', align: 'center', customRender: ({index}) => index + 1},
  ...tableData,
  {title: '操作', dataIndex: 'action', key: 'action', width: 50, fixed: 'right'},
];
const resultColumns: any[] = [
  // {width: 50, title: '序号', align: 'center', customRender: ({index}) => index + 1},
  ...tableData
];

const state = reactive<State>({
  activeStep: 0,
  fileId: undefined,
  btnLoading: false,
  list: [],
  result: {},
  resultList: [],
});
const {activeStep, fileId, btnLoading, list, result, resultList} = toRefs(state);

// 检查是否存在错误信息
const hasErrors = computed(() => {
  return state.list.some(item => item.errorsInfo && item.errorsInfo.trim() !== '');
});

function init() {
  state.activeStep = 0;
  state.fileId = undefined;
  state.btnLoading = false;
}

function handlePrev() {
  if (state.activeStep == 0) return;
  state.activeStep -= 1;
}

function handleNext() {
  if (state.activeStep == 0) {
    if (!state.fileId) return createMessage.warning('请先上传文件');
    state.btnLoading = true;
    MasterUserApi.importUserInfoPreview({fileId: state.fileId})
      .then(res => {
        state.list = res.data || [];
        state.btnLoading = false;
        state.activeStep += 1;
      })
      .catch(() => {
        state.btnLoading = false;
      });
    return;
  }
  if (state.activeStep == 1) {
    if (!state.list.length) return createMessage.warning('导入数据为空');
    state.btnLoading = true;
    MasterUserApi.importData({list: state.list})
      .then(res => {
        state.result = res.data;
        state.resultList = res.data.failResult;
        state.btnLoading = false;
        state.activeStep += 1;
      })
      .catch(() => {
        state.btnLoading = false;
      });
  }
}

function handleDelItem(index) {
  state.list.splice(index, 1);
}

function handleClose(reload = false) {
  closeModal();
  if (reload) emit('reload');
}
</script>

<style lang="less" scoped>
:deep(.ant-table-thead > tr > th:nth-last-child(2)) {
  color: #ff4d4f !important;
  font-weight: bold !important;
}

:deep(.ant-table-tbody > tr > td:nth-last-child(2)) {
  color: #ff4d4f !important;
}
</style>
