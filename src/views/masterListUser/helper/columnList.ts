const columnList = [
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '入场状态',
    label: '入场状态',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 102,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '入场状态',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718174685861,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'entryMark',
    width: 90,
    __vModel__: 'entryMark',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'entryMark',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  // {
  //   clearable: true,
  //   maxlength: null,
  //   jnpfKey: 'input',
  //   suffixIcon: '',
  //   fullName: '来源单位',
  //   label: '来源单位',
  //   sortable: false,
  //   align: 'left',
  //   addonAfter: '',
  //   __config__: {
  //     formId: 101,
  //     visibility: ['pc', 'app'],
  //     jnpfKey: 'input',
  //     noShow: false,
  //     tipLabel: '',
  //     dragDisabled: false,
  //     className: [],
  //     label: '来源单位',
  //     trigger: 'blur',
  //     showLabel: true,
  //     required: false,
  //     tableName: 'base_user',
  //     renderKey: 1718174662812,
  //     layout: 'colFormItem',
  //     tagIcon: 'icon-ym icon-ym-generator-input',
  //     tag: 'JnpfInput',
  //     regList: [],
  //     span: 24,
  //   },
  //   readonly: false,
  //   prop: 'sourceUnit',
  //   width: null,
  //   __vModel__: 'sourceUnit',
  //   showPassword: false,
  //   fixed: 'none',
  //   style: { width: '100%' },
  //   disabled: false,
  //   id: 'sourceUnit',
  //   placeholder: '请输入',
  //   prefixIcon: '',
  //   addonBefore: '',
  //   on: {
  //     change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
  //     blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
  //   },
  // },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '姓名',
    label: '姓名',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 102,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '姓名',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718174685861,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'realName',
    width: null,
    __vModel__: 'realName',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'realName',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    filterable: false,
    clearable: true,
    jnpfKey: 'select',
    multiple: false,
    fullName: '性别',
    label: '性别',
    sortable: false,
    align: 'left',
    props: { label: 'fullName', value: 'id' },
    __config__: {
      formId: 103,
      visibility: ['pc', 'app'],
      jnpfKey: 'select',
      defaultValue: '',
      noShow: false,
      dataType: 'static',
      dictionaryType: '',
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '性别',
      trigger: 'change',
      propsUrl: '',
      templateJson: [],
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718174710981,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-select',
      propsName: '',
      tag: 'JnpfSelect',
      regList: [],
      span: 24,
    },
    prop: 'genderText', // 使用映射后的字段
    width: 60,
    options: [
      { fullName: '男', id: '1' },
      { fullName: '女', id: '2' },
      { fullName: '保密', id: '3' },
    ],
    __vModel__: 'gender',
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'gender',
    placeholder: '请选择',
    on: { change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}' },
  },
  {
    filterable: false,
    clearable: true,
    jnpfKey: 'depSelect',
    ableIds: [],
    multiple: false,
    fullName: '部门',
    label: '部门',
    sortable: false,
    align: 'left',
    __config__: {
      formId: 104,
      visibility: ['pc', 'app'],
      jnpfKey: 'depSelect',
      defaultValue: null,
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '部门',
      trigger: 'change',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718174736246,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-tree-department1',
      defaultCurrent: false,
      tag: 'JnpfDepSelect',
      regList: [],
      span: 24,
    },
    prop: 'organizeText', // 使用映射后的字段
    width: 150,
    __vModel__: 'organizeId',
    fixed: 'none',
    style: { width: '100%' },
    selectType: 'all',
    disabled: false,
    id: 'organizeId',
    placeholder: '请选择',
    on: { change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}' },
  },
  {
    filterable: false,
    clearable: true,
    jnpfKey: 'posSelect',
    ableIds: [],
    multiple: false,
    fullName: '岗位',
    label: '岗位',
    sortable: false,
    align: 'left',
    __config__: {
      formId: 105,
      visibility: ['pc', 'app'],
      jnpfKey: 'posSelect',
      defaultValue: null,
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '岗位',
      trigger: 'change',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718174767518,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-jobs',
      defaultCurrent: false,
      tag: 'JnpfPosSelect',
      regList: [],
      span: 24,
    },
    prop: 'positionText', // 使用映射后的字段
    width: null,
    __vModel__: 'positionId',
    fixed: 'none',
    style: { width: '100%' },
    selectType: 'all',
    disabled: false,
    id: 'positionId',
    placeholder: '请选择',
    on: { change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}' },
  },
  // {
  //   clearable: true,
  //   maxlength: null,
  //   jnpfKey: 'input',
  //   suffixIcon: '',
  //   fullName: '身份证号',
  //   label: '身份证号',
  //   sortable: false,
  //   align: 'left',
  //   addonAfter: '',
  //   __config__: {
  //     formId: 106,
  //     visibility: ['pc', 'app'],
  //     jnpfKey: 'input',
  //     noShow: false,
  //     tipLabel: '',
  //     dragDisabled: false,
  //     className: [],
  //     label: '身份证号',
  //     trigger: 'blur',
  //     showLabel: true,
  //     required: false,
  //     tableName: 'base_user',
  //     renderKey: 1718174781597,
  //     layout: 'colFormItem',
  //     tagIcon: 'icon-ym icon-ym-generator-input',
  //     tag: 'JnpfInput',
  //     regList: [
  //       { pattern: '/^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/', message: '请输入正确的身份证号码' },
  //     ],
  //     span: 24,
  //   },
  //   readonly: false,
  //   prop: 'identificationNumber',
  //   width: 200,
  //   __vModel__: 'identificationNumber',
  //   showPassword: false,
  //   fixed: 'none',
  //   style: { width: '100%' },
  //   disabled: false,
  //   id: 'identificationNumber',
  //   placeholder: '请输入',
  //   prefixIcon: '',
  //   addonBefore: '',
  //   on: {
  //     change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
  //     blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
  //   },
  // },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '工龄',
    label: '工龄',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 106,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '工龄',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718174781597,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'seniority',
    width: 80,
    __vModel__: 'seniority',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'seniority',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '内部工龄',
    label: '内部工龄',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 106,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '内部工龄',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718174781597,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'interiorSeniority',
    width: 100,
    __vModel__: 'interiorSeniority',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'interiorSeniority',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '手机',
    label: '手机',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 107,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '手机',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718174821051,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [{ pattern: '/^1[3456789]\\d{9}$/', message: '请输入正确的手机号码' }],
      span: 24,
    },
    readonly: false,
    prop: 'mobilePhone',
    width: 150,
    __vModel__: 'mobilePhone',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'mobilePhone',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    jnpfKey: 'datePicker',
    format: 'yyyy-MM-dd',
    fullName: '培训时间',
    label: '培训时间',
    sortable: false,
    align: 'left',
    __config__: {
      endRelationField: '',
      defaultValue: null,
      dragDisabled: false,
      className: [],
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718174866432,
      tagIcon: 'icon-ym icon-ym-generator-date',
      startRelationField: '',
      defaultCurrent: false,
      tag: 'JnpfDatePicker',
      formId: 109,
      visibility: ['pc', 'app'],
      jnpfKey: 'datePicker',
      noShow: false,
      endTimeTarget: 1,
      tipLabel: '',
      startTimeType: 1,
      endTimeRule: false,
      label: '培训时间',
      trigger: 'change',
      startTimeRule: false,
      startTimeValue: null,
      endTimeValue: null,
      endTimeType: 1,
      layout: 'colFormItem',
      startTimeTarget: 1,
      regList: [],
      span: 24,
    },
    prop: 'trainingTimeText', // 使用格式化后的时间字段
    width: null,
    __vModel__: 'trainingTime',
    fixed: 'none',
    style: { width: '100%' },
    startTime: null,
    disabled: false,
    id: 'trainingTime',
    placeholder: '请选择',
    endTime: null,
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    jnpfKey: 'datePicker',
    format: 'yyyy-MM-dd',
    fullName: '生日',
    label: '生日',
    sortable: false,
    align: 'left',
    __config__: {
      endRelationField: '',
      defaultValue: null,
      dragDisabled: false,
      className: [],
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718174898317,
      tagIcon: 'icon-ym icon-ym-generator-date',
      startRelationField: '',
      defaultCurrent: false,
      tag: 'JnpfDatePicker',
      formId: 110,
      visibility: ['pc', 'app'],
      jnpfKey: 'datePicker',
      noShow: false,
      endTimeTarget: 1,
      tipLabel: '',
      startTimeType: 1,
      endTimeRule: false,
      label: '生日',
      trigger: 'change',
      startTimeRule: false,
      startTimeValue: null,
      endTimeValue: null,
      endTimeType: 1,
      layout: 'colFormItem',
      startTimeTarget: 1,
      regList: [],
      span: 24,
    },
    prop: 'birthdayText', // 使用格式化后的时间字段
    width: null,
    __vModel__: 'birthday',
    fixed: 'none',
    style: { width: '100%' },
    startTime: null,
    disabled: false,
    id: 'birthday',
    placeholder: '请选择',
    endTime: null,
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    filterable: false,
    clearable: true,
    jnpfKey: 'depSelect',
    ableIds: [],
    multiple: false,
    fullName: '班组',
    label: '班组',
    sortable: false,
    align: 'left',
    __config__: {
      formId: 113,
      visibility: ['pc', 'app'],
      jnpfKey: 'depSelect',
      defaultValue: null,
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '班组',
      trigger: 'change',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718175017089,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-tree-department1',
      defaultCurrent: false,
      tag: 'JnpfDepSelect',
      regList: [],
      span: 24,
    },
    prop: 'team',
    width: null,
    __vModel__: 'team',
    fixed: 'none',
    style: { width: '100%' },
    selectType: 'all',
    disabled: false,
    id: 'team',
    placeholder: '请选择',
    on: { change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}' },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '年龄',
    label: '年龄',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 114,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '年龄',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718175069428,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'age',
    width: null,
    __vModel__: 'age',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'age',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    filterable: false,
    clearable: true,
    jnpfKey: 'select',
    multiple: false,
    fullName: '民族',
    label: '民族',
    sortable: false,
    align: 'left',
    props: { label: 'fullName', value: 'id' },
    __config__: {
      formId: 116,
      visibility: ['pc', 'app'],
      jnpfKey: 'select',
      defaultValue: '54bad287aa084f81b40c6bc35c7f2545',
      noShow: false,
      dataType: 'dictionary',
      dictionaryType: 'b6cd65a763fa45eb9fe98e5057693e40',
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '民族',
      trigger: 'change',
      propsUrl: '',
      templateJson: [],
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718175109736,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-select',
      propsName: '',
      tag: 'JnpfSelect',
      regList: [],
      span: 24,
    },
    prop: 'nationText', // 使用映射后的字段
    width: null,
    options: [
      { enCode: '达斡尔族', children: null, hasChildren: false, fullName: '达斡尔族', id: '01ba59c5d9a44d71ab0284cb48525813', parentId: '0' },
      { enCode: '锡伯族', children: null, hasChildren: false, fullName: '锡伯族', id: '059275f8dc94475d82efd6780d2f67b6', parentId: '0' },
      { enCode: '俄罗斯族', children: null, hasChildren: false, fullName: '俄罗斯族', id: '07f10a73a43942969e7b75a6d14c4e17', parentId: '0' },
      { enCode: '景颇族', children: null, hasChildren: false, fullName: '景颇族', id: '0bb8dce3cd934b5da4c31be379fdb962', parentId: '0' },
      { enCode: '京族', children: null, hasChildren: false, fullName: '京族', id: '12f83c695e104710933f9d713ac68b32', parentId: '0' },
      { enCode: '普米族', children: null, hasChildren: false, fullName: '普米族', id: '1b6da0db9c8f4054ac5225d32ac2607a', parentId: '0' },
      { enCode: '撒拉族', children: null, hasChildren: false, fullName: '撒拉族', id: '1db9a7c7c88a4dc7a5f61ae3aed723ae', parentId: '0' },
      { enCode: '裕固族', children: null, hasChildren: false, fullName: '裕固族', id: '25b952c507b148f98d50762a2e90419a', parentId: '0' },
      { enCode: '毛南族', children: null, hasChildren: false, fullName: '毛南族', id: '287ac847ef2a4f4b9198300477e8c4d9', parentId: '0' },
      { enCode: '鄂温克族', children: null, hasChildren: false, fullName: '鄂温克族', id: '2aa6c64c8875401fa2ab862e3ac6f0e2', parentId: '0' },
      { enCode: '怒族', children: null, hasChildren: false, fullName: '怒族', id: '33cf9894fe3f415086796946acde9a21', parentId: '0' },
      { enCode: '水族', children: null, hasChildren: false, fullName: '水族', id: '3b6c150a092e44bda357cfb1a0d51461', parentId: '0' },
      { enCode: '门巴族', children: null, hasChildren: false, fullName: '门巴族', id: '43347e3055f2498d83e7201be5a243f8', parentId: '0' },
      { enCode: '高山族', children: null, hasChildren: false, fullName: '高山族', id: '49d97d47a68e4787b8e46ae41fc2e9fa', parentId: '0' },
      { enCode: '仫佬族', children: null, hasChildren: false, fullName: '仫佬族', id: '5b6759fbcd9247f4b00a1cafb8e39c1b', parentId: '0' },
      { enCode: '基诺族', children: null, hasChildren: false, fullName: '基诺族', id: '696babc2ceaf4f5bae3705e2b7e6dee9', parentId: '0' },
      { enCode: '保安族', children: null, hasChildren: false, fullName: '保安族', id: '7584cef8439241a6a06121f655225715', parentId: '0' },
      { enCode: '塔塔尔族', children: null, hasChildren: false, fullName: '塔塔尔族', id: '7eee315f771a4282838e168603315eb1', parentId: '0' },
      { enCode: '柯尔克孜族', children: null, hasChildren: false, fullName: '柯尔克孜族', id: '803a14a1ba33425da9d3141ffee7f203', parentId: '0' },
      { enCode: '独龙族', children: null, hasChildren: false, fullName: '独龙族', id: '8a9f47d9f51e454fb36fce55fb7a63a6', parentId: '0' },
      { enCode: '羌族', children: null, hasChildren: false, fullName: '羌族', id: '907cbdba92344967a148a374528dee75', parentId: '0' },
      { enCode: '佤族', children: null, hasChildren: false, fullName: '佤族', id: '908f94c264a64c31a1fecccb811179c6', parentId: '0' },
      { enCode: '土族', children: null, hasChildren: false, fullName: '土族', id: '9828f30dd8b5488a968ac048836dcb77', parentId: '0' },
      { enCode: '阿昌族', children: null, hasChildren: false, fullName: '阿昌族', id: 'a13874e5d3924b7e8270f1487cd04fe1', parentId: '0' },
      { enCode: '乌孜别克族', children: null, hasChildren: false, fullName: '乌孜别克族', id: 'b0f9f27b34d54ccea3bb5c7e4b5e5f34', parentId: '0' },
      { enCode: '德昂族', children: null, hasChildren: false, fullName: '德昂族', id: 'b16664b364414793b310b4cc56dacd5a', parentId: '0' },
      { enCode: '珞巴族', children: null, hasChildren: false, fullName: '珞巴族', id: 'c7199f50afaa46069be39644a7b602c1', parentId: '0' },
      { enCode: '布朗族', children: null, hasChildren: false, fullName: '布朗族', id: 'd68614ec2be944b0b48be4c5edb9a83f', parentId: '0' },
      { enCode: '纳西族', children: null, hasChildren: false, fullName: '纳西族', id: 'e669a5d1545e4c39805e22da6df6dfd0', parentId: '0' },
      { enCode: '赫哲族', children: null, hasChildren: false, fullName: '赫哲族', id: 'f5ebba6dbf47453687b6a10694694aaf', parentId: '0' },
      { enCode: '鄂伦春族', children: null, hasChildren: false, fullName: '鄂伦春族', id: 'f8be14c58efe48d5adc66cbea176bfcd', parentId: '0' },
      { enCode: '塔吉克族', children: null, hasChildren: false, fullName: '塔吉克族', id: 'fb096124a2564dcc84eafc92b771e667', parentId: '0' },
      { enCode: '壮族', children: null, hasChildren: false, fullName: '壮族', id: '113779bb677d4021b54b10eb8c29145a', parentId: '0' },
      { enCode: '黎族', children: null, hasChildren: false, fullName: '黎族', id: '134d1bdae57e4d6c898e0d2ee72f2164', parentId: '0' },
      { enCode: '东乡族', children: null, hasChildren: false, fullName: '东乡族', id: '1c8bc07c65214f7ca842d8504cea4067', parentId: '0' },
      { enCode: '哈萨克族', children: null, hasChildren: false, fullName: '哈萨克族', id: '1d53970d6da94c6ca648311399122a02', parentId: '0' },
      { enCode: '朝鲜族', children: null, hasChildren: false, fullName: '朝鲜族', id: '20720625593343e39676654e73a6a39a', parentId: '0' },
      { enCode: '哈尼族', children: null, hasChildren: false, fullName: '哈尼族', id: '2835dcf61cf04a0e97bc4b117b08e3cb', parentId: '0' },
      { enCode: '侗族', children: null, hasChildren: false, fullName: '侗族', id: '3676319f3e0b444e9f754a1691de696c', parentId: '0' },
      { enCode: '瑶族', children: null, hasChildren: false, fullName: '瑶族', id: '37bf7db5a5c94dc6a3485ccccb1c6447', parentId: '0' },
      { enCode: '彝族', children: null, hasChildren: false, fullName: '彝族', id: '40472c04faa9498bba7b4e11851cf6df', parentId: '0' },
      { enCode: '汉族', children: null, hasChildren: false, fullName: '汉族', id: '54bad287aa084f81b40c6bc35c7f2545', parentId: '0' },
      { enCode: '回族', children: null, hasChildren: false, fullName: '回族', id: '5509d55ae8ee43bc8c25ad93eef90d98', parentId: '0' },
      { enCode: '苗族', children: null, hasChildren: false, fullName: '苗族', id: '588b2b6c0fa349a79992d011c7c47a62', parentId: '0' },
      { enCode: '满族', children: null, hasChildren: false, fullName: '满族', id: '61fe8b6156a743d685d0509226e7a70b', parentId: '0' },
      { enCode: '拉祜族', children: null, hasChildren: false, fullName: '拉祜族', id: '65704bad82ac4984bb93968333393590', parentId: '0' },
      { enCode: '维吾尔族', children: null, hasChildren: false, fullName: '维吾尔族', id: '7501989f1ba94623b44159c9d630858a', parentId: '0' },
      { enCode: '藏族', children: null, hasChildren: false, fullName: '藏族', id: '828eaef66cb14adda9c5c1997dd7bbe2', parentId: '0' },
      { enCode: '仡佬族', children: null, hasChildren: false, fullName: '仡佬族', id: '84992f0eab4742ea81a541e2e8789cab', parentId: '0' },
      { enCode: '白族', children: null, hasChildren: false, fullName: '白族', id: '93667ec1069c492082fb60dde3ce8d1c', parentId: '0' },
      { enCode: '布依族', children: null, hasChildren: false, fullName: '布依族', id: '998ce700fa2449b9a34ff07097e0798b', parentId: '0' },
      { enCode: '畲族', children: null, hasChildren: false, fullName: '畲族', id: '9d9c76c1bbb44fea9402e1c90801191f', parentId: '0' },
      { enCode: '土家族', children: null, hasChildren: false, fullName: '土家族', id: 'ad4039ce0d4e4017ae7c917c53ffbcc8', parentId: '0' },
      { enCode: '蒙古族', children: null, hasChildren: false, fullName: '蒙古族', id: 'b12b07c35a1f4591b64015c012aa0129', parentId: '0' },
      { enCode: '傈僳族', children: null, hasChildren: false, fullName: '傈僳族', id: 'bcf56b39e6bd4e31ae1621f839fa8b6c', parentId: '0' },
      { enCode: '傣族', children: null, hasChildren: false, fullName: '傣族', id: 'dd961d9d30a5492682a462bd282e8158', parentId: '0' },
    ],
    __vModel__: 'nation',
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'nation',
    placeholder: '请选择',
    on: { change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}' },
  },
  {
    filterable: false,
    clearable: true,
    jnpfKey: 'select',
    multiple: false,
    fullName: '学历',
    label: '学历',
    sortable: false,
    align: 'left',
    props: { label: 'fullName', value: 'id' },
    __config__: {
      formId: 117,
      visibility: ['pc', 'app'],
      jnpfKey: 'select',
      defaultValue: '',
      noShow: false,
      dataType: 'dictionary',
      dictionaryType: '6a6d6fb541b742fbae7e8888528baa16',
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '学历',
      trigger: 'change',
      propsUrl: '',
      templateJson: [],
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718175177068,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-select',
      propsName: '',
      tag: 'JnpfSelect',
      regList: [],
      span: 24,
    },
    prop: 'educationText', // 使用映射后的字段
    width: null,
    options: [
      { enCode: '7', children: null, hasChildren: false, fullName: '研究生', id: '1128f7a50cc64c6db6eed4696fdbee80', parentId: '0' },
      { enCode: '1', children: null, hasChildren: false, fullName: '小学', id: '3547c4e289194e92854fec4cd84836b2', parentId: '0' },
      { enCode: '3', children: null, hasChildren: false, fullName: '高中', id: '6410a712cd144e2c8ce5e0f153d88c0d', parentId: '0' },
      { enCode: '2', children: null, hasChildren: false, fullName: '初中', id: '8eea678c942145f99d7878ace0d8657e', parentId: '0' },
      { enCode: '5', children: null, hasChildren: false, fullName: '高职', id: '9b78f178da8448b298bc534e229386b5', parentId: '0' },
      { enCode: '8', children: null, hasChildren: false, fullName: '博士及以上', id: 'a38b8d5c43294b7e97c04d06a7e904ff', parentId: '0' },
      { enCode: '6', children: null, hasChildren: false, fullName: '本科', id: 'b620b397f3ec44f083b0df9fbc2f3ba7', parentId: '0' },
      { enCode: '4', children: null, hasChildren: false, fullName: '中专', id: 'df721d25cb9b4502afb4fe9796f71ac7', parentId: '0' },
    ],
    __vModel__: 'education',
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'education',
    placeholder: '请选择',
    on: { change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}' },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '籍贯',
    label: '籍贯',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 118,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '籍贯',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718175245274,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'nativePlace',
    width: null,
    __vModel__: 'nativePlace',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'nativePlace',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '人员类别',
    label: '人员类别',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 119,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '人员类别',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718175388275,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'categoryPersonnel',
    width: null,
    __vModel__: 'categoryPersonnel',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'categoryPersonnel',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    jnpfKey: 'datePicker',
    format: 'yyyy-MM-dd',
    fullName: '进入项目时间',
    label: '进入项目时间',
    sortable: false,
    align: 'left',
    __config__: {
      endRelationField: '',
      defaultValue: null,
      dragDisabled: false,
      className: [],
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718175408421,
      tagIcon: 'icon-ym icon-ym-generator-date',
      startRelationField: '',
      defaultCurrent: false,
      tag: 'JnpfDatePicker',
      formId: 120,
      visibility: ['pc', 'app'],
      jnpfKey: 'datePicker',
      noShow: false,
      endTimeTarget: 1,
      tipLabel: '',
      startTimeType: 1,
      endTimeRule: false,
      label: '进入项目时间',
      trigger: 'change',
      startTimeRule: false,
      startTimeValue: null,
      endTimeValue: null,
      endTimeType: 1,
      layout: 'colFormItem',
      startTimeTarget: 1,
      regList: [],
      span: 24,
    },
    prop: 'goProjectTimeText', // 使用格式化后的时间字段
    width: 110,
    __vModel__: 'goProjectTime',
    fixed: 'none',
    style: { width: '100%' },
    startTime: null,
    disabled: false,
    id: 'goProjectTime',
    placeholder: '请选择',
    endTime: null,
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '血型',
    label: '血型',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 125,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '血型',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718175623719,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'bloodType',
    width: null,
    __vModel__: 'bloodType',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'bloodType',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '用工形式',
    label: '用工形式',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 122,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '用工形式',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718175564418,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'formEmployment',
    width: null,
    __vModel__: 'formEmployment',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'formEmployment',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '户口性质',
    label: '户口性质',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 123,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '户口性质',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: *************,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'natureAccount',
    width: null,
    __vModel__: 'natureAccount',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'natureAccount',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '岗位层级',
    label: '岗位层级',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '岗位层级',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'positionLevel',
    width: null,
    __vModel__: 'positionLevel',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'positionLevel',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '岗位序列',
    label: '岗位序列',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '岗位序列',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'positionSequence',
    width: null,
    __vModel__: 'positionSequence',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'positionSequence',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '项目',
    label: '项目',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '项目',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'project',
    width: null,
    __vModel__: 'project',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'project',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '政治面貌',
    label: '政治面貌',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '政治面貌',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'politicalOutlook',
    width: null,
    __vModel__: 'politicalOutlook',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'politicalOutlook',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '毕业学校',
    label: '毕业学校',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '毕业学校',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'graduationSchool',
    width: null,
    __vModel__: 'graduationSchool',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'graduationSchool',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '所学专业',
    label: '所学专业',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '所学专业',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'specialty',
    width: null,
    __vModel__: 'specialty',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'specialty',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    jnpfKey: 'datePicker',
    format: 'yyyy-MM-dd',
    fullName: '毕业时间',
    label: '毕业时间',
    sortable: false,
    align: 'left',
    __config__: {
      endRelationField: '',
      defaultValue: null,
      dragDisabled: false,
      className: [],
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718176135799,
      tagIcon: 'icon-ym icon-ym-generator-date',
      startRelationField: '',
      defaultCurrent: false,
      tag: 'JnpfDatePicker',
      formId: 127,
      visibility: ['pc', 'app'],
      jnpfKey: 'datePicker',
      noShow: false,
      endTimeTarget: 1,
      tipLabel: '',
      startTimeType: 1,
      endTimeRule: false,
      label: '毕业时间',
      trigger: 'change',
      startTimeRule: false,
      startTimeValue: null,
      endTimeValue: null,
      endTimeType: 1,
      layout: 'colFormItem',
      startTimeTarget: 1,
      regList: [],
      span: 24,
    },
    prop: 'graduationTimeText', // 使用格式化后的时间字段
    width: null,
    __vModel__: 'graduationTime',
    fixed: 'none',
    style: { width: '100%' },
    startTime: null,
    disabled: false,
    id: 'graduationTime',
    placeholder: '请选择',
    endTime: null,
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    jnpfKey: 'datePicker',
    format: 'yyyy-MM-dd',
    fullName: '参加工作时间',
    label: '参加工作时间',
    sortable: false,
    align: 'left',
    __config__: {
      endRelationField: '',
      defaultValue: null,
      dragDisabled: false,
      className: [],
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718176135799,
      tagIcon: 'icon-ym icon-ym-generator-date',
      startRelationField: '',
      defaultCurrent: false,
      tag: 'JnpfDatePicker',
      formId: 127,
      visibility: ['pc', 'app'],
      jnpfKey: 'datePicker',
      noShow: false,
      endTimeTarget: 1,
      tipLabel: '',
      startTimeType: 1,
      endTimeRule: false,
      label: '参加工作时间',
      trigger: 'change',
      startTimeRule: false,
      startTimeValue: null,
      endTimeValue: null,
      endTimeType: 1,
      layout: 'colFormItem',
      startTimeTarget: 1,
      regList: [],
      span: 24,
    },
    prop: 'joinWorkTimeText', // 使用格式化后的时间字段
    width: 110,
    __vModel__: 'joinWorkTime',
    fixed: 'none',
    style: { width: '100%' },
    startTime: null,
    disabled: false,
    id: 'joinWorkTime',
    placeholder: '请选择',
    endTime: null,
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    jnpfKey: 'datePicker',
    format: 'yyyy-MM-dd',
    fullName: '进入五公司时间',
    label: '进入五公司时间',
    sortable: false,
    align: 'left',
    __config__: {
      endRelationField: '',
      defaultValue: null,
      dragDisabled: false,
      className: [],
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718176135799,
      tagIcon: 'icon-ym icon-ym-generator-date',
      startRelationField: '',
      defaultCurrent: false,
      tag: 'JnpfDatePicker',
      formId: 127,
      visibility: ['pc', 'app'],
      jnpfKey: 'datePicker',
      noShow: false,
      endTimeTarget: 1,
      tipLabel: '',
      startTimeType: 1,
      endTimeRule: false,
      label: '进入五公司时间',
      trigger: 'change',
      startTimeRule: false,
      startTimeValue: null,
      endTimeValue: null,
      endTimeType: 1,
      layout: 'colFormItem',
      startTimeTarget: 1,
      regList: [],
      span: 24,
    },
    prop: 'joinFiveFirmTimeText', // 使用格式化后的时间字段
    width: 120,
    __vModel__: 'joinFiveFirmTime',
    fixed: 'none',
    style: { width: '100%' },
    startTime: null,
    disabled: false,
    id: 'joinFiveFirmTime',
    placeholder: '请选择',
    endTime: null,
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '专业技术职称',
    label: '专业技术职称',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '专业技术职称',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'technicalTitle',
    width: 110,
    __vModel__: 'technicalTitle',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'technicalTitle',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '工人技能等级',
    label: '工人技能等级',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '工人技能等级',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'workerSkillLevel',
    width: 110,
    __vModel__: 'workerSkillLevel',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'workerSkillLevel',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '职业资格',
    label: '职业资格',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '职业资格',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'professionalQual',
    width: null,
    __vModel__: 'professionalQual',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'professionalQual',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '来源分类',
    label: '来源分类',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '来源分类',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'sourceClassify',
    width: null,
    __vModel__: 'sourceClassify',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'sourceClassify',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '核电建设经历',
    label: '核电建设经历',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '核电建设经历',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'constructionExperience',
    width: 110,
    __vModel__: 'constructionExperience',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'constructionExperience',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '办公地点',
    label: '办公地点',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '办公地点',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'officeLocation',
    width: null,
    __vModel__: 'officeLocation',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'officeLocation',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '办公电话',
    label: '办公电话',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '办公电话',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'officePhone',
    width: null,
    __vModel__: 'officePhone',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'officePhone',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '邮箱',
    label: '邮箱',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '邮箱',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'email',
    width: 200,
    __vModel__: 'email',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'email',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '紧急联系人',
    label: '紧急联系人',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '紧急联系人',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'emergencyContacts',
    width: null,
    __vModel__: 'emergencyContacts',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'emergencyContacts',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '紧急联系人电话',
    label: '紧急联系人电话',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '紧急联系人电话',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'emergencyContactsPhone',
    width: 150,
    __vModel__: 'emergencyContactsPhone',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'emergencyContactsPhone',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '婚姻状况',
    label: '婚姻状况',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '婚姻状况',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'maritalStatus',
    width: null,
    __vModel__: 'maritalStatus',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'maritalStatus',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '人数',
    label: '人数',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '人数',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'number',
    width: null,
    __vModel__: 'number',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'number',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  // {
  //   clearable: true,
  //   maxlength: null,
  //   jnpfKey: 'input',
  //   suffixIcon: '',
  //   fullName: '家庭住址',
  //   label: '家庭住址',
  //   sortable: false,
  //   align: 'left',
  //   addonAfter: '',
  //   __config__: {
  //     formId: 124,
  //     visibility: ['pc', 'app'],
  //     jnpfKey: 'input',
  //     noShow: false,
  //     tipLabel: '',
  //     dragDisabled: false,
  //     className: [],
  //     label: '家庭住址',
  //     trigger: 'blur',
  //     showLabel: true,
  //     required: false,
  //     tableName: 'base_user',
  //     renderKey: **********714,
  //     layout: 'colFormItem',
  //     tagIcon: 'icon-ym icon-ym-generator-input',
  //     tag: 'JnpfInput',
  //     regList: [],
  //     span: 24,
  //   },
  //   readonly: false,
  //   prop: 'homeAddress',
  //   width: null,
  //   __vModel__: 'homeAddress',
  //   showPassword: false,
  //   fixed: 'none',
  //   style: { width: '100%' },
  //   disabled: false,
  //   id: 'homeAddress',
  //   placeholder: '请输入',
  //   prefixIcon: '',
  //   addonBefore: '',
  //   on: {
  //     change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
  //     blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
  //   },
  // },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '一二期',
    label: '一二期',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '一二期',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'phase',
    width: null,
    __vModel__: 'phase',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'phase',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '培训状态',
    label: '培训状态',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 113,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '培训状态',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'study_admission_request',
      renderKey: 1718272400169,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'trainingStatus',
    width: null,
    __vModel__: 'trainingStatus',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'trainingStatus',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    jnpfKey: 'datePicker',
    format: 'yyyy-MM-dd',
    fullName: '撤场时间',
    label: '撤场时间',
    sortable: false,
    align: 'left',
    __config__: {
      endRelationField: '',
      defaultValue: null,
      dragDisabled: false,
      className: [],
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718176135799,
      tagIcon: 'icon-ym icon-ym-generator-date',
      startRelationField: '',
      defaultCurrent: false,
      tag: 'JnpfDatePicker',
      formId: 127,
      visibility: ['pc', 'app'],
      jnpfKey: 'datePicker',
      noShow: false,
      endTimeTarget: 1,
      tipLabel: '',
      startTimeType: 1,
      endTimeRule: false,
      label: '撤场时间',
      trigger: 'change',
      startTimeRule: false,
      startTimeValue: null,
      endTimeValue: null,
      endTimeType: 1,
      layout: 'colFormItem',
      startTimeTarget: 1,
      regList: [],
      span: 24,
    },
    prop: 'removeTimeText', // 使用格式化后的时间字段
    width: null,
    __vModel__: 'removeTime',
    fixed: 'none',
    style: { width: '100%' },
    startTime: null,
    disabled: false,
    id: 'removeTime',
    placeholder: '请选择',
    endTime: null,
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '撤场原因',
    label: '撤场原因',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 126,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '撤场原因',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: 1718176122359,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'removeRemark',
    width: null,
    __vModel__: 'removeRemark',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'removeRemark',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
  {
    clearable: true,
    maxlength: null,
    jnpfKey: 'input',
    suffixIcon: '',
    fullName: '备注',
    label: '备注',
    sortable: false,
    align: 'left',
    addonAfter: '',
    __config__: {
      formId: 124,
      visibility: ['pc', 'app'],
      jnpfKey: 'input',
      noShow: false,
      tipLabel: '',
      dragDisabled: false,
      className: [],
      label: '备注',
      trigger: 'blur',
      showLabel: true,
      required: false,
      tableName: 'base_user',
      renderKey: **********714,
      layout: 'colFormItem',
      tagIcon: 'icon-ym icon-ym-generator-input',
      tag: 'JnpfInput',
      regList: [],
      span: 24,
    },
    readonly: false,
    prop: 'remark',
    width: null,
    __vModel__: 'remark',
    showPassword: false,
    fixed: 'none',
    style: { width: '100%' },
    disabled: false,
    id: 'remark',
    placeholder: '请输入',
    prefixIcon: '',
    addonBefore: '',
    on: {
      change: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
      blur: '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}',
    },
  },
];
export default columnList;
