
<template>
    <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="600px"
                :minHeight="100" :showOkBtn="false">
        <template #insertFooter>
        </template>
        <!-- 表单 -->
        <a-row class="dynamic-form ">
            <a-form :colon="false" size="default" layout= "horizontal" 
            labelAlign= "right" 
 :labelCol="{ style: { width: '100px' } }"             :model="dataForm"  ref="formRef">
            <a-row :gutter="15">
                <!-- 具体表单 -->
        <a-col :span="24" class="ant-col-item"  >
        <a-form-item    
  name="name" >
            <template #label>姓名</template>
        <p>{{ dataForm.name }} </p>
        </a-form-item>
    </a-col>
        <a-col :span="24" class="ant-col-item"  >
        <a-form-item    
  name="dept" >
            <template #label>部门</template>
        <p>{{ dataForm.dept }} </p>
        </a-form-item>
    </a-col>
        <a-col :span="24" class="ant-col-item"  >
        <a-form-item    
  name="job" >
            <template #label>岗位</template>
        <p>{{ dataForm.job }} </p>
        </a-form-item>
    </a-col>
        <a-col :span="24" class="ant-col-item"  >
        <a-form-item    
  name="phone" >
            <template #label>联系方式</template>
        <p>{{ dataForm.phone }} </p>
        </a-form-item>
    </a-col>
        <a-col :span="24" class="ant-col-item"  >
        <a-form-item    
  name="idCard" >
            <template #label>身份证</template>
        <p>{{ dataForm.idCard }} </p>
        </a-form-item>
    </a-col>
        <a-col :span="24" class="ant-col-item"  >
        <a-form-item    
  name="sourceUnit" >
            <template #label>来源公司</template>
        <p>{{ dataForm.sourceUnit }} </p>
        </a-form-item>
    </a-col>
        <a-col :span="24" class="ant-col-item"  >
        <a-form-item    
  name="status" >
            <template #label>办证状态</template>
        <p>{{ dataForm.status }} </p>
        </a-form-item>
    </a-col>
                <!-- 表单结束 -->
            </a-row>
            </a-form>
        </a-row>
    </BasicModal>
    <!-- 有关联表单详情：开始 -->
    <RelationDetail ref="relationDetailRef" />
    <!-- 有关联表单详情：结束 -->
</template>
<script lang="ts" setup>
    import { getDetailInfo } from './helper/api';
    import { getConfigData } from '/@/api/onlineDev/visualDev';
    import { reactive, toRefs, nextTick, ref, computed, unref ,toRaw} from 'vue';
    import { BasicModal, useModal } from '/@/components/Modal';
    // 有关联表单详情
    import RelationDetail from '/@/views/common/dynamicModel/list/detail/index.vue';
    // 表单权限
    import { usePermission } from '/@/hooks/web/usePermission';
    import { useMessage } from '/@/hooks/web/useMessage';

    interface State {
        dataForm: any;
        title: string;
    }

    defineOptions({ name: 'Detail' });
    const { createMessage, createConfirm } = useMessage();
    const [registerModal, { openModal, setModalProps, closeModal }] = useModal();
        
    const relationDetailRef = ref<any>(null);
    const state = reactive<State>({
        dataForm:{},
        title: '详情',
    });
    const { title, dataForm } = toRefs(state);
    // 表单权限
    const { hasFormP } = usePermission();


    defineExpose({ init });

    function init(data) {
        state.dataForm.id = data.id;
            openModal();
        nextTick(() => {
            setTimeout(initData, 0);
        });
    }
    function initData() {
        changeLoading(true);
        if (state.dataForm.id) {
            getData(state.dataForm.id);
        } else {
            closeModal();
        }
    }
    function getData(id) {
        getDetailInfo(id).then((res) => {
            state.dataForm = res.data || {};
            nextTick(() => {
                changeLoading(false);
            });
        });
    }

    function toDetail(modelId, id) {
        if (!id) return;
        getConfigData(modelId).then((res) => {
            if (!res.data || !res.data.formData) return;
            const formConf = JSON.parse(res.data.formData);
            formConf.popupType = 'general';
            const data = { id, formConf, modelId };
            relationDetailRef.value?.init(data);
        });
    }
    function setFormProps(data) {
        setModalProps(data);
    }
    function changeLoading(loading) {
        setFormProps({ loading });
    }
    
</script>
