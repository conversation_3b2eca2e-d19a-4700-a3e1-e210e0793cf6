// const columnList = [{"clearable":true,"maxlength":null,"jnpfKey":"input","suffixIcon":"","fullName":"姓名","label":"姓名","sortable":false,"align":"left","addonAfter":"","__config__":{"formId":101,"visibility":["pc","app"],"jnpfKey":"input","noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"姓名","trigger":"blur","showLabel":true,"required":false,"tableName":"document_ledger","renderKey":1717464711893,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-input","tag":"JnpfInput","regList":[],"span":24},"readonly":false,"prop":"name","width":null,"__vModel__":"name","showPassword":false,"fixed":"none","style":{"width":"100%"},"disabled":false,"id":"name","placeholder":"请输入","prefixIcon":"","addonBefore":"","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}","blur":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"filterable":false,"clearable":true,"jnpfKey":"depSelect","ableIds":[],"multiple":false,"fullName":"部门","label":"部门","sortable":false,"align":"left","__config__":{"formId":102,"visibility":["pc","app"],"jnpfKey":"depSelect","defaultValue":null,"noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"部门","trigger":"change","showLabel":true,"required":false,"tableName":"document_ledger","renderKey":1717464716542,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-tree-department1","defaultCurrent":false,"tag":"JnpfDepSelect","regList":[],"span":24},"prop":"dept","width":null,"__vModel__":"dept","fixed":"none","style":{"width":"100%"},"selectType":"all","disabled":false,"id":"dept","placeholder":"请选择","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"filterable":false,"clearable":true,"jnpfKey":"posSelect","ableIds":[],"multiple":false,"fullName":"岗位","label":"岗位","sortable":false,"align":"left","__config__":{"formId":103,"visibility":["pc","app"],"jnpfKey":"posSelect","defaultValue":null,"noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"岗位","trigger":"change","showLabel":true,"required":false,"tableName":"document_ledger","renderKey":1717464729521,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-jobs","defaultCurrent":false,"tag":"JnpfPosSelect","regList":[],"span":24},"prop":"job","width":null,"__vModel__":"job","fixed":"none","style":{"width":"100%"},"selectType":"all","disabled":false,"id":"job","placeholder":"请选择","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"clearable":true,"maxlength":null,"jnpfKey":"input","suffixIcon":"","fullName":"联系方式","label":"联系方式","sortable":false,"align":"left","addonAfter":"","__config__":{"formId":104,"visibility":["pc","app"],"jnpfKey":"input","noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"联系方式","trigger":"blur","showLabel":true,"required":false,"tableName":"document_ledger","renderKey":1717464733594,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-input","tag":"JnpfInput","regList":[],"span":24},"readonly":false,"prop":"phone","width":null,"__vModel__":"phone","showPassword":false,"fixed":"none","style":{"width":"100%"},"disabled":false,"id":"phone","placeholder":"请输入","prefixIcon":"","addonBefore":"","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}","blur":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"clearable":true,"maxlength":null,"jnpfKey":"input","suffixIcon":"","fullName":"身份证","label":"身份证","sortable":false,"align":"left","addonAfter":"","__config__":{"formId":105,"visibility":["pc","app"],"jnpfKey":"input","noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"身份证","trigger":"blur","showLabel":true,"required":false,"tableName":"document_ledger","renderKey":1717464738879,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-input","tag":"JnpfInput","regList":[],"span":24},"readonly":false,"prop":"idCard","width":null,"__vModel__":"idCard","showPassword":false,"fixed":"none","style":{"width":"100%"},"disabled":false,"id":"idCard","placeholder":"请输入","prefixIcon":"","addonBefore":"","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}","blur":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"clearable":true,"maxlength":null,"jnpfKey":"input","suffixIcon":"","fullName":"来源公司","label":"来源公司","sortable":false,"align":"left","addonAfter":"","__config__":{"formId":106,"visibility":["pc","app"],"jnpfKey":"input","noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"来源公司","trigger":"blur","showLabel":true,"required":false,"tableName":"document_ledger","renderKey":1717464745503,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-input","tag":"JnpfInput","regList":[],"span":24},"readonly":false,"prop":"sourceUnit","width":null,"__vModel__":"sourceUnit","showPassword":false,"fixed":"none","style":{"width":"100%"},"disabled":false,"id":"sourceUnit","placeholder":"请输入","prefixIcon":"","addonBefore":"","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}","blur":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"filterable":false,"clearable":true,"jnpfKey":"select","multiple":false,"fullName":"办证状态","label":"办证状态","sortable":false,"align":"left","props":{"label":"fullName","value":"id"},"__config__":{"formId":107,"visibility":["pc","app"],"jnpfKey":"select","defaultValue":"","noShow":false,"dataType":"static","dictionaryType":"","tipLabel":"","dragDisabled":false,"className":[],"label":"办证状态","trigger":"change","propsUrl":"","templateJson":[],"showLabel":true,"required":false,"tableName":"document_ledger","renderKey":1717464803334,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-select","propsName":"","tag":"JnpfSelect","regList":[],"span":24},"prop":"status","width":null,"options":[{"fullName":"未办证","id":"1"},{"fullName":"已办证","id":"2"}],"__vModel__":"status","fixed":"none","style":{"width":"100%"},"disabled":false,"id":"status","placeholder":"请选择","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}}]

const columnList = [
  {
    "clearable": true,
    "maxlength": null,
    "jnpfKey": "input",
    "suffixIcon": "",
    "fullName": "姓名",
    "label": "姓名",
    "sortable": false,
    "align": "left",
    "addonAfter": "",
    "__config__": {
      "formId": 101,
      "visibility": [
        "pc",
        "app"
      ],
      "jnpfKey": "input",
      "noShow": false,
      "tipLabel": "",
      "dragDisabled": false,
      "className": [

      ],
      "label": "姓名",
      "trigger": "blur",
      "showLabel": true,
      "required": false,
      "tableName": "document_ledger",
      "renderKey": 1717464711893,
      "layout": "colFormItem",
      "tagIcon": "icon-ym icon-ym-generator-input",
      "tag": "JnpfInput",
      "regList": [

      ],
      "span": 24
    },
    "readonly": false,
    "prop": "name",
    "width": null,
    "__vModel__": "name",
    "showPassword": false,
    "fixed": "none",
    "style": {
      "width": "100%"
    },
    "disabled": false,
    "id": "name",
    "placeholder": "请输入",
    "prefixIcon": "",
    "addonBefore": "",
    "on": {
      "change": "({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
      "blur": "({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
    }
  },
  {
    "filterable": false,
    "clearable": true,
    "jnpfKey": "depSelect",
    "ableIds": [

    ],
    "multiple": false,
    "fullName": "部门",
    "label": "部门",
    "sortable": false,
    "align": "left",
    "__config__": {
      "formId": 102,
      "visibility": [
        "pc",
        "app"
      ],
      "jnpfKey": "depSelect",
      "defaultValue": null,
      "noShow": false,
      "tipLabel": "",
      "dragDisabled": false,
      "className": [

      ],
      "label": "部门",
      "trigger": "change",
      "showLabel": true,
      "required": false,
      "tableName": "document_ledger",
      "renderKey": 1717464716542,
      "layout": "colFormItem",
      "tagIcon": "icon-ym icon-ym-tree-department1",
      "defaultCurrent": false,
      "tag": "JnpfDepSelect",
      "regList": [

      ],
      "span": 24
    },
    "prop": "dept",
    "width": null,
    "__vModel__": "dept",
    "fixed": "none",
    "style": {
      "width": "100%"
    },
    "selectType": "all",
    "disabled": false,
    "id": "dept",
    "placeholder": "请选择",
    "on": {
      "change": "({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
    }
  },
  {
    "filterable": false,
    "clearable": true,
    "jnpfKey": "posSelect",
    "ableIds": [

    ],
    "multiple": false,
    "fullName": "岗位",
    "label": "岗位",
    "sortable": false,
    "align": "left",
    "__config__": {
      "formId": 103,
      "visibility": [
        "pc",
        "app"
      ],
      "jnpfKey": "posSelect",
      "defaultValue": null,
      "noShow": false,
      "tipLabel": "",
      "dragDisabled": false,
      "className": [

      ],
      "label": "岗位",
      "trigger": "change",
      "showLabel": true,
      "required": false,
      "tableName": "document_ledger",
      "renderKey": 1717464729521,
      "layout": "colFormItem",
      "tagIcon": "icon-ym icon-ym-generator-jobs",
      "defaultCurrent": false,
      "tag": "JnpfPosSelect",
      "regList": [

      ],
      "span": 24
    },
    "prop": "job",
    "width": null,
    "__vModel__": "job",
    "fixed": "none",
    "style": {
      "width": "100%"
    },
    "selectType": "all",
    "disabled": false,
    "id": "job",
    "placeholder": "请选择",
    "on": {
      "change": "({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
    }
  },
  {
    "clearable": true,
    "maxlength": null,
    "jnpfKey": "input",
    "suffixIcon": "",
    "fullName": "联系方式",
    "label": "联系方式",
    "sortable": false,
    "align": "left",
    "addonAfter": "",
    "__config__": {
      "formId": 104,
      "visibility": [
        "pc",
        "app"
      ],
      "jnpfKey": "input",
      "noShow": false,
      "tipLabel": "",
      "dragDisabled": false,
      "className": [

      ],
      "label": "联系方式",
      "trigger": "blur",
      "showLabel": true,
      "required": false,
      "tableName": "document_ledger",
      "renderKey": 1717464733594,
      "layout": "colFormItem",
      "tagIcon": "icon-ym icon-ym-generator-input",
      "tag": "JnpfInput",
      "regList": [

      ],
      "span": 24
    },
    "readonly": false,
    "prop": "phone",
    "width": null,
    "__vModel__": "phone",
    "showPassword": false,
    "fixed": "none",
    "style": {
      "width": "100%"
    },
    "disabled": false,
    "id": "phone",
    "placeholder": "请输入",
    "prefixIcon": "",
    "addonBefore": "",
    "on": {
      "change": "({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
      "blur": "({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
    }
  },
  {
    "clearable": true,
    "maxlength": null,
    "jnpfKey": "input",
    "suffixIcon": "",
    "fullName": "身份证",
    "label": "身份证",
    "sortable": false,
    "align": "left",
    "addonAfter": "",
    "__config__": {
      "formId": 105,
      "visibility": [
        "pc",
        "app"
      ],
      "jnpfKey": "input",
      "noShow": false,
      "tipLabel": "",
      "dragDisabled": false,
      "className": [

      ],
      "label": "身份证",
      "trigger": "blur",
      "showLabel": true,
      "required": false,
      "tableName": "document_ledger",
      "renderKey": 1717464738879,
      "layout": "colFormItem",
      "tagIcon": "icon-ym icon-ym-generator-input",
      "tag": "JnpfInput",
      "regList": [

      ],
      "span": 24
    },
    "readonly": false,
    "prop": "idCard",
    "width": null,
    "__vModel__": "idCard",
    "showPassword": false,
    "fixed": "none",
    "style": {
      "width": "100%"
    },
    "disabled": false,
    "id": "idCard",
    "placeholder": "请输入",
    "prefixIcon": "",
    "addonBefore": "",
    "on": {
      "change": "({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
      "blur": "({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
    }
  },
  {
    "clearable": true,
    "maxlength": null,
    "jnpfKey": "input",
    "suffixIcon": "",
    "fullName": "来源公司",
    "label": "来源公司",
    "sortable": false,
    "align": "left",
    "addonAfter": "",
    "__config__": {
      "formId": 106,
      "visibility": [
        "pc",
        "app"
      ],
      "jnpfKey": "input",
      "noShow": false,
      "tipLabel": "",
      "dragDisabled": false,
      "className": [

      ],
      "label": "来源公司",
      "trigger": "blur",
      "showLabel": true,
      "required": false,
      "tableName": "document_ledger",
      "renderKey": 1717464745503,
      "layout": "colFormItem",
      "tagIcon": "icon-ym icon-ym-generator-input",
      "tag": "JnpfInput",
      "regList": [

      ],
      "span": 24
    },
    "readonly": false,
    "prop": "sourceUnit",
    "width": null,
    "__vModel__": "sourceUnit",
    "showPassword": false,
    "fixed": "none",
    "style": {
      "width": "100%"
    },
    "disabled": false,
    "id": "sourceUnit",
    "placeholder": "请输入",
    "prefixIcon": "",
    "addonBefore": "",
    "on": {
      "change": "({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}",
      "blur": "({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
    }
  },
  {
    "filterable": false,
    "clearable": true,
    "jnpfKey": "select",
    "multiple": false,
    "fullName": "办证状态",
    "label": "办证状态",
    "sortable": false,
    "align": "left",
    "props": {
      "label": "fullName",
      "value": "id"
    },
    "__config__": {
      "formId": 107,
      "visibility": [
        "pc",
        "app"
      ],
      "jnpfKey": "select",
      "defaultValue": "",
      "noShow": false,
      "dataType": "static",
      "dictionaryType": "",
      "tipLabel": "",
      "dragDisabled": false,
      "className": [

      ],
      "label": "办证状态",
      "trigger": "change",
      "propsUrl": "",
      "templateJson": [

      ],
      "showLabel": true,
      "required": false,
      "tableName": "document_ledger",
      "renderKey": 1717464803334,
      "layout": "colFormItem",
      "tagIcon": "icon-ym icon-ym-generator-select",
      "propsName": "",
      "tag": "JnpfSelect",
      "regList": [

      ],
      "span": 24
    },
    "prop": "status",
    "width": null,
    "options": [
      {
        "fullName": "未办证",
        "id": "1"
      },
      {
        "fullName": "已办证",
        "id": "2"
      }
    ],
    "__vModel__": "status",
    "fixed": "none",
    "style": {
      "width": "100%"
    },
    "disabled": false,
    "id": "status",
    "placeholder": "请选择",
    "on": {
      "change": "({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"
    }
  }
]
export default columnList
