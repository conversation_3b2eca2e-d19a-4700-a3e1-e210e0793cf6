<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="getTitle" showOkBtn @ok="handleSubmit">
    <BasicForm @register="registerForm" @field-value-change="handleFieldValueChange" class="!px-10px !mt-10px"> </BasicForm>
  </BasicPopup>
</template>
<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { BasicPopup, usePopupInner } from '/@/components/Popup';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import * as MachineLedgerApi from '/@/api/flowApi/machineLedger';
  import dayjs from 'dayjs';
  import { genDate, genInput, genSelect } from '/@/utils/formUtils';
  import { MACHINE_LEDGER_STATE } from '/@/enums/zzEnums';

  const id = ref('');

  const schemas: FormSchema[] = [
    genInput('器具名称', 'utensilName'),
    genInput('器具编号', 'utensilNo'),
    genInput('分级', 'classification', false),
    genInput('制作厂（商）', 'fabricationPlant', false),
    genInput('供应商', 'suppliers', false),
    genInput('精度', 'elaborate', false),
    genInput('规格型号', 'specificationsModels', false),
    genDate('检定日期', 'verificationDate', false),
    genInput('检定周期（月）', 'verificationCycle', false),
    genDate('有效期', 'effectiveDate', false),
    genInput('到期天数', 'expireDate', false),
    genInput('检定单位', 'verificationUnit', false),
    genSelect('状态', 'state', false, 'string', MACHINE_LEDGER_STATE),
    genInput('领用部门', 'acceptanceDept', false),
    genInput('领用人', 'acceptanceUser', false),
    genInput('领用人身份证号', 'acceptanceIdCard', false),
    genInput('检定员', 'inspector', false),
    genInput('证书编号', 'certificateNo', false),
    genInput('领料单小票号', 'pickingReceiptNumber', false),
    genDate('领用时间', 'acceptanceDate', false),
    genInput('遗失，报废单编号', 'lostScrapOrder', false),
    genDate('失效/撤场/封存日期', 'expirationDate', false),
    // genDate('撤场日期', 'withdrawalDate',false),
    genInput('需求计划号', 'demandPlanNumber', false),
    genInput('备注', 'remark', false),
  ];
  const getTitle = computed(() => (!unref(id) ? '新建计量器具台账' : '编辑计量器具台账'));
  const emit = defineEmits(['register', 'reload']);
  const { createMessage } = useMessage();
  const [registerForm, { setFieldsValue, validate, resetFields }] = useForm({ labelWidth: 120, schemas: schemas });
  const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);

  async function init(data: any) {
    resetFields();
    id.value = data.id;
    setFieldsValue({
      picyear: dayjs().year(),
    });
    if (id.value) {
      changeLoading(true);
      MachineLedgerApi.getById(id.value).then(res => {
        setFieldsValue(res.data);
        changeLoading(false);
      });
    }
  }

  // 检定日期
  const verificationDate = ref('');
  // 检定周期（月）
  const verificationCycle = ref('');

  function handleFieldValueChange(field: any, value: any) {
    // console.log('field', field, 'value', value);
    if (field === 'verificationDate') {
      // console.log('verificationDate:', value);
      verificationDate.value = value;
    }

    if (field === 'verificationCycle') {
      // console.log('verificationCycle:', value);
      verificationCycle.value = value;
    }

    if (verificationDate.value !== '' && verificationCycle.value !== '') {

      const effectiveInfoDate = dayjs(verificationDate.value).add(Number(verificationCycle.value), 'month').format('YYYY-MM-DD');

      const dateObj = new Date(effectiveInfoDate);
      const timestamp = dateObj.getTime();

      // 计算verificationDate.value到effectiveDate需要的天数
      const verificationDateObj = new Date(verificationDate.value);
      const verificationTimestamp = verificationDateObj.getTime();
      const daysDifference = Math.ceil((timestamp - verificationTimestamp) / (1000 * 60 * 60 * 24));

      setFieldsValue({
        effectiveDate: timestamp,
        expireDate: daysDifference - 1,
      });
    }
  }

  async function handleSubmit() {
    // console.log('id:',id)
    const values = await validate();
    // console.log('values:',values)
    if (!values) return;
    changeOkLoading(true);
    const data = {
      ...values,
      id: id.value,
    };
    // console.log('id:',id.value,)
    const formMethod = id.value ? MachineLedgerApi.update : MachineLedgerApi.save;
    formMethod(data)
      .then(res => {
        createMessage.success(res.msg);
        changeOkLoading(false);
        closePopup();
        emit('reload');
      })
      .catch(() => {
        changeOkLoading(false);
      });
  }
</script>
