<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'allotsState'">
              <LastTermState :state="record.allotsState" />
            </template>

            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '/@/components/Table';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { expireLedgerApi } from '/@/api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePopup } from '/@/components/Popup';
  import Form from './Form.vue';
  import { genQueryInput, genQueryKeyword } from '/@/utils/tableUtils';
  import LastTermState from '/@/views/extend/lastTerm/LastTermAllotsState.vue';
  // import { ref } from 'vue';

  defineOptions({ name: 'extend-zz-project-contract-sub' });

  // const selList = ref<any[]>([]); // 选中的

  const { t } = useI18n();
  const { createMessage } = useMessage();
  const columns: BasicColumn[] = [
    // { title: '序号', dataIndex: 'id', width: 80 },
    { title: '器具名称', dataIndex: 'utensilName' },
    { title: '器具编号', dataIndex: 'utensilNo' },
    { title: '分级', dataIndex: 'classification' },
    { title: '精度', dataIndex: 'elaborate' },
    { title: '规格型号', dataIndex: 'specificationsModels' },
    { title: '检定日期', dataIndex: 'verificationDate', width: 100, format: 'date|YYYY-MM-DD' },
    { title: '检定周期', dataIndex: 'verificationCycle' },
    { title: '有效期', dataIndex: 'effectiveDate', width: 100, format: 'date|YYYY-MM-DD' },
    { title: '状态', dataIndex: 'state' },
    { title: '领用部门', dataIndex: 'acceptanceDept' },
    { title: '领用人', dataIndex: 'acceptanceUser' },
    { title: '领用时间', dataIndex: 'acceptanceDate', width: 100, format: 'date|YYYY-MM-DD' },
    { title: '备注', dataIndex: 'remark' },
  ];
  const [registerForm, { openPopup: openFormPopup }] = usePopup();
  const [registerTable, { reload }] = useTable({
    api: expireLedgerApi.page,
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: [
        genQueryKeyword(),
        genQueryInput('器具编号', 'utensilNo'),
      ],
    },
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
    },
    // rowSelection: {
    //   // 选中行
    //   onChange: (selectedRows) => {
    //     selList.value = selectedRows;
    //     // console.log('value:', selList.value);
    //     // selectedRows.forEach(row => {
    //     //   console.log('dept:', row.responsibleDept);
    //     // });
    //   },
    // },
  });

  function getTableActions(record: any): ActionItem[] {
    return [
      {
        label: t('common.editText'),
        onClick: addOrUpdateHandle.bind(null, record.id),
      },
      {
        label: t('common.delText'),
        color: 'error',
        modelConfirm: {
          onOk: handleDelete.bind(null, record.id),
        },
      },
    ];
  }

  function handleDelete(id: any) {
    expireLedgerApi.remove(id).then(res => {
      createMessage.success(res.msg);
      reload();
    });
  }

  function addOrUpdateHandle(id = '') {
    openFormPopup(true, { id });
  }

</script>
