<template>
  <div class="flow-form">
    <a-form :colon="false" :labelCol="{ style: { width: '90px' } }" :model="dataForm" :rules="dataRule" ref="formRef" :disabled="config.disabled">
      <a-row>

        <a-col :span="24" v-if="judgeShow('compilationPer')">
          <a-form-item label="编制人" name="compilationPer">
            <a-input v-model:value="dataForm.compilationPer" placeholder="编制人" :disabled="judgeWrite('compilationPer')" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="judgeShow('compilationTime')">
          <a-form-item label="编制日期" name="compilationTime">
            <JnpfDatePicker v-model:value="dataForm.compilationTime" placeholder="编制日期" :disabled="judgeWrite('compilationTime')" />
          </a-form-item>
        </a-col>

      </a-row>
    </a-form>
    <a-tabs v-model:activeKey="activeKey" size="small" v-if="config.formOperates">
      <a-tab-pane key="1" tab="检定校验单">
        <!-- <a-space style="margin: 10px 0">
          <a-button type="primary" @click="handleOpenImport"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>导入领用流转单列表Excel</a-button>
        </a-space> -->
        <a-table :data-source="dataForm.verificationList" :columns="getGoodsColumns" size="small" :pagination="false" v-if="judgeShow('verificationList')">
          <template #headerCell="{ column }"><span class="required-sign" v-if="judgeRequired(`verificationList-${column.key}`)">*</span>{{ column.title }}</template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'utensilName'">
              <a-input v-model:value="record.utensilName" :disabled="judgeWrite('verificationList') || judgeWrite('verificationList-utensilName')" />
            </template>

            <template v-if="column.key === 'utensilNo'">
              <a-input v-model:value="record.utensilNo" :disabled="judgeWrite('verificationList') || judgeWrite('verificationList-utensilNo')" />
            </template>
            <template v-if="column.key === 'elaborate'">
              <a-input v-model:value="record.elaborate" :disabled="judgeWrite('verificationList') || judgeWrite('verificationList-elaborate')" />
            </template>
            <template v-if="column.key === 'specificationsModels'">
              <a-input v-model:value="record.specificationsModels" :disabled="judgeWrite('verificationList') || judgeWrite('verificationList-specificationsModels')" />
            </template>

            <template v-if="column.key === 'verificationDate'">
              <JnpfDatePicker
                v-model:value="record.verificationDate"
                :disabled="judgeWrite('verificationList') || judgeWrite('verificationList-verificationDate')" />
            </template>

            <template v-if="column.key === 'effectiveDate'">
              <JnpfDatePicker
                v-model:value="record.effectiveDate"
                :disabled="judgeWrite('verificationList') || judgeWrite('verificationList-effectiveDate')" />
            </template>

            <template v-if="column.key === 'verificationUnit'">
              <a-input v-model:value="record.verificationUnit" :disabled="judgeWrite('verificationList') || judgeWrite('verificationList-verificationUnit')" />
            </template>

            <template v-if="column.key === 'confirmResult'">
              <a-input v-model:value="record.confirmResult" :disabled="judgeWrite('verificationList') || judgeWrite('verificationList-confirmResult')" />
            </template>

            <template v-if="column.key === 'remark'">
              <a-input v-model:value="record.remark" :disabled="judgeWrite('verificationList') || judgeWrite('verificationList-remark')" />
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-button class="action-btn" type="link" color="error" @click="handleDel(index, record.id)" size="small">删除</a-button>
                <!-- <a-button v-if="record.displayStatus === 1" class="action-btn" type="link" color="blue"  @click="uploadFile(record)" size="small">查看资料</a-button> -->
              </a-space>
            </template>
          </template>
        </a-table>
        <div class="table-add-action" @click="addCounterfeit(true, {})" v-if="!(config.disabled || judgeWrite('verificationList'))">
          <a-button type="link" preIcon="icon-ym icon-ym-btn-add">新增器具</a-button>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- <ImportModal @register="registerImportModal" @import="handleImport" /> -->
    <LedgerModal @register="registerLedgerModal" @select="onLedgerSelect" />
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref, toRefs, watch } from 'vue';
  import { useFlowForm } from '/@/views/workFlow/workFlowForm/hooks/useFlowForm';
  import type { FormInstance } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  // import ImportModal from './ImportModal.vue';
  import * as MachineVerificationApi from '/@/api/flowApi/machineVerification';
  import { useMessage } from '/@/hooks/web/useMessage';
  import LedgerModal from './LedgerModal.vue';
  import { useUserStore } from '/@/store/modules/user';

  interface State {
    dataForm: any;
    dataRule: any;
    billEnCode: string;
    fileList: any[];
    activeKey: string;
  }

  defineOptions({ name: 'machineVerificationRequest' });

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;

  const { createMessage } = useMessage();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerLedgerModal, { openModal: addCounterfeit }] = useModal();

  const props = defineProps(['config']);
  const emit = defineEmits(['setPageLoad', 'eventReceiver']);
  const defaultGoodsColumns = [
    { title: '器具名称', dataIndex: 'utensilName', key: 'utensilName'},
    { title: '器具编号', dataIndex: 'utensilNo', key: 'utensilNo'},
    { title: '精度', dataIndex: 'elaborate', key: 'elaborate'},
    { title: '规格型号', dataIndex: 'specificationsModels', key: 'specificationsModels'},
    { title: '检定日期', dataIndex: 'verificationDate', key: 'verificationDate' },
    { title: '有效期', dataIndex: 'effectiveDate', key: 'effectiveDate' },
    { title: '检定单位', dataIndex: 'verificationUnit', key: 'verificationUnit' },
    { title: '确认结果', dataIndex: 'confirmResult', key: 'confirmResult' },
    { title: '备注', dataIndex: 'remark', key: 'remark' },
  ];
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      compilationPer: userInfo.userName,
      compilationTime: '',
      // admissionRequestName: '',
      // creatTime: undefined,
      status: '',
      flowId: '',
      verificationList: [],
      fileJson: '',
    },
    dataRule: {
      compilationPer: [{ required: true, message: '编制人不能为空', trigger: 'change' }],
      compilationTime: [{ required: true, message: '编制时间不能为空', trigger: 'change' }],
    },
    billEnCode: 'OrderNumber',
    fileList: [],
    activeKey: '1',
  });
  const { dataForm, dataRule, activeKey } = toRefs(state);
  const { init, judgeShow, judgeWrite, judgeRequired, dataFormSubmit } = useFlowForm({
    config: props.config,
    selfState: state,
    emit,
    formRef,
  });

  defineExpose({ dataFormSubmit });
  const noColumn = { width: 50, title: '序号', dataIndex: 'index', key: 'index', align: 'center', customRender: ({ index }) => index + 1 };

  const getGoodsColumns = computed(() => {
    const actionColumn = !props.config.disabled && !judgeWrite('verificationList') ? { title: '操作', dataIndex: 'action', key: 'action', width: 50 } : null;
    const column = defaultGoodsColumns.filter(o => judgeShow(`verificationList-${o.key}`));
    const list = [noColumn, ...column];
    if (actionColumn) list.push(actionColumn);
    return list;
  });

  watch(
    () => state.dataForm.verificationList,
    val => {
      let money = 0;
      for (let i = 0; i < val.length; i++) {
        const e = val[i];
        money += parseFloat(e.actualAmount);
      }
      state.dataForm.receivableMoney = money.toFixed(2);
    },
    { deep: true },
  );

  function handleDel(index, id) {
    if (id === undefined || id === null || id === '') {
      console.log('ID为空', id);
      state.dataForm.verificationList.splice(index, 1);
    } else {
      MachineVerificationApi.deleteById(id).then(res => {
        // 打印返回数据
        console.log('res', res);
      });
      console.log('ID不为空', id);
      state.dataForm.verificationList.splice(index, 1);
    }
  }

  function onLedgerSelect(list) {
    console.log('onLedgerList:', list);
    for (let i = 0; i < list.length; i++) {
      const e = list[i];
      let item = {
        utensilName: e.utensilName,
        utensilNo: e.utensilNo,
        elaborate: e.elaborate,
        specificationsModels: e.specificationsModels,
      };
      state.dataForm.verificationList.push(item);
    }
  }

  function handleOpenImport() {
    openImportModal(true, {});
  }

  /**
   * 预览确认后回调
   */
  function handleImport(list) {
    console.log('list', list);
    list.forEach(function (item) {
      console.log('item:', item);
      state.dataForm.verificationList.push(item);
    });
  }

  onMounted(() => {
    init();
  });
</script>
<style lang="less" scoped>
  .ant-tabs {
    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
    }
  }
</style>
