<template>
  <div class="flow-form">
    <a-form :colon="false" :labelCol="{ style: { width: '90px' } }" :model="dataForm" :rules="dataRule" ref="formRef" :disabled="config.disabled">
      <a-row>
        <a-col :span="24" v-if="judgeShow('chargePerson')">
          <a-form-item label="责任人" name="chargePerson">
            <a-input v-model:value="dataForm.chargePerson" placeholder="责任人" :disabled="judgeWrite('chargePerson')" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="judgeShow('amountRange')">
          <a-form-item label="金额范围" name="amountRange">
            <a-select v-model:value="dataForm.amountRange" :disabled="judgeWrite('amountRange')">
              <a-select-option value="5000以下">5000以下</a-select-option>
              <a-select-option value="5000以上">5000以上</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="judgeShow('scrapTime')">
          <a-form-item label="报废时间" name="scrapTime">
            <JnpfDatePicker v-model:value="dataForm.scrapTime" placeholder="报废时间" :disabled="judgeWrite('scrapTime')" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-tabs v-model:activeKey="activeKey" size="small" v-if="config.formOperates">
      <a-tab-pane key="1" tab="报废单">
        <!-- <a-space style="margin: 10px 0">
          <a-button type="primary" @click="handleOpenImport"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>导入领用流转单列表Excel</a-button>
        </a-space> -->
        <a-table :data-source="dataForm.scrapList" :columns="getGoodsColumns" size="small" :pagination="false" v-if="judgeShow('scrapList')">
          <template #headerCell="{ column }"><span class="required-sign" v-if="judgeRequired(`scrapList-${column.key}`)">*</span>{{ column.title }}</template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'utensilName'">
              <a-input v-model:value="record.utensilName" :disabled="judgeWrite('scrapList') || judgeWrite('scrapList-utensilName')" />
            </template>

            <template v-if="column.key === 'utensilNo'">
              <a-input v-model:value="record.utensilNo" :disabled="judgeWrite('scrapList') || judgeWrite('scrapList-utensilNo')" />
            </template>
            <template v-if="column.key === 'elaborate'">
              <a-input v-model:value="record.elaborate" :disabled="judgeWrite('scrapList') || judgeWrite('scrapList-elaborate')" />
            </template>
            <template v-if="column.key === 'specificationsModels'">
              <a-input v-model:value="record.specificationsModels" :disabled="judgeWrite('scrapList') || judgeWrite('scrapList-specificationsModels')" />
            </template>

            <template v-if="column.key === 'value'">
              <a-input v-model:value="record.value" :disabled="judgeWrite('scrapList') || judgeWrite('scrapList-value')" />
            </template>

            <template v-if="column.key === 'scrapImage'">
                <JnpfFaUploadFileQiniu 
                v-model:value="record.scrapImage" 
                prefix="test" :disabled="judgeWrite('scrapList') || judgeWrite('scrapList-scrapImage')"/>
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-button class="action-btn" type="link" color="error" @click="handleDel(index, record.id)" size="small">删除</a-button>
                <!-- <a-button v-if="record.displayStatus === 1" class="action-btn" type="link" color="blue"  @click="uploadFile(record)" size="small">查看资料</a-button> -->
              </a-space>
            </template>
          </template>
        </a-table>
        <div class="table-add-action" @click="addCounterfeit(true, {})" v-if="!(config.disabled || judgeWrite('scrapList'))">
          <a-button type="link" preIcon="icon-ym icon-ym-btn-add">新增器具</a-button>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- <ImportModal @register="registerImportModal" @import="handleImport" /> -->
    <LedgerModal @register="registerLedgerModal" @select="onLedgerSelect" />
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref, toRefs, watch } from 'vue';
  import { useFlowForm } from '/@/views/workFlow/workFlowForm/hooks/useFlowForm';
  import type { FormInstance } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  // import ImportModal from './ImportModal.vue';
  import * as MachineScrapfeitApi from '/@/api/flowApi/machineScrap';
  import { useMessage } from '/@/hooks/web/useMessage';
  import LedgerModal from './LedgerModal.vue';
  import JnpfFaUploadFileQiniu from "../counterfeit/FaUploadFileQiniu.vue"

  interface State {
    dataForm: any;
    dataRule: any;
    billEnCode: string;
    fileList: any[];
    activeKey: string;
  }

  defineOptions({ name: 'machineScrapRequest' });

  const { createMessage } = useMessage();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerLedgerModal, { openModal: addCounterfeit }] = useModal();

  const props = defineProps(['config']);
  const emit = defineEmits(['setPageLoad', 'eventReceiver']);
  const defaultGoodsColumns = [
    { title: '器具名称', dataIndex: 'utensilName', key: 'utensilName', width: 80 },
    { title: '器具编号', dataIndex: 'utensilNo', key: 'utensilNo', width: 80 },
    { title: '精度', dataIndex: 'elaborate', key: 'elaborate', width: 80 },
    { title: '规格型号', dataIndex: 'specificationsModels', key: 'specificationsModels', width: 80 },
    { title: '价值', dataIndex: 'value', key: 'value', width: 120 },
    { title: '报废图片', dataIndex: 'scrapImage', key: 'scrapImage', width: 120 },
  ];
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      chargePerson: '',
      amountRange: '',
      scrapTime: new Date(),
      // admissionRequestName: '',
      // creatTime: undefined,
      status: '',
      flowId: '',
      scrapList: [],
      fileJson: '',
    },
    dataRule: {
      chargePerson: [{ required: true, message: '责任人不能为空', trigger: 'change' }],
      amountRange: [{ required: true, message: '金额范围不能为空', trigger: 'change' }],
      scrapTime: [{ required: true, message: '报废时间不能为空', trigger: 'change' }],
    },
    billEnCode: 'OrderNumber',
    fileList: [],
    activeKey: '1',
  });
  const { dataForm, dataRule, activeKey } = toRefs(state);
  const { init, judgeShow, judgeWrite, judgeRequired, dataFormSubmit } = useFlowForm({
    config: props.config,
    selfState: state,
    emit,
    formRef,
  });

  defineExpose({ dataFormSubmit });
  const noColumn = { width: 50, title: '序号', dataIndex: 'index', key: 'index', align: 'center', customRender: ({ index }) => index + 1 };

  const getGoodsColumns = computed(() => {
    const actionColumn = !props.config.disabled && !judgeWrite('scrapList') ? { title: '操作', dataIndex: 'action', key: 'action', width: 50 } : null;
    const column = defaultGoodsColumns.filter(o => judgeShow(`scrapList-${o.key}`));
    const list = [noColumn, ...column];
    if (actionColumn) list.push(actionColumn);
    return list;
  });

  watch(
    () => state.dataForm.scrapList,
    val => {
      let money = 0;
      for (let i = 0; i < val.length; i++) {
        const e = val[i];
        money += parseFloat(e.actualAmount);
      }
      state.dataForm.receivableMoney = money.toFixed(2);
    },
    { deep: true },
  );

  function handleDel(index, id) {
    if (id === undefined || id === null || id === '') {
      console.log('ID为空', id);
      state.dataForm.scrapList.splice(index, 1);
    } else {
      MachineScrapfeitApi.deleteById(id).then(res => {
        // 打印返回数据
        console.log('res', res);
      });
      console.log('ID不为空', id);
      state.dataForm.scrapList.splice(index, 1);
    }
  }

  function onLedgerSelect(list) {
    console.log('onLedgerList:', list);
    for (let i = 0; i < list.length; i++) {
      const e = list[i];
      let item = {
        utensilName: e.utensilName,
        utensilNo: e.utensilNo,
        elaborate: e.elaborate,
        specificationsModels: e.specificationsModels,
      };
      state.dataForm.scrapList.push(item);
    }
  }

  function handleOpenImport() {
    openImportModal(true, {});
  }

  /**
   * 预览确认后回调
   */
  function handleImport(list) {
    console.log('list', list);
    list.forEach(function (item) {
      console.log('item:', item);
      state.dataForm.scrapList.push(item);
    });
  }

  onMounted(() => {
    init();
  });
</script>
<style lang="less" scoped>
  .ant-tabs {
    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
    }
  }
</style>
