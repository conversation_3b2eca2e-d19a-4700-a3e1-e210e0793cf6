<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">新建</a-button>
            <a-button type="link" @click="handleExport"><i class="icon-ym icon-ym-btn-download button-preIcon"></i>{{ t('common.exportText') }}</a-button>
            <a-button type="link" @click="handleImport"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>{{ t('common.importText') }}</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handleBatchDel()" :disabled="selList.length === 0">批量删除</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
    <ExportModal @register="registerExportModal" />
    <ImportModal @register="registerImportModal" @reload="reload" />
    <verificationFile @register="registerVerificationFile" />
  </div>
</template>
<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '/@/components/Table';
  import { useI18n } from '/@/hooks/web/useI18n';
  import * as MachineLedgerApi from '/@/api/flowApi/machineLedger';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePopup } from '/@/components/Popup';
  import Form from './Form.vue';
  import { genQueryInput, genQueryKeyword, genQueryTimeInput } from '/@/utils/tableUtils';
  import ExportModal from './ExportModal.vue';
  import ImportModal from './ImportModal.vue';
  import { useModal } from '/@/components/Modal';
  import verificationFile from './VerificationFile.vue';
  import { ref } from 'vue';

  defineOptions({ name: 'extend-zz-project-contract-sub' });

  const selList = ref<any[]>([]); // 选中的

  const { t } = useI18n();
  const { createMessage } = useMessage();
  const [registerExportModal, { openModal: openExportModal }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerVerificationFile, { openModal: openVerificationFile }] = useModal();
  const columns: BasicColumn[] = [
    // { title: '序号', dataIndex: 'id', width: 80 },
    { title: '器具名称', dataIndex: 'utensilName' },
    { title: '器具编号', dataIndex: 'utensilNo' },
    { title: '分级', dataIndex: 'classification' },
    { title: '制作厂（商）', dataIndex: 'fabricationPlant' },
    { title: '供应商', dataIndex: 'suppliers' },
    { title: '精度', dataIndex: 'elaborate' },
    { title: '规格型号', dataIndex: 'specificationsModels' },
    { title: '检定日期', dataIndex: 'verificationDate', width: 100, format: 'date|YYYY-MM-DD' },
    { title: '检定周期（月）', dataIndex: 'verificationCycle' },
    { title: '有效期', dataIndex: 'effectiveDate', width: 100, format: 'date|YYYY-MM-DD' },
    { title: '到期天数', dataIndex: 'expireDate' },
    { title: '检定单位', dataIndex: 'verificationUnit' },
    { title: '状态', dataIndex: 'state' },
    { title: '领用部门', dataIndex: 'acceptanceDept' },
    { title: '领用人', dataIndex: 'acceptanceUser' },
    { title: '领用人身份证号', dataIndex: 'acceptanceIdCard' },
    { title: '检定员', dataIndex: 'inspector' },
    { title: '证书编号', dataIndex: 'certificateNo' },
    { title: '领料单小票号', dataIndex: 'pickingReceiptNumber' },
    { title: '领用时间', dataIndex: 'acceptanceDate', width: 100, format: 'date|YYYY-MM-DD' },
    { title: '遗失，报废单编号', dataIndex: 'lostScrapOrder', width: 250 },
    { title: '失效/撤场/封存日期', dataIndex: 'expirationDate', width: 100, format: 'date|YYYY-MM-DD' },
    // { title: '撤场日期', dataIndex: 'withdrawalDate', width: 100, format: 'date|YYYY-MM-DD' },
    { title: '需求计划号', dataIndex: 'demandPlanNumber' },
    { title: '备注', dataIndex: 'remark' },
  ];
  const [registerForm, { openPopup: openFormPopup }] = usePopup();
  const [registerTable, { reload, getFetchParams }] = useTable({
    api: MachineLedgerApi.page,
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: [
        genQueryKeyword(),
        genQueryInput('器具名称', 'utensilName'),
        genQueryInput('器具编号', 'utensilNo'),
        genQueryInput('制作厂（商）', 'fabricationPlant'),
        genQueryInput('供应商', 'suppliers'),
        genQueryTimeInput('检定日期', 'verificationDate'),
        genQueryInput('领用部门', 'acceptanceDept'),
        genQueryInput('领用人', 'acceptanceUser'),
        genQueryInput('领用人身份证号', 'acceptanceIdCard'),
        genQueryInput('检定员', 'inspector'),
        genQueryTimeInput('领用时间', 'acceptanceDate'),
        genQueryTimeInput('失效/撤场/封存日期', 'expirationDate'),
        genQueryInput('状态', 'state'),
        genQueryTimeInput('有效期', 'effectiveDate'),

        genQueryInput('分级', 'classification'),
        genQueryInput('精度', 'elaborate'),
        genQueryInput('规格型号', 'specificationsModels'),
        genQueryInput('检定周期（月）', 'verificationCycle'),
        genQueryInput('到期天数', 'expireDate'),
        genQueryInput('检定单位', 'verificationUnit'),
        genQueryInput('证书编号', 'certificateNo'),
        genQueryInput('领料单小票号', 'pickingReceiptNumber'),
        genQueryInput('遗失，报废单编号', 'lostScrapOrder'),
        genQueryInput('需求计划号', 'demandPlanNumber'),
      ],
    },
    actionColumn: {
      width: 150,
      title: '操作',
      dataIndex: 'action',
    },
    rowSelection: {
      // 选中行
      onChange: (selectedRowKeys) => {
        selList.value = selectedRowKeys;
        // console.log('value:', selList.value);
        // selectedRows.forEach(row => {
        //   console.log('dept:', row.responsibleDept);
        // });
      },
    },
  });

  function getTableActions(record: any): ActionItem[] {
    return [
      {
        label: t('common.editText'),
        onClick: addOrUpdateHandle.bind(null, record.id),
      },
      {
        label: t('common.delText'),
        color: 'error',
        modelConfirm: {
          onOk: handleDelete.bind(null, record.id),
        },
      },
      {
        label: '查看资料',
        onClick: uploadFile.bind(null, record.id),
      },
    ];
  }

  // 批量删除
  function handleBatchDel() {
    const allotsList = selList.value;
    MachineLedgerApi.batchRemove(allotsList).then(res => {
      createMessage.success(res.msg);
      reload();
    });
  }

  // 查看资料
  function uploadFile(id) {
    openVerificationFile(true, id);
  }

  function handleDelete(id: any) {
    MachineLedgerApi.remove(id).then(res => {
      createMessage.success(res.msg);
      reload();
    });
  }

  function addOrUpdateHandle(id = '') {
    // console.log('id:',id);
    openFormPopup(true, { id });
  }

  function handleExport() {
    const listQuery = {
      ...getFetchParams(),
      condition: getFetchParams().condition || '',
      keyword: getFetchParams().keyword || '',
    };
    openExportModal(true, { listQuery });
  }

  function handleImport() {
    openImportModal(true, {});
  }
</script>
