<template>
  <div class="flow-form">
    <a-form :colon="false" :labelCol="{ style: { width: '90px' } }" :model="dataForm" :rules="dataRule" ref="formRef" :disabled="config.disabled">
      <a-row>
        <a-col :span="24" v-if="judgeShow('handOverPer')">
          <a-form-item label="移交人" name="handOverPer">
            <a-input v-model:value="dataForm.handOverPer" placeholder="移交人" :disabled="judgeWrite('handOverPer')" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="judgeShow('handOverTime')">
          <a-form-item label="移交日期" name="handOverTime">
            <JnpfDatePicker v-model:value="dataForm.handOverTime" placeholder="移交日期" :disabled="judgeWrite('handOverTime')" />
          </a-form-item>
        </a-col>

        <!-- <a-col :span="24" v-if="judgeShow('receivePer')">
          <a-form-item label="接收人" name="receivePer">
            <a-input v-model:value="dataForm.receivePer" placeholder="接收人" :disabled="judgeWrite('receivePer')" />
          </a-form-item>
        </a-col> -->

        <a-col :span="24" class="ant-col-item">
          <a-form-item name="receivePer">
            <template #label>接收人</template>
            <JnpfUserSelect
              @change="handleUserChange"
              v-model:value="dataForm.receivePer"
              placeholder="请选择"
              :allowClear="true"
              :style="{ width: '100%' }"
              :multiple="false"
              selectType="all">
            </JnpfUserSelect>
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="judgeShow('receiveTime')">
          <a-form-item label="接收日期" name="receiveTime">
            <JnpfDatePicker v-model:value="dataForm.receiveTime" placeholder="接收日期" :disabled="judgeWrite('receiveTime')" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="judgeShow('suppliesTime')">
          <a-form-item label="物资部/日期" name="suppliesTime">
            <JnpfDatePicker v-model:value="dataForm.suppliesTime" placeholder="物资部/日期" :disabled="judgeWrite('suppliesTime')" />
          </a-form-item>
        </a-col>

      </a-row>
    </a-form>
    <a-tabs v-model:activeKey="activeKey" size="small" v-if="config.formOperates">
      <a-tab-pane key="1" tab="转接单">
        <!-- <a-space style="margin: 10px 0">
          <a-button type="primary" @click="handleOpenImport"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>导入领用流转单列表Excel</a-button>
        </a-space> -->
        <a-table :data-source="dataForm.hdoverList" :columns="getGoodsColumns" size="small" :pagination="false" v-if="judgeShow('hdoverList')">
          <template #headerCell="{ column }"><span class="required-sign" v-if="judgeRequired(`hdoverList-${column.key}`)">*</span>{{ column.title }}</template>
          <template #bodyCell="{ column, record, index }">

            <template v-if="column.key === 'utensilName'">
              <a-input v-model:value="record.utensilName" :disabled="judgeWrite('hdoverList') || judgeWrite('hdoverList-utensilName')" />
            </template>
            <template v-if="column.key === 'utensilNo'">
              <a-input v-model:value="record.utensilNo" :disabled="judgeWrite('hdoverList') || judgeWrite('hdoverList-utensilNo')" />
            </template>
            <template v-if="column.key === 'specificationsModels'">
              <a-input v-model:value="record.specificationsModels" :disabled="judgeWrite('hdoverList') || judgeWrite('hdoverList-specificationsModels')" />
            </template>
            <template v-if="column.key === 'state'">
              <a-input v-model:value="record.state" :disabled="judgeWrite('hdoverList') || judgeWrite('hdoverList-state')" />
            </template>
            <template v-if="column.key === 'number'">
              <a-input v-model:value="record.number" :disabled="judgeWrite('hdoverList') || judgeWrite('hdoverList-number')" />
            </template>
            <template v-if="column.key === 'useDept'">
              <a-input v-model:value="record.useDept" :disabled="judgeWrite('hdoverList') || judgeWrite('hdoverList-useDept')" />
            </template>
            <template v-if="column.key === 'remark'">
              <a-input v-model:value="record.remark" :disabled="judgeWrite('hdoverList') || judgeWrite('hdoverList-remark')" />
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-button class="action-btn" type="link" color="error" @click="handleDel(index, record.id)" size="small">删除</a-button>
                <!-- <a-button v-if="record.displayStatus === 1" class="action-btn" type="link" color="blue"  @click="uploadFile(record)" size="small">查看资料</a-button> -->
              </a-space>
            </template>
          </template>
        </a-table>
        <div class="table-add-action" @click="addCounterfeit(true, {})" v-if="!(config.disabled || judgeWrite('hdoverList'))">
          <a-button type="link" preIcon="icon-ym icon-ym-btn-add">新增器具</a-button>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- <ImportModal @register="registerImportModal" @import="handleImport" /> -->
    <LedgerModal @register="registerLedgerModal" @select="onLedgerSelect" />
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref, toRefs, watch } from 'vue';
  import { useFlowForm } from '/@/views/workFlow/workFlowForm/hooks/useFlowForm';
  import type { FormInstance } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  // import ImportModal from './ImportModal.vue';
  import * as MachineHdoverfeitApi from '/@/api/flowApi/machineHdover';
  import { useMessage } from '/@/hooks/web/useMessage';
  import LedgerModal from './LedgerModal.vue';
  import { useUserStore } from '/@/store/modules/user';

  interface State {
    dataForm: any;
    dataRule: any;
    billEnCode: string;
    fileList: any[];
    activeKey: string;
  }

  defineOptions({ name: 'machineHdoverRequest' });

  const { createMessage } = useMessage();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerLedgerModal, { openModal: addCounterfeit }] = useModal();

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;

  const props = defineProps(['config']);
  const emit = defineEmits(['setPageLoad', 'eventReceiver']);
  const defaultGoodsColumns = [
    { title: '器具名称', dataIndex: 'utensilName', key: 'utensilName', width: 80 },
    { title: '器具编号', dataIndex: 'utensilNo', key: 'utensilNo', width: 80 },
    { title: '规格型号', dataIndex: 'specificationsModels', key: 'specificationsModels', width: 80 },
    { title: '状态', dataIndex: 'state', key: 'state', width: 80 },
    { title: '数量', dataIndex: 'number', key: 'number', width: 80 },
    { title: '使用部门', dataIndex: 'useDept', key: 'useDept', width: 80 },
    { title: '备注', dataIndex: 'remark', key: 'remark', width: 80 },
  ];
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      handOverPer: userInfo.userName,
      handOverTime: '',
      receivePer: '',
      receiveTime: '',
      suppliesTime: '',
      // admissionRequestName: '',
      // creatTime: undefined,
      status: '',
      flowId: '',
      hdoverList: [],
      fileJson: '',
    },
    dataRule: {
      handOverPer: [{ required: true, message: '移交人不能为空', trigger: 'change' }],
      handOverTime: [{ required: true, message: '移交日期不能为空', trigger: 'change' }],
      receivePer: [{ required: true, message: '接收人不能为空', trigger: 'change' }],
      receiveTime: [{ required: true, message: '接收日期不能为空', trigger: 'change' }],
      suppliesTime: [{ required: true, message: '物资部/日期不能为空', trigger: 'change' }],
    },
    billEnCode: 'OrderNumber',
    fileList: [],
    activeKey: '1',
  });
  const { dataForm, dataRule, activeKey } = toRefs(state);
  const { init, judgeShow, judgeWrite, judgeRequired, dataFormSubmit } = useFlowForm({
    config: props.config,
    selfState: state,
    emit,
    formRef,
  });

  defineExpose({ dataFormSubmit });
  const noColumn = { width: 50, title: '序号', dataIndex: 'index', key: 'index', align: 'center', customRender: ({ index }) => index + 1 };

  const getGoodsColumns = computed(() => {
    const actionColumn = !props.config.disabled && !judgeWrite('hdoverList') ? { title: '操作', dataIndex: 'action', key: 'action', width: 50 } : null;
    const column = defaultGoodsColumns.filter(o => judgeShow(`hdoverList-${o.key}`));
    const list = [noColumn, ...column];
    if (actionColumn) list.push(actionColumn);
    return list;
  });

  watch(
    () => state.dataForm.hdoverList,
    val => {
      let money = 0;
      for (let i = 0; i < val.length; i++) {
        const e = val[i];
        money += parseFloat(e.actualAmount);
      }
      state.dataForm.receivableMoney = money.toFixed(2);
    },
    { deep: true },
  );

  function handleDel(index, id) {
    if (id === undefined || id === null || id === '') {
      console.log('ID为空', id);
      state.dataForm.hdoverList.splice(index, 1);
    } else {
      MachineHdoverfeitApi.deleteById(id).then(res => {
        // 打印返回数据
        console.log('res', res);
      });
      console.log('ID不为空', id);
      state.dataForm.hdoverList.splice(index, 1);
    }
  }

  function onLedgerSelect(list) {
    console.log('onLedgerList:', list);
    for (let i = 0; i < list.length; i++) {
      const e = list[i];
      let item = {
        utensilName: e.utensilName,
        utensilNo: e.utensilNo,
        specificationsModels: e.specificationsModels,
        state: e.state,
        number: 1,
      };
      state.dataForm.hdoverList.push(item);
    }
  }

  function handleOpenImport() {
    openImportModal(true, {});
  }

  /**
   * 预览确认后回调
   */
  function handleImport(list) {
    console.log('list', list);
    list.forEach(function (item) {
      console.log('item:', item);
      state.dataForm.hdoverList.push(item);
    });
  }

  onMounted(() => {
    init();
  });
</script>
<style lang="less" scoped>
  .ant-tabs {
    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
    }
  }
</style>
