<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="批量导入"
    :width="1000"
    :show-cancel-btn="false"
    :show-ok-btn="false"
    destroyOnClose
    class="jnpf-import-modal">
    <div class="header-steps">
      <a-steps v-model:current="activeStep" type="navigation" size="small">
        <a-step title="上传文件" disabled />
        <a-step title="数据预览" disabled />
      </a-steps>
    </div>

    <div class="import-main" v-show="activeStep == 0">
      <div class="upload">
        <div class="up_left">
          <img src="../../../assets/images/upload.png" />
        </div>
        <div class="up_right">
          <p class="title">上传填好的数据表</p>
          <p class="tip">文件后缀名必须是xls或xlsx，文件大小不超过500KB，最多支持导入1000条数据</p>
          <jnpf-fa-upload-file-qiniu v-model:value="fileId" accept=".xls,.xlsx" />
        </div>
      </div>
      <div class="upload">
        <div class="up_left">
          <img src="../../../assets/images/import.png" />
        </div>
        <div class="up_right">
          <p class="title">填写导入数据信息</p>
          <p class="tip">请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除</p>
          <a-button type="link" @click="handleTemplateDownload()">下载模板</a-button>
        </div>
      </div>
    </div>

    <div class="import-main" v-show="activeStep == 1">
      <a-table :data-source="list" :columns="columns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '440px' }" class="import-preview-table">
        <template #bodyCell="{ column, record, index }">
          <template v-for="item in tableData">
            <template v-if="column.key === item.key">
              <a-input v-model:value="record[column.key]" />
            </template>
          </template>
          <template v-if="column.key === 'action'">
            <a-button class="action-btn" type="link" color="error" @click="handleDelItem(index)" size="small">删除</a-button>
          </template>
        </template>
      </a-table>
    </div>

    <template #insertFooter>
      <a-button @click="handleClose()" v-if="activeStep == 0">{{ t('common.cancelText') }}</a-button>
      <a-button @click="handlePrev" v-if="activeStep === 1">{{ t('common.prev') }}</a-button>
      <a-button type="primary" @click="handleNext" :loading="btnLoading" v-if="activeStep < 2" :disabled="activeStep === 0 && !fileId">
        {{ t('common.next') }}
      </a-button>
      <a-button type="primary" @click="handleClose(true)" v-else>关闭</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { reactive, toRefs, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import type { UploadFile } from 'ant-design-vue';
  import JnpfFaUploadFileQiniu from '/@/components/Jnpf/Upload/src/FaUploadFileQiniu.vue';
  import * as MachineReceiveApi from '/@/api/flowApi/machineReceive';
  import { downloadByUrl } from '/@/utils/file/download';

  interface State {
    activeStep: number;
    fileId: string;
    fileName: string;
    fileList: UploadFile[];
    btnLoading: boolean;
    list: any[];
    result: any;
    resultList: any[];
  }

  const emit = defineEmits(['register', 'reload', 'import']);
  const [registerModal, { closeModal }] = useModalInner(init);
  const { createMessage } = useMessage();
  const { t } = useI18n();
  const tableData = [
    { title: '器具名称', dataIndex: 'utensilName', key: 'utensilName', width: 150 },
    { title: '器具编号', dataIndex: 'utensilNo', key: 'utensilNo', width: 150 },
    { title: '精度', dataIndex: 'elaborate', key: 'elaborate', width: 150 },
    { title: '规格型号', dataIndex: 'specificationsModels', key: 'specificationsModels', width: 150 },
    { title: '数量', dataIndex: 'num', key: 'num', width: 150 },
    { title: '领用部门', dataIndex: 'acceptanceDept', key: 'acceptanceDept', width: 150 },
    { title: '领用人', dataIndex: 'acceptanceUser', key: 'acceptanceUser', width: 150 },
    { title: '金额', dataIndex: 'amount', key: 'amount', width: 150 },
    { title: '备注', dataIndex: 'remark', key: 'remark', width: 150 },
  ];
  const columns: any[] = [
    { width: 50, title: '序号', align: 'center', customRender: ({ index }) => index + 1 },
    ...tableData,
    { title: '操作', dataIndex: 'action', key: 'action', width: 50, fixed: 'right' },
  ];
  const state = reactive<State>({
    activeStep: 0,
    fileId: undefined,
    fileName: '',
    fileList: [],
    btnLoading: false,
    list: [],
    result: {},
    resultList: [],
  });
  const { activeStep, fileId, btnLoading, list } = toRefs(state);

  function init() {
    state.activeStep = 0;
    state.fileName = '';
    state.fileList = [];
    state.btnLoading = false;
  }

  function handlePrev() {
    if (state.activeStep == 0) return;
    state.activeStep -= 1;
  }

  function handleNext() {
    if (state.activeStep == 0) {
      if (!state.fileId) return createMessage.warning('请先上传文件');
      state.btnLoading = true;
      MachineReceiveApi.importReceivePreview(state.fileId)
        .then(res => {
          state.list = res.data || [];
          state.btnLoading = false;
          state.activeStep += 1;
        })
        .catch(() => (state.btnLoading = false));
      return;
    }
    if (state.activeStep == 1) {
      if (!state.list.length) return createMessage.warning('导入数据为空');
      emit('import', unref(state.list));
      handleClose(false);
    }
  }

  function handleTemplateDownload() {
    // window.open('http://fa.file.dward.cn/zz_szh/pro/TemplateFile/%E5%85%A5%E5%9C%BA%E4%BA%BA%E5%91%98%E7%94%B3%E8%AF%B7%E6%A8%A1%E6%9D%BF.xlsx')
    MachineReceiveApi.templateDownload().then(res => {
      downloadByUrl({ url: res.data?.url });
    });
  }

  function handleDelItem(index) {
    state.list.splice(index, 1);
  }

  function handleClose(reload) {
    closeModal();
    if (reload) emit('reload');
  }
</script>
