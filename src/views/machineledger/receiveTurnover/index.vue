<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">新建领用流转单</a-button>
          </template>
          <template #expandedRowRender="{ record }">
            <a-tabs size="small" v-loading="record.childTableLoading">
              <a-tab-pane key="1" tab="领用流转单">
                <BasicTable @register="registerGoodsTable" :data-source="record.goodsList"> </BasicTable>
              </a-tab-pane>
            </a-tabs>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'selectPerNames'">
              <span>{{ record.selectPerNames.join(',') }}</span>
            </template>
            <template v-if="column.key === 'state'">
              <FaFlowStatus :status="record.state" />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <FaFlowCube ref="flowRef" flow-en-code="machineReceiveRequest" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import * as MachineReceiveApi from '/@/api/flowApi/machineReceive';
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import FaFlowCube from '/@/components/Fa/common/src/FaFlowCube.vue';
  import { genFlowDetailBtn, genFlowDeleteBtn, genFlowEditBtn } from '/@/utils/tableUtils';
  import { FLOW_OP_TYPE } from "/@/enums/zzEnums";

  defineOptions({ name: 'ext-machine-receive' });

  const flowRef = ref<any>();

  const { createMessage } = useMessage();
  const { t } = useI18n();
  const columns: BasicColumn[] = [
    { title: '领用单位', dataIndex: 'acceptanceUnit', width: 150 },
    { title: '领用时间', dataIndex: 'acceptanceTime', width: 170, format: 'date|YYYY-MM-DD HH:mm:ss' },
    { title: '审批人选', dataIndex: 'approveUser', width: 150 },
    { title: '领用人选', dataIndex: 'acceptanceUser', width: 150 },
    { title: '领用单号', dataIndex: 'acceptanceNo', width: 150 },
    { title: '状态', dataIndex: 'state', width: 120, align: 'center'},
  ];
  const goodsColumns: BasicColumn[] = [
    { title: '器具名称', dataIndex: 'utensilName' },
    { title: '器具编号', dataIndex: 'utensilNo' },
    { title: '精度', dataIndex: 'elaborate' },
    { title: '规格型号', dataIndex: 'specificationsModels' },
    { title: '数量', dataIndex: 'num' },
    { title: '领用部门', dataIndex: 'acceptanceDept' },
    { title: '领用人', dataIndex: 'acceptanceUser' },
    { title: '金额', dataIndex: 'amount' },
    { title: '备注', dataIndex: 'remark' },
  ];

  const [registerTable, { reload }] = useTable({
    api: MachineReceiveApi.getRequestList,
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'keyword',
          label: t('common.keyword'),
          component: 'Input',
          componentProps: { placeholder: t('common.enterKeyword'), submitOnPressEnter: true },
        },
        // {
        //   field: 'pickerVal',
        //   label: '日期',
        //   component: 'DateRange',
        // },
      ],
      fieldMapToTime: [['pickerVal', ['verificationDate', 'admissionDate']]],
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
    onExpand: handleExpand,
  });
  const [registerGoodsTable] = useTable({
    columns: goodsColumns,
    pagination: false,
    showTableSetting: false,
    canResize: false,
    scroll: { x: undefined },
  });

  function getTableActions(record): ActionItem[] {
    return [genFlowEditBtn(record, toDetail), genFlowDeleteBtn(record, handleDelete), genFlowDetailBtn(record, toDetail)];
  }

  function handleAdd() {
    flowRef.value.handleAdd();
  }

  function toDetail(record: any, opType: FLOW_OP_TYPE) {
    flowRef.value.toDetail(record, opType);
  }

  function handleDelete(id: string) {
    MachineReceiveApi.delAdmission(id).then(res => {
      createMessage.success(res.msg);
      reload();
    });
  }
 
  function handleExpand(expanded, record) {
    if (!expanded || record.goodsList?.length || record.planList?.length) return;
    record.childTableLoading = true;
    console.log(record);
    MachineReceiveApi.getReceiveList(record.id)
      .then(res => {
        record.childTableLoading = false;
        record.goodsList = res.data.list;
      })
      .catch(() => {
        record.childTableLoading = false;
      });
  }
  
</script>
