<template>
  <div class="flow-form">
    <a-form :colon="false" :labelCol="{ style: { width: '90px' } }" :model="dataForm" :rules="dataRule" ref="formRef" :disabled="config.disabled">
      <a-row>
        <a-col :span="24" v-if="judgeShow('acceptanceUnit')">
          <a-form-item label="领用单位" name="acceptanceUnit">
            <a-select v-model:value="dataForm.acceptanceUnit" placeholder="领用单位" :disabled="judgeWrite('acceptanceUnit')">
              <a-select-option v-for="item in deptTypeOptions" :key="item" :value="item">{{ item }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="judgeShow('acceptanceTime')">
          <a-form-item label="领用时间" name="acceptanceTime">
            <JnpfDatePicker v-model:value="dataForm.acceptanceTime" placeholder="领用时间" :disabled="judgeWrite('acceptanceTime')" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="judgeShow('approveUser')">
          <a-form-item label="审批人选" name="approveUser">
            <a-select v-model:value="dataForm.approveUser" :disabled="judgeWrite('approveUser')">
              <a-select-option value="物资部经理">物资部经理</a-select-option>
              <a-select-option value="物资部副经理">物资部副经理</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="24" class="ant-col-item">
          <a-form-item name="acceptanceUser">
            <template #label>领用人选</template>
            <JnpfUserSelect
              @change="handleUserChange"
              v-model:value="dataForm.acceptanceUser"
              placeholder="请选择"
              :allowClear="true"
              :style="{ width: '100%' }"
              :multiple="false"
              selectType="all">
            </JnpfUserSelect>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-tabs v-model:activeKey="activeKey" size="small" v-if="config.formOperates">
      <a-tab-pane key="1" tab="领用流转单列表">
        <a-table :data-source="dataForm.receiveList" :columns="getGoodsColumns" size="small" :pagination="false" v-if="judgeShow('receiveList')">
          <template #headerCell="{ column }"><span class="required-sign" v-if="judgeRequired(`receiveList-${column.key}`)">*</span>{{ column.title }}</template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'utensilName'">
              <a-input v-model:value="record.utensilName" :disabled="judgeWrite('receiveList') || judgeWrite('receiveList-utensilName')" />
            </template>

            <template v-if="column.key === 'utensilNo'">
              <a-input v-model:value="record.utensilNo" :disabled="judgeWrite('receiveList') || judgeWrite('receiveList-utensilNo')" />
            </template>
            <template v-if="column.key === 'elaborate'">
              <a-input v-model:value="record.elaborate" :disabled="judgeWrite('receiveList') || judgeWrite('receiveList-elaborate')" />
            </template>
            <template v-if="column.key === 'specificationsModels'">
              <a-input v-model:value="record.specificationsModels" :disabled="judgeWrite('receiveList') || judgeWrite('receiveList-specificationsModels')" />
            </template>

            <template v-if="column.key === 'num'">
              <a-input v-model:value="record.num" :disabled="judgeWrite('receiveList') || judgeWrite('receiveList-num')" :defaultValue="1" />
            </template>
            <template v-if="column.key === 'acceptanceDept'">
              <a-input v-model:value="record.acceptanceDept" :disabled="judgeWrite('receiveList') || judgeWrite('receiveList-acceptanceDept')" />
            </template>

            <template v-if="column.key === 'acceptanceUser'">
              <a-input v-model:value="record.acceptanceUser" :disabled="judgeWrite('receiveList') || judgeWrite('receiveList-acceptanceUser')" />
            </template>

            <template v-if="column.key === 'amount'">
              <a-input v-model:value="record.amount" :disabled="judgeWrite('receiveList') || judgeWrite('receiveList-amount')" />
            </template>

            <template v-if="column.key === 'remark'">
              <a-input v-model:value="record.remark" :disabled="judgeWrite('receiveList') || judgeWrite('receiveList-remark')" />
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-button class="action-btn" type="link" color="error" @click="handleDel(index, record.id)" size="small">删除</a-button>
                <!-- <a-button v-if="record.displayStatus === 1" class="action-btn" type="link" color="blue"  @click="uploadFile(record)" size="small">查看资料</a-button> -->
              </a-space>
            </template>
          </template>
        </a-table>
        <div class="table-add-action" @click="addCounterfeit(true, {})" v-if="!(config.disabled || judgeWrite('receiveList'))">
          <a-button type="link" preIcon="icon-ym icon-ym-btn-add">新增器具</a-button>
        </div>
      </a-tab-pane>
    </a-tabs>

    <ImportModal @register="registerImportModal" @import="handleImport" />
    <LedgerModal @register="registerLedgerModal" @select="onLedgerSelect" />
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref, toRefs, watch } from 'vue';
  import { useFlowForm } from '/@/views/workFlow/workFlowForm/hooks/useFlowForm';
  import type { FormInstance } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  import ImportModal from './ImportModal.vue';
  import * as MachineReceivefeitApi from '/@/api/flowApi/machineReceive';
  import { useMessage } from '/@/hooks/web/useMessage';
  import LedgerModal from './LedgerModal.vue';
  import { getDictionaryDataSelector } from '/@/api/systemData/dictionary';
  import * as userApi from '/@/api/permission/user';

  interface State {
    dataForm: any;
    dataRule: any;
    billEnCode: string;
    fileList: any[];
    activeKey: string;
  }

  defineOptions({ name: 'machineReceiveRequest' });

  const { createMessage } = useMessage();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerLedgerModal, { openModal: addCounterfeit }] = useModal();

  const props = defineProps(['config']);
  const emit = defineEmits(['setPageLoad', 'eventReceiver']);
  const defaultGoodsColumns = [
    { title: '器具名称', dataIndex: 'utensilName', key: 'utensilName', width: 80 },
    { title: '器具编号', dataIndex: 'utensilNo', key: 'utensilNo', width: 80 },
    { title: '精度', dataIndex: 'elaborate', key: 'elaborate', width: 80 },
    { title: '规格型号', dataIndex: 'specificationsModels', key: 'specificationsModels', width: 80 },
    { title: '数量', dataIndex: 'num', key: 'num', width: 120 },
    { title: '领用部门', dataIndex: 'acceptanceDept', key: 'acceptanceDept', width: 120 },
    { title: '领用人', dataIndex: 'acceptanceUser', key: 'acceptanceUser', width: 120 },
    { title: '金额', dataIndex: 'amount', key: 'amount', width: 120 },
    { title: '备注', dataIndex: 'remark', key: 'remark', width: 120 },
  ];
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      acceptanceUnit: '',
      acceptanceTime: new Date(), // 设置默认值为当前时间
      approveUser: '',
      acceptanceUser: '',
      // admissionRequestName: '',
      // creatTime: undefined,
      status: '',
      flowId: '',
      receiveList: [],
      fileJson: '',
    },
    dataRule: {
      acceptanceUnit: [{ required: true, message: '领用单位不能为空', trigger: 'change' }],
      acceptanceTime: [{ required: true, message: '领用时间不能为空', trigger: 'change' }],
      approveUser: [{ required: true, message: '审批人选不能为空', trigger: 'change' }],
      acceptanceUser: [{ required: true, message: '领用人选不能为空', trigger: 'change' }],
    },
    billEnCode: 'OrderNumber',
    fileList: [],
    activeKey: '1',
  });
  const { dataForm, dataRule, activeKey } = toRefs(state);
  const { init, judgeShow, judgeWrite, judgeRequired, dataFormSubmit } = useFlowForm({
    config: props.config,
    selfState: state,
    emit,
    formRef,
  });

  defineExpose({ dataFormSubmit });
  const noColumn = { width: 50, title: '序号', dataIndex: 'index', key: 'index', align: 'center', customRender: ({ index }) => index + 1 };

  const getGoodsColumns = computed(() => {
    const actionColumn = !props.config.disabled && !judgeWrite('receiveList') ? { title: '操作', dataIndex: 'action', key: 'action', width: 50 } : null;
    const column = defaultGoodsColumns.filter(o => judgeShow(`receiveList-${o.key}`));
    const list = [noColumn, ...column];
    if (actionColumn) list.push(actionColumn);
    return list;
  });

  watch(
    () => state.dataForm.receiveList,
    val => {
      let money = 0;
      for (let i = 0; i < val.length; i++) {
        const e = val[i];
        money += parseFloat(e.actualAmount);
      }
      state.dataForm.receivableMoney = money.toFixed(2);
    },
    { deep: true },
  );

  function handleDel(index, id) {
    if (id === undefined || id === null || id === '') {
      console.log('ID为空', id);
      state.dataForm.receiveList.splice(index, 1);
    } else {
      MachineReceivefeitApi.deleteById(id).then(res => {
        // 打印返回数据
        console.log('res', res);
      });
      console.log('ID不为空', id);
      state.dataForm.receiveList.splice(index, 1);
    }
  }

  // function onLedgerSelect(list) {
  //   console.log('onLedgerList:', list);
  //   for (let i = 0; i < list.length; i++) {

  //     let name = "";
  //     userApi.getUserInfo(state.dataForm.acceptanceUser).then(res => {
  //       console.log('id:', res);
  //       name = res.data.realName;
  //     });

  //     console.log('name:', name);
  //     const e = list[i];
  //     let item = {
  //       utensilName: e.utensilName,
  //       utensilNo: e.utensilNo,
  //       elaborate: e.elaborate,
  //       specificationsModels: e.specificationsModels,
  //       acceptanceUser: name,
  //     };
  //     state.dataForm.receiveList.push(item);
  //   }
  // }
  async function onLedgerSelect(list) {  
    console.log('onLedgerList:', list);  
    let name = await getUserRealName(); // 只调用一次  
  
    for (let i = 0; i < list.length; i++) {  
        let e = list[i];  
  
        console.log('name:', name);  
        let item = {  
            utensilName: e.utensilName,  
            utensilNo: e.utensilNo,  
            elaborate: e.elaborate,  
            specificationsModels: e.specificationsModels,  
            acceptanceUser: name,  
        };  
        state.dataForm.receiveList.push(item);  
    }  
}  
  
// 封装异步调用  
async function getUserRealName() {  
    try {  
        const res = await userApi.getUserInfo(state.dataForm.acceptanceUser);  
        return res.data.realName;  
    } catch (error) {  
        console.error('Error fetching user info:', error);  
        return null; // 或者其他适当的错误处理  
    }  
}

  function handleOpenImport() {
    openImportModal(true, {});
  }

  /**
   * 预览确认后回调
   */
  function handleImport(list) {
    console.log('list', list);
    list.forEach(function (item) {
      console.log('item:', item);
      state.dataForm.receiveList.push(item);
    });
  }

  //数据选项--数据字典初始化方法
  const deptTypeOptions = ref<Array<any>>([]);

  function getdeptOptions() {
    getDictionaryDataSelector('efdbdac8d9e04539b85cda898aaadsa').then(res => {
      console.log(res.data);
      res.data.list.forEach(item => {
        deptTypeOptions.value.push(item.fullName);
      });
    });
  }

  onMounted(() => {
    init();
    getdeptOptions();
  });
</script>
<style lang="less" scoped>
  .ant-tabs {
    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
    }
  }
</style>
