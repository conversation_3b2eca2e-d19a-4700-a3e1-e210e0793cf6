<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">新建退库报告单</a-button>
          </template>
          <template #expandedRowRender="{ record }">
            <a-tabs size="small" v-loading="record.childTableLoading">
              <a-tab-pane key="1" tab="退库报告单">
                <BasicTable @register="registerGoodsTable" :data-source="record.goodsList"> </BasicTable>
              </a-tab-pane>
            </a-tabs>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'selectPerNames'">
              <span>{{ record.selectPerNames.join(',') }}</span>
            </template>
            <template v-if="column.key === 'state'">
              <FaFlowStatus :status="record.state" />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <FaFlowCube ref="flowRef" flow-en-code="machineInspectionRequest" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import * as MachineInspectionApi from '/@/api/flowApi/machineInspection';
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import FaFlowCube from '/@/components/Fa/common/src/FaFlowCube.vue';
  import { genFlowDetailBtn, genFlowDeleteBtn, genFlowEditBtn } from '/@/utils/tableUtils';
  import { FLOW_OP_TYPE } from "/@/enums/zzEnums";

  defineOptions({ name: 'ext-machine-inspection' });

  const flowRef = ref<any>();

  const { createMessage } = useMessage();
  const { t } = useI18n();
  const columns: BasicColumn[] = [
    { title: '退库人', dataIndex: 'returnStoreroomPer', width: 100},
    { title: '退库日期', dataIndex: 'returnStoreroomTime', format: 'date|YYYY-MM-DD HH:mm:ss' },
    { title: '接收人', dataIndex: 'receivePer' , width: 100},
    { title: '接收日期', dataIndex: 'receiveTime', format: 'date|YYYY-MM-DD HH:mm:ss' },
    { title: '流水单号', dataIndex: 'invoiceNumber' },
    { title: '状态', dataIndex: 'state', width: 120, align: 'center' },
  ];
  const goodsColumns: BasicColumn[] = [
    { title: '器具名称', dataIndex: 'utensilName' },
    { title: '出厂编号', dataIndex: 'utensilNo' },
    { title: '精度等级', dataIndex: 'elaborate' },
    { title: '规格型号', dataIndex: 'specificationsModels' },
    { title: '退库原因', dataIndex: 'sendBackReason' },
    { title: '备注', dataIndex: 'remark' },
  ];

  const [registerTable, { reload }] = useTable({
    api: MachineInspectionApi.getRequestList,
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'keyword',
          label: t('common.keyword'),
          component: 'Input',
          componentProps: { placeholder: t('common.enterKeyword'), submitOnPressEnter: true },
        },
        // {
        //   field: 'pickerVal',
        //   label: '日期',
        //   component: 'DateRange',
        // },
      ],
      fieldMapToTime: [['pickerVal', ['verificationDate', 'admissionDate']]],
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
    onExpand: handleExpand,
  });
  const [registerGoodsTable] = useTable({
    columns: goodsColumns,
    pagination: false,
    showTableSetting: false,
    canResize: false,
    scroll: { x: undefined },
  });

  function getTableActions(record): ActionItem[] {
    return [genFlowEditBtn(record, toDetail), genFlowDeleteBtn(record, handleDelete), genFlowDetailBtn(record, toDetail)];
  }

  function handleDelete(id: string) {
    MachineInspectionApi.delAdmission(id).then(res => {
      createMessage.success(res.msg);
      reload();
    });
  }

  function handleAdd() {
    flowRef.value.handleAdd();
  }

  function toDetail(record: any, opType: FLOW_OP_TYPE) {
    flowRef.value.toDetail(record, opType);
  }

  function handleExpand(expanded, record) {
    if (!expanded || record.goodsList?.length || record.planList?.length) return;
    record.childTableLoading = true;
    console.log(record);
    MachineInspectionApi.getInspectionList(record.id)
      .then(res => {
        record.childTableLoading = false;
        record.goodsList = res.data.list;
      })
      .catch(() => {
        record.childTableLoading = false;
      });
  }
  
</script>
