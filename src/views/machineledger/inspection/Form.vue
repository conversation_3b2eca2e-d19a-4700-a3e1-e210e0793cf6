<template>
  <div class="flow-form">
    <a-form :colon="false" :labelCol="{ style: { width: '90px' } }" :model="dataForm" :rules="dataRule" ref="formRef" :disabled="config.disabled">
      <a-row>
        <a-col :span="24" v-if="judgeShow('returnStoreroomPer')">
          <a-form-item label="退库人" name="returnStoreroomPer">
            <a-input v-model:value="dataForm.returnStoreroomPer" placeholder="退库人" :disabled="judgeWrite('returnStoreroomPer')" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="judgeShow('returnStoreroomTime')">
          <a-form-item label="退库日期" name="returnStoreroomTime">
            <JnpfDatePicker v-model:value="dataForm.returnStoreroomTime" placeholder="退库日期" :disabled="judgeWrite('returnStoreroomTime')" />
          </a-form-item>
        </a-col>

        <!-- <a-col :span="24" v-if="judgeShow('receivePer')">
          <a-form-item label="接收人" name="receivePer">
            <a-input v-model:value="dataForm.receivePer" placeholder="接收人" :disabled="judgeWrite('receivePer')" />
          </a-form-item>
        </a-col> -->

        <!-- <a-col :span="24" v-if="judgeShow('receivePer')">
          <a-form-item label="接收人" name="receivePer">
            <a-select v-model:value="dataForm.receivePer" placeholder="接收人" :disabled="judgeWrite('receivePer')">
              <a-select-option v-for="item in meteringUserOptions" :key="item" :value="item">{{ item }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col> -->

        <a-col :span="24" class="ant-col-item">
          <a-form-item name="receivePer">
            <template #label>接收人</template>
            <JnpfUserSelect
              @change="handleUserChange"
              v-model:value="dataForm.receivePer"
              placeholder="请选择"
              :allowClear="true"
              :style="{ width: '100%' }"
              :multiple="false"
              selectType="all">
            </JnpfUserSelect>
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="judgeShow('receiveTime')">
          <a-form-item label="接收日期" name="receiveTime">
            <JnpfDatePicker v-model:value="dataForm.receiveTime" placeholder="接收日期" :disabled="judgeWrite('receiveTime')" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-tabs v-model:activeKey="activeKey" size="small" v-if="config.formOperates">
      <a-tab-pane key="1" tab="退库单">
        <!-- <a-space style="margin: 10px 0">
          <a-button type="primary" @click="handleOpenImport"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>导入领用流转单列表Excel</a-button>
        </a-space> -->
        <a-table :data-source="dataForm.inspectionList" :columns="getGoodsColumns" size="small" :pagination="false" v-if="judgeShow('inspectionList')">
          <template #headerCell="{ column }"
            ><span class="required-sign" v-if="judgeRequired(`inspectionList-${column.key}`)">*</span>{{ column.title }}</template
          >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'utensilName'">
              <a-input v-model:value="record.utensilName" :disabled="judgeWrite('inspectionList') || judgeWrite('inspectionList-utensilName')" />
            </template>
            <template v-if="column.key === 'utensilNo'">
              <a-input v-model:value="record.utensilNo" :disabled="judgeWrite('inspectionList') || judgeWrite('inspectionList-utensilNo')" />
            </template>
            <template v-if="column.key === 'specificationsModels'">
              <a-input
                v-model:value="record.specificationsModels"
                :disabled="judgeWrite('inspectionList') || judgeWrite('inspectionList-specificationsModels')" />
            </template>
            <template v-if="column.key === 'elaborate'">
              <a-input v-model:value="record.elaborate" :disabled="judgeWrite('inspectionList') || judgeWrite('inspectionList-elaborate')" />
            </template>

            <template v-if="column.key === 'sendBackReason'">
              <a-select v-model:value="record.sendBackReason" :disabled="judgeWrite('inspectionList') || judgeWrite('inspectionList-sendBackReason')">
                <a-select-option v-for="item in inspectionCauseOptions" :key="item" :value="item">{{ item }}</a-select-option>
              </a-select>
            </template>

            <template v-if="column.key === 'inspectionImage'">
              <JnpfFaUploadFileQiniu
                v-model:value="record.inspectionImage"
                prefix="test"
                :disabled="judgeWrite('inspectionList') || judgeWrite('inspectionList-inspectionImage')" />
            </template>

            <template v-if="column.key === 'remark'">
              <a-input v-model:value="record.remark" :disabled="judgeWrite('inspectionList') || judgeWrite('inspectionList-remark')" />
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-button class="action-btn" type="link" color="error" @click="handleDel(index, record.id)" size="small">删除</a-button>
                <!-- <a-button v-if="record.displayStatus === 1" class="action-btn" type="link" color="blue"  @click="uploadFile(record)" size="small">查看资料</a-button> -->
              </a-space>
            </template>
          </template>
        </a-table>
        <div class="table-add-action" @click="addCounterfeit(true, {})" v-if="!(config.disabled || judgeWrite('inspectionList'))">
          <a-button type="link" preIcon="icon-ym icon-ym-btn-add">新增器具</a-button>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- <ImportModal @register="registerImportModal" @import="handleImport" /> -->
    <LedgerModal @register="registerLedgerModal" @select="onLedgerSelect" />
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref, toRefs, watch } from 'vue';
  import { useFlowForm } from '/@/views/workFlow/workFlowForm/hooks/useFlowForm';
  import type { FormInstance } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  // import ImportModal from './ImportModal.vue';
  import * as MachineInspectionApi from '/@/api/flowApi/machineInspection';
  import { useMessage } from '/@/hooks/web/useMessage';
  import LedgerModal from './LedgerModal.vue';
  import { useUserStore } from '/@/store/modules/user';
  import { getDictionaryDataSelector } from '/@/api/systemData/dictionary';
  import JnpfFaUploadFileQiniu from '../counterfeit/FaUploadFileQiniu.vue';

  interface State {
    dataForm: any;
    dataRule: any;
    billEnCode: string;
    fileList: any[];
    activeKey: string;
  }

  defineOptions({ name: 'machineInspectionRequest' });

  const { createMessage } = useMessage();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerLedgerModal, { openModal: addCounterfeit }] = useModal();

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;

  const props = defineProps(['config']);
  const emit = defineEmits(['setPageLoad', 'eventReceiver']);
  const defaultGoodsColumns = [
    { title: '器具名称', dataIndex: 'utensilName', key: 'utensilName', width: 80 },
    { title: '器具编号', dataIndex: 'utensilNo', key: 'utensilNo', width: 80 },
    { title: '规格型号', dataIndex: 'specificationsModels', key: 'specificationsModels', width: 80 },
    { title: '精度', dataIndex: 'elaborate', key: 'elaborate', width: 80 },
    { title: '退库原因', dataIndex: 'sendBackReason', key: 'sendBackReason', width: 80 },
    { title: '退库图片', dataIndex: 'inspectionImage', key: 'inspectionImage', width: 120 },
    { title: '备注', dataIndex: 'remark', key: 'remark', width: 80 },
  ];
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      returnStoreroomPer: userInfo.userName,
      returnStoreroomTime: '',
      receivePer: '',
      receiveTime: '',
      // admissionRequestName: '',
      // creatTime: undefined,
      status: '',
      flowId: '',
      inspectionList: [],
      fileJson: '',
    },
    dataRule: {
      returnStoreroomPer: [{ required: true, message: '退库人不能为空', trigger: 'change' }],
      returnStoreroomTime: [{ required: true, message: '退库日期不能为空', trigger: 'change' }],
      receivePer: [{ required: true, message: '接收人不能为空', trigger: 'change' }],
      receiveTime: [{ required: true, message: '接收日期不能为空', trigger: 'change' }],
    },
    billEnCode: 'OrderNumber',
    fileList: [],
    activeKey: '1',
  });
  const { dataForm, dataRule, activeKey } = toRefs(state);
  const { init, judgeShow, judgeWrite, judgeRequired, dataFormSubmit } = useFlowForm({
    config: props.config,
    selfState: state,
    emit,
    formRef,
  });

  defineExpose({ dataFormSubmit });
  const noColumn = { width: 50, title: '序号', dataIndex: 'index', key: 'index', align: 'center', customRender: ({ index }) => index + 1 };

  const getGoodsColumns = computed(() => {
    const actionColumn = !props.config.disabled && !judgeWrite('inspectionList') ? { title: '操作', dataIndex: 'action', key: 'action', width: 50 } : null;
    const column = defaultGoodsColumns.filter(o => judgeShow(`inspectionList-${o.key}`));
    const list = [noColumn, ...column];
    if (actionColumn) list.push(actionColumn);
    return list;
  });

  watch(
    () => state.dataForm.inspectionList,
    val => {
      let money = 0;
      for (let i = 0; i < val.length; i++) {
        const e = val[i];
        money += parseFloat(e.actualAmount);
      }
      state.dataForm.receivableMoney = money.toFixed(2);
    },
    { deep: true },
  );

  function handleDel(index, id) {
    if (id === undefined || id === null || id === '') {
      console.log('ID为空', id);
      state.dataForm.inspectionList.splice(index, 1);
    } else {
      MachineInspectionApi.deleteById(id).then(res => {
        // 打印返回数据
        console.log('res', res);
      });
      console.log('ID不为空', id);
      state.dataForm.inspectionList.splice(index, 1);
    }
  }

  function onLedgerSelect(list) {
    console.log('onLedgerList:', list);
    for (let i = 0; i < list.length; i++) {
      const e = list[i];
      let item = {
        utensilName: e.utensilName,
        utensilNo: e.utensilNo,
        specificationsModels: e.specificationsModels,
        elaborate: e.elaborate,
      };
      state.dataForm.inspectionList.push(item);
    }
    getCauseOptions();
  }

  function handleOpenImport() {
    openImportModal(true, {});
  }

  /**
   * 预览确认后回调
   */
  function handleImport(list) {
    console.log('list', list);
    list.forEach(function (item) {
      console.log('item:', item);
      state.dataForm.inspectionList.push(item);
    });
  }

  //数据选项--数据字典初始化方法
  const inspectionCauseOptions = ref<Array<any>>([]);

  function getCauseOptions() {
    getDictionaryDataSelector('efdbdac8d9e04539b85cda898aaadsb').then(res => {
      console.log(res.data);
      res.data.list.forEach(item => {
        inspectionCauseOptions.value.push(item.fullName);
      });
    });
  }

  //数据选项
  const meteringUserOptions = ref<Array<any>>([]);

  function getMeteringOptions() {
    MachineInspectionApi.getMeteringList('592715291134270789').then(res => {
      console.log(res.data);
      res.data.forEach(item => {
        meteringUserOptions.value.push(item);
      });
    });
  }

  onMounted(() => {
    init();
    getMeteringOptions();
  });
</script>
<style lang="less" scoped>
  .ant-tabs {
    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
    }
  }
</style>
