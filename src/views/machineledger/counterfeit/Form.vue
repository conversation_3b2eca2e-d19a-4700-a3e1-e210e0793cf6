<template>
  <div class="flow-form">
    <a-form :colon="false" :labelCol="{ style: { width: '90px' } }" :model="dataForm" :rules="dataRule" ref="formRef" :disabled="config.disabled">
      <a-row>
        <a-col :span="24" v-if="judgeShow('creatorUserId')">
          <a-form-item label="申请人" name="creatorUserId">
            <a-input v-model:value="dataForm.creatorUserId" placeholder="申请人" :disabled="judgeWrite('creatorUserId')" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-tabs v-model:activeKey="activeKey" size="small" v-if="config.formOperates">
      <a-tab-pane key="1" tab="入场检查及防造假核查列表">
        <a-space style="margin: 10px 0">
          <a-button type="primary" @click="handleOpenImport"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>导入核查列表Excel</a-button>
        </a-space>
        <a-table
          :data-source="dataForm.utensilCounterfeitList"
          :columns="getGoodsColumns"
          size="small"
          :pagination="false"
          v-if="judgeShow('utensilCounterfeitList')">
          <template #headerCell="{ column }"
            ><span class="required-sign" v-if="judgeRequired(`utensilCounterfeitList-${column.key}`)">*</span>{{ column.title }}</template
          >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'utensilName'">
              <a-input
                v-model:value="record.utensilName"
                :disabled="judgeWrite('utensilCounterfeitList') || judgeWrite('utensilCounterfeitList-utensilName')" />
            </template>

            <template v-if="column.key === 'utensilNo'">
              <a-input v-model:value="record.utensilNo" :disabled="judgeWrite('utensilCounterfeitList') || judgeWrite('utensilCounterfeitList-utensilNo')" />
            </template>
            <template v-if="column.key === 'elaborate'">
              <a-input v-model:value="record.elaborate" :disabled="judgeWrite('utensilCounterfeitList') || judgeWrite('utensilCounterfeitList-elaborate')" />
            </template>
            <template v-if="column.key === 'specificationsModels'">
              <a-input
                v-model:value="record.specificationsModels"
                :disabled="judgeWrite('utensilCounterfeitList') || judgeWrite('utensilCounterfeitList-specificationsModels')" />
            </template>

            <template v-if="column.key === 'verificationDate'">
              <JnpfDatePicker
                v-model:value="record.verificationDate"
                :disabled="judgeWrite('utensilCounterfeitList') || judgeWrite('utensilCounterfeitList-verificationDate')" />
            </template>
            <template v-if="column.key === 'admissionDate'">
              <JnpfDatePicker
                v-model:value="record.admissionDate"
                :disabled="judgeWrite('utensilCounterfeitList') || judgeWrite('utensilCounterfeitList-admissionDate')" />
            </template>

            <template v-if="column.key === 'unitQualificationList'">
              <a-select
                v-model:value="record.unitQualificationList"
                :disabled="judgeWrite('utensilCounterfeitList') || judgeWrite('utensilCounterfeitList-unitQualificationList')"
                mode="multiple">
                <a-select-option value="CNAS查验">CNAS查验</a-select-option>
                <a-select-option value="官网查验">官网查验</a-select-option>
              </a-select>
            </template>

            <template v-if="column.key === 'certificateAuthenticityList'">
              <a-select
                v-model:value="record.certificateAuthenticityList"
                :disabled="judgeWrite('utensilCounterfeitList') || judgeWrite('utensilCounterfeitList-certificateAuthenticityList')"
                mode="multiple">
                <a-select-option value="电话查验">电话查验</a-select-option>
                <a-select-option value="扫码查验">扫码查验</a-select-option>
                <a-select-option value="官网查验">官网查验</a-select-option>
              </a-select>
            </template>

            <template v-if="column.key === 'qualificationState'">
              <a-select
                v-model:value="record.qualificationState"
                :disabled="judgeWrite('utensilCounterfeitList') || judgeWrite('utensilCounterfeitList-qualificationState')">
                <a-select-option value="是">是</a-select-option>
                <a-select-option value="否">否</a-select-option>
              </a-select>
            </template>

            <template v-if="column.key === 'remark'">
              <a-input v-model:value="record.remark" :disabled="judgeWrite('utensilCounterfeitList') || judgeWrite('utensilCounterfeitList-remark')" />
            </template>

            <template v-if="column.key === 'verificationCertificate'">
                <JnpfFaUploadFileQiniu 
                v-model:value="record.verificationCertificate" 
                prefix="test" :disabled="judgeWrite('utensilCounterfeitList') || judgeWrite('utensilCounterfeitList-verificationCertificate')"/>
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-button class="action-btn" type="link" color="error" @click="handleDel(index, record.id)" size="small">删除</a-button>
                <!-- <a-button v-if="record.displayStatus === 1" class="action-btn" type="link" color="blue"  @click="uploadFile(record)" size="small">查看资料</a-button> -->
              </a-space>
            </template>
          </template>
        </a-table>
        <div class="table-add-action" @click="addCounterfeit(true, {})" v-if="!(config.disabled || judgeWrite('utensilCounterfeitList'))">
          <a-button type="link" preIcon="icon-ym icon-ym-btn-add">新增器具</a-button>
        </div>
      </a-tab-pane>
    </a-tabs>

    <ImportModal @register="registerImportModal" @import="handleImport" />
    <LedgerModal @register="registerLedgerModal" @select="onLedgerSelect" />
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, reactive, ref, toRefs, watch } from 'vue';
  import { useFlowForm } from '/@/views/workFlow/workFlowForm/hooks/useFlowForm';
  import type { FormInstance } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  import ImportModal from './ImportModal.vue';
  import * as MachineCounterfeitApi from '/@/api/flowApi/machineCounterfeit';
  import { useMessage } from '/@/hooks/web/useMessage';
  import LedgerModal from './LedgerModal.vue';
  import { useUserStore } from '/@/store/modules/user';
  // import JnpfFaUploadFileQiniu from "/@/components/Jnpf/Upload/src/FaUploadFileQiniu.vue";
  import JnpfFaUploadFileQiniu from "./FaUploadFileQiniu.vue"
  import { parseDate } from "/@/utils/formUtils";

  interface State {
    dataForm: any;
    dataRule: any;
    billEnCode: string;
    fileList: any[];
    activeKey: string;
  }

  defineOptions({ name: 'machineCounterfeitRequest' });

  const userStore = useUserStore();
    const userInfo = userStore.getUserInfo;

  const { createMessage } = useMessage();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerLedgerModal, { openModal: addCounterfeit }] = useModal();

  const props = defineProps(['config']);
  const emit = defineEmits(['setPageLoad', 'eventReceiver']);
  const defaultGoodsColumns = [
    { title: '器具名称', dataIndex: 'utensilName', key: 'utensilName', width: 80 },
    { title: '器具编号', dataIndex: 'utensilNo', key: 'utensilNo', width: 80 },
    { title: '精度', dataIndex: 'elaborate', key: 'elaborate', width: 80 },
    { title: '规格型号', dataIndex: 'specificationsModels', key: 'specificationsModels', width: 80 },
    { title: '检定日期', dataIndex: 'verificationDate', key: 'verificationDate', width: 120 },
    { title: '入场时间', dataIndex: 'admissionDate', key: 'admissionDate', width: 120 },
    { title: '单位资质查验', dataIndex: 'unitQualificationList', key: 'unitQualificationList', width: 120 },
    { title: '证书真伪查验', dataIndex: 'certificateAuthenticityList', key: 'certificateAuthenticityList', width: 120 },
    { title: '合格状态', dataIndex: 'qualificationState', key: 'qualificationState', width: 120 },
    { title: '校准证书', dataIndex: 'verificationCertificate', key: 'verificationCertificate', width: 100 },
    { title: '备注', dataIndex: 'remark', key: 'remark', width: 120 },
  ];
  const formRef = ref<FormInstance>();
  const state = reactive<State>({
    dataForm: {
      creatorUserId: userInfo.userName,
      // admissionRequestName: '',
      // creatTime: undefined,
      status: '',
      flowId: '',
      utensilCounterfeitList: [],
      fileJson: '',
    },
    dataRule: {
      creatorUserId: [{ required: true, message: '申请人不能为空', trigger: 'change' }],
      // creatTime: [{required: true, message: '申请日期不能为空', trigger: 'change'}],
    },
    billEnCode: 'OrderNumber',
    fileList: [],
    activeKey: '1',
  });
  const { dataForm, dataRule, activeKey } = toRefs(state);
  const { init, judgeShow, judgeWrite, judgeRequired, dataFormSubmit } = useFlowForm({
    config: props.config,
    selfState: state,
    emit,
    formRef,
    selfGetInfo: (formInfo:any) => { // 自定义获取详情方法，可以做数据转换
      state.dataForm.utensilCounterfeitList = (formInfo.utensilCounterfeitList || []).map(i => ({
        ...i,
        verificationDate: parseDate(i.verificationDate + ' 00:00:00').unix()*1000, // 表格字段组件使用的是JnpfDatePicker，传入的字段需要是时间戳
        admissionDate: parseDate(i.admissionDate + ' 00:00:00').unix()*1000,
      }))
    },
  });

  defineExpose({ dataFormSubmit });
  const noColumn = { width: 50, title: '序号', dataIndex: 'index', key: 'index', align: 'center', customRender: ({ index }) => index + 1 };

  const getGoodsColumns = computed(() => {
    const actionColumn =
      !props.config.disabled && !judgeWrite('utensilCounterfeitList') ? { title: '操作', dataIndex: 'action', key: 'action', width: 50 } : null;
    const column = defaultGoodsColumns.filter(o => judgeShow(`utensilCounterfeitList-${o.key}`));
    const list = [noColumn, ...column];
    if (actionColumn) list.push(actionColumn);
    return list;
  });

  watch(
    () => state.dataForm.utensilCounterfeitList,
    val => {
      let money = 0;
      for (let i = 0; i < val.length; i++) {
        const e = val[i];
        money += parseFloat(e.actualAmount);
      }
      state.dataForm.receivableMoney = money.toFixed(2);
    },
    { deep: true },
  );

  function handleDel(index, id) {
    if (id === undefined || id === null || id === '') {
      console.log('ID为空', id);
      state.dataForm.utensilCounterfeitList.splice(index, 1);
    } else {
      MachineCounterfeitApi.deleteById(id).then(res => {
        // 打印返回数据
        console.log('res', res);
      });
      console.log('ID不为空', id);
      state.dataForm.utensilCounterfeitList.splice(index, 1);
    }
  }

  function onLedgerSelect(list) {
    console.log('onLedgerList:', list);
    for (let i = 0; i < list.length; i++) {
      const e = list[i];
      let item = {
        utensilName: e.utensilName,
        utensilNo: e.utensilNo,
        elaborate: e.elaborate,
        specificationsModels: e.specificationsModels,
        verificationDate: e.verificationDate,
        remark: e.remark,
      };
      state.dataForm.utensilCounterfeitList.push(item);
    }
  }

  function handleOpenImport() {
    openImportModal(true, {});
  }

  /**
   * 预览确认后回调
   */
  function handleImport(list) {
    console.log('list', list);
    list.forEach(function (item) {
      console.log('item:', item);
      state.dataForm.utensilCounterfeitList.push(item);
    });
  }

  onMounted(() => {
    init();
  });
</script>
<style lang="less" scoped>
  .ant-tabs {
    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
    }
  }
</style>
