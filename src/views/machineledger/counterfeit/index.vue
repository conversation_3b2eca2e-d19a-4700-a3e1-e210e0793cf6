<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">新建入场检查及防造假核查单</a-button>
          </template>

          <template #expandedRowRender="{ record }">
            <a-tabs size="small" v-loading="record.childTableLoading">
              <a-tab-pane key="1" tab="入场检查及防造假核查列表">
                <BasicTable @register="registerGoodsTable" :data-source="record.goodsList"> </BasicTable>
              </a-tab-pane>
            </a-tabs>
          </template>

          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'selectPerNames'">
              <span>{{ record.selectPerNames.join(',') }}</span>
            </template>
            <template v-if="column.key === 'state'">
              <FaFlowStatus :status="record.state" />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
          
        </BasicTable>
      </div>
    </div>

    <FaFlowCube ref="flowRef" flow-en-code="machineCounterfeitRequest" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import * as MachineCounterfeitApi from '/@/api/flowApi/machineCounterfeit';
  // import { getFlowIdByCode, getFlowList } from '/@/api/workFlow/flowEngine';
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  // import { usePopup } from '/@/components/Popup';
  // import { BasicModal, useModal } from '/@/components/Modal';
  import FaFlowCube from '/@/components/Fa/common/src/FaFlowCube.vue';
  import { genFlowDetailBtn, genFlowDeleteBtn, genFlowEditBtn, genQueryInput } from '/@/utils/tableUtils';
  import { FLOW_OP_TYPE } from "/@/enums/zzEnums";

  defineOptions({ name: 'ext-machine-counterfeit' });

  const flowRef = ref<any>();

  const { createMessage } = useMessage();
  const { t } = useI18n();
  // const [registerFlowParser, { openPopup: openFlowParser }] = usePopup();
  // const [registerFlowListModal, { openModal: openFlowListModal, closeModal: closeFlowListModal }] = useModal();
  // const flowTemplateId = ref('');
  // const flowList = ref<any[]>([]);
  const columns: BasicColumn[] = [
    { title: '申请人', dataIndex: 'creatorUserId', width: 150 },
    { title: '流水单号', dataIndex: 'invoiceNumber', width: 150 },
    { title: '创建时间', dataIndex: 'creatorTime', width: 170, format: 'date|YYYY-MM-DD HH:mm:ss' },
    { title: '状态', dataIndex: 'state', width: 120, align: 'center' },
  ];
  const goodsColumns: BasicColumn[] = [
    { title: '器具名称', dataIndex: 'utensilName' },
    { title: '器具编号', dataIndex: 'utensilNo' },
    { title: '精度', dataIndex: 'elaborate' },
    { title: '规格型号', dataIndex: 'specificationsModels' },
    { title: '检定日期', dataIndex: 'verificationDate', width: 120, format: 'date|YYYY-MM-DD' },
    { title: '入场时间', dataIndex: 'admissionDate', width: 120, format: 'date|YYYY-MM-DD' },
    { title: '单位资质查验', dataIndex: 'unitQualification' },
    { title: '证书真伪查验', dataIndex: 'certificateAuthenticity' },
    { title: '合格状态', dataIndex: 'qualificationState' },
    { title: '备注', dataIndex: 'remark' },
  ];

  const [registerTable, { reload }] = useTable({
    api: MachineCounterfeitApi.getRequestList,
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'keyword',
          label: t('common.keyword'),
          component: 'Input',
          componentProps: { placeholder: t('common.enterKeyword'), submitOnPressEnter: true },
        },
        // {
        //   field: 'pickerVal',
        //   label: '日期',
        //   component: 'DateRange',
        // },
      ],
      fieldMapToTime: [['pickerVal', ['verificationDate', 'admissionDate']]],
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
    onExpand: handleExpand,
  });
  const [registerGoodsTable] = useTable({
    columns: goodsColumns,
    pagination: false,
    showTableSetting: false,
    canResize: false,
    scroll: { x: undefined },
  });

  function getTableActions(record): ActionItem[] {
    return [genFlowEditBtn(record, toDetail), genFlowDeleteBtn(record, handleDelete), genFlowDetailBtn(record, toDetail)];
  }

  function handleAdd() {
    flowRef.value.handleAdd();
  }

  function toDetail(record: any, opType: FLOW_OP_TYPE) {
    flowRef.value.toDetail(record, opType);
  }

  function handleDelete(id: string) {
    MachineCounterfeitApi.delAdmission(id).then(res => {
      createMessage.success(res.msg);
      reload();
    });
  }

  // function handleDelete(id: string) {
  //   MachineCounterfeitApi.delAdmission(id).then(res => {
  //     createMessage.success(res.msg);
  //     reload();
  //   });
  // }
  // function toDetail(record: any, opType: FLOW_OP_TYPE) {
  //   const data = {
  //     id: record.id,
  //     flowId: record.flowId,
  //     opType,
  //     status: record.state,
  //   };
  //   openFlowParser(true, data);
  // }
  function handleExpand(expanded, record) {
    if (!expanded || record.goodsList?.length || record.planList?.length) return;
    record.childTableLoading = true;
    console.log(record);
    MachineCounterfeitApi.getCounterfeitList(record.id)
      .then(res => {
        record.childTableLoading = false;
        record.goodsList = res.data.list;
      })
      .catch(() => {
        record.childTableLoading = false;
      });
  }
  // function getFlowTemplateId() {
  //   getFlowIdByCode('machineCounterfeitRequest').then(res => {
  //     flowTemplateId.value = res.data;
  //     getFlowList(flowTemplateId.value, '1').then(res => {
  //       flowList.value = res.data;
  //     });
  //   });
  // }
  // function handleAdd() {
  //   if (!flowList.value.length) return createMessage.error('流程不存在');
  //   if (flowList.value.length === 1) return selectFlow(flowList.value[0]);
  //   openFlowListModal(true);
  // }
  // function selectFlow(record) {
  //   closeFlowListModal();
  //   const data = {
  //     id: '',
  //     flowId: record.id,
  //     opType: '-1',
  //   };
  //   openFlowParser(true, data);
  // }

  // onMounted(() => {
  //   getFlowTemplateId();
  // });
</script>
