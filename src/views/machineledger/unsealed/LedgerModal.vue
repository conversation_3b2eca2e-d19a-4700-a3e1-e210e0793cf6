<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="选择器具" @ok="handleSubmit" :width="800" class="jnpf-list-modal">
    <BasicTable @register="registerTable" class="jnpf-sub-table"></BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { nextTick } from 'vue';
  import * as MachineCounterfeitApi from '/@/api/flowApi/machineCounterfeit';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, useTable } from '/@/components/Table';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { genQueryInput } from '/@/utils/tableUtils';

  const emit = defineEmits(['register', 'select']);
  const { t } = useI18n();
  const [registerModal, { closeModal }] = useModalInner(init);

  const [registerTable, { getForm, getSelectRows, clearSelectedRowKeys }] = useTable({
    api: MachineCounterfeitApi.page,
    columns: [
      { title: '器具名称', dataIndex: 'utensilName' },
      { title: '器具编号', dataIndex: 'utensilNo' },
      { title: '分级', dataIndex: 'classification' },
      { title: '制作厂（商）', dataIndex: 'fabricationPlant' },
      { title: '供应商', dataIndex: 'suppliers' },
      { title: '精度', dataIndex: 'elaborate' },
      { title: '规格型号', dataIndex: 'specificationsModels' },
      { title: '检定日期', dataIndex: 'verificationDate', width: 100, format: 'date|YYYY-MM-DD' },
      { title: '检定周期', dataIndex: 'verificationCycle', width: 100, format: 'date|YYYY-MM-DD' },
      { title: '有效期', dataIndex: 'effectiveDate', width: 100, format: 'date|YYYY-MM-DD' },
      { title: '到期天数', dataIndex: 'expireDate' },
      { title: '检定单位', dataIndex: 'verificationUnit' },
      { title: '状态', dataIndex: 'state' },
      { title: '领用部门', dataIndex: 'acceptanceDept' },
      { title: '领用人', dataIndex: 'acceptanceUser' },
      { title: '检定员', dataIndex: 'inspector' },
      { title: '证书编号', dataIndex: 'certificateNo' },
      { title: '领料单小票号', dataIndex: 'pickingReceiptNumber' },
      { title: '领用时间', dataIndex: 'acceptanceDate', width: 100, format: 'date|YYYY-MM-DD' },
      { title: '遗失，报废单编号', dataIndex: 'lostScrapOrder' },
      { title: '失效日期', dataIndex: 'expirationDate', width: 100, format: 'date|YYYY-MM-DD' },
      { title: '撤场日期', dataIndex: 'withdrawalDate', width: 100, format: 'date|YYYY-MM-DD' },
      { title: '需求计划号', dataIndex: 'demandPlanNumber' },
      { title: '备注', dataIndex: 'remark' },
    ],
    useSearchForm: true,
    formConfig: {
      baseColProps: { span: 8 },
      schemas: [
        {
          field: 'keyword',
          label: t('common.keyword'),
          component: 'Input',
          componentProps: {
            placeholder: t('common.enterKeyword'),
            submitOnPressEnter: true,
          },
        },
        genQueryInput('器具名称', 'utensilName'),
        genQueryInput('器具编号', 'utensilNo'),
        genQueryInput('状态', 'state'),
      ],
    },
    tableSetting: { size: true, setting: false },
    isCanResizeParent: true,
    resizeHeightOffset: -63,
    immediate: false,
    rowSelection: { type: 'checkbox' },
  });
  function init() {
    getForm().resetFields();
    nextTick(() => {
      clearSelectedRowKeys();
    });
  }
  function handleSubmit() {
    const selectedData = getSelectRows();
    emit('select', selectedData);
    closeModal();
  }
</script>
