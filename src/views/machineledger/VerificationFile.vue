<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @ok="handleSubmit" title="校准证件">
    <a-row class="dynamic-form" style="min-height: 120px">
      <a-form
        :colon="false"
        size="default"
        layout="horizontal"
        labelAlign="right"
        :labelCol="{ style: { width: '0px' } }"
        :model="dataForm"
        :rules="dataRule"
        ref="formRef">
        <a-row :gutter="15">

          <a-col :span="24" class="ant-col-item">
            <a-form-item label="校准证件">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.verificationCertificate" />
            </a-form-item>
          </a-col>
          
          <!-- 表单结束 -->
        </a-row>
      </a-form>
    </a-row>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, toRefs } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { getFile, uploadSubmitFile } from '/@/api/flowApi/machineLedger';
import JnpfFaUploadFileQiniu from '/@/components/Jnpf/Upload/src/FaUploadFileQiniu.vue';
import { useMessage } from "/@/hooks/web/useMessage";
import { each } from "lodash-es";


const emit = defineEmits(['register', 'reload']);
const [registerModal, {changeLoading, closeModal, changeOkLoading}] = useModalInner(init);
const {createMessage} = useMessage();

interface State {
  dataForm: {
    verificationCertificate?: string,
  };
  dataRule: any;
}

const state = reactive<State>({
  dataForm: {
    verificationCertificate: undefined,
  },
  dataRule: {},
});

const {dataRule, dataForm} = toRefs(state);

function init(id) {
  // 打印外部传给modal的参数
  console.log('init', id);
  state.dataForm = {
    verificationCertificate: undefined,
  }
  changeLoading(true);
  getFile(id).then(res => {
    // 打印返回数据
    console.log('res', res);
    state.dataForm = res.data;
    changeLoading(false);
  }).catch(() => changeLoading(false))
}



async function handleSubmit() {
  // 打印提交信息
  const params = {
    id: state.dataForm.id,
    fileIdsList: state.dataForm,
    verificationCertificate: state.dataForm.verificationCertificate,
  }

  changeOkLoading(true);
  
  uploadSubmitFile(params).then(res => {
    // 打印返回数据
    console.log('res', res);
    createMessage.success("不可进行修改")
    changeOkLoading(false);
    closeModal();
    emit('reload'); // 发布reload事件，外部组件接受此事件
  }).catch(() => changeOkLoading(false))
}
</script>
