const columnList = [{"clearable":true,"maxlength":null,"jnpfKey":"input","suffixIcon":"","fullName":"姓名","label":"姓名","sortable":true,"align":"left","addonAfter":"","__config__":{"formId":101,"visibility":["pc","app"],"jnpfKey":"input","noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"姓名","trigger":"blur","showLabel":true,"required":false,"tableName":"study_training_info","renderKey":1711587581250,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-input","tag":"JnpfInput","regList":[],"span":24},"readonly":false,"prop":"name","width":null,"__vModel__":"name","showPassword":false,"fixed":"none","style":{"width":"100%"},"disabled":false,"id":"name","placeholder":"请输入","prefixIcon":"","addonBefore":"","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}","blur":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"clearable":true,"maxlength":null,"jnpfKey":"input","suffixIcon":"","fullName":"身份证","label":"身份证","sortable":true,"align":"left","addonAfter":"","__config__":{"formId":102,"visibility":["pc","app"],"jnpfKey":"input","noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"身份证","trigger":"blur","showLabel":true,"required":false,"tableName":"study_training_info","renderKey":1711587585582,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-input","tag":"JnpfInput","regList":[],"span":24},"readonly":false,"prop":"idCard","width":null,"__vModel__":"idCard","showPassword":false,"fixed":"none","style":{"width":"100%"},"disabled":false,"id":"idCard","placeholder":"请输入","prefixIcon":"","addonBefore":"","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}","blur":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"clearable":true,"maxlength":null,"jnpfKey":"input","suffixIcon":"","fullName":"培训名称","label":"培训名称","sortable":true,"align":"left","addonAfter":"","__config__":{"formId":103,"visibility":["pc","app"],"jnpfKey":"input","noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"培训名称","trigger":"blur","showLabel":true,"required":false,"tableName":"study_training_info","renderKey":1711587589896,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-input","tag":"JnpfInput","regList":[],"span":24},"readonly":false,"prop":"trainingName","width":null,"__vModel__":"trainingName","showPassword":false,"fixed":"none","style":{"width":"100%"},"disabled":false,"id":"trainingName","placeholder":"请输入","prefixIcon":"","addonBefore":"","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}","blur":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"clearable":true,"jnpfKey":"datePicker","format":"yyyy-MM-dd HH:mm:ss","fullName":"培训时间","label":"培训时间","sortable":true,"align":"left","__config__":{"endRelationField":"","defaultValue":null,"dragDisabled":false,"className":[],"showLabel":true,"required":false,"tableName":"study_training_info","renderKey":1711587595939,"tagIcon":"icon-ym icon-ym-generator-date","startRelationField":"","defaultCurrent":false,"tag":"JnpfDatePicker","formId":104,"visibility":["pc","app"],"jnpfKey":"datePicker","noShow":false,"endTimeTarget":1,"tipLabel":"","startTimeType":1,"endTimeRule":false,"label":"培训时间","trigger":"change","startTimeRule":false,"startTimeValue":null,"endTimeValue":null,"endTimeType":1,"layout":"colFormItem","startTimeTarget":1,"regList":[],"span":24},"prop":"trainingTime","width":null,"__vModel__":"trainingTime","fixed":"none","style":{"width":"100%"},"startTime":null,"disabled":false,"id":"trainingTime","placeholder":"请选择","endTime":null,"on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}","blur":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"clearable":true,"maxlength":null,"jnpfKey":"input","suffixIcon":"","fullName":"培训成绩","label":"培训成绩","sortable":true,"align":"left","addonAfter":"","__config__":{"formId":105,"visibility":["pc","app"],"jnpfKey":"input","noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"培训成绩","trigger":"blur","showLabel":true,"required":false,"tableName":"study_training_info","renderKey":1711587606781,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-input","tag":"JnpfInput","regList":[],"span":24},"readonly":false,"prop":"trainingScores","width":null,"__vModel__":"trainingScores","showPassword":false,"fixed":"none","style":{"width":"100%"},"disabled":false,"id":"trainingScores","placeholder":"请输入","prefixIcon":"","addonBefore":"","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}","blur":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"clearable":true,"maxlength":null,"jnpfKey":"input","suffixIcon":"","fullName":"培训地点","label":"培训地点","sortable":true,"align":"left","addonAfter":"","__config__":{"formId":106,"visibility":["pc","app"],"jnpfKey":"input","noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"培训地点","trigger":"blur","showLabel":true,"required":false,"tableName":"study_training_info","renderKey":1711587615338,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-input","tag":"JnpfInput","regList":[],"span":24},"readonly":false,"prop":"trainingPlace","width":null,"__vModel__":"trainingPlace","showPassword":false,"fixed":"none","style":{"width":"100%"},"disabled":false,"id":"trainingPlace","placeholder":"请输入","prefixIcon":"","addonBefore":"","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}","blur":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"clearable":true,"maxlength":null,"jnpfKey":"input","suffixIcon":"","fullName":"部门","label":"部门","sortable":true,"align":"left","addonAfter":"","__config__":{"formId":107,"visibility":["pc","app"],"jnpfKey":"input","noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"部门","trigger":"blur","showLabel":true,"required":false,"tableName":"study_training_info","renderKey":1711587620362,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-input","tag":"JnpfInput","regList":[],"span":24},"readonly":false,"prop":"dept","width":null,"__vModel__":"dept","showPassword":false,"fixed":"none","style":{"width":"100%"},"disabled":false,"id":"dept","placeholder":"请输入","prefixIcon":"","addonBefore":"","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}","blur":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}},{"clearable":true,"maxlength":null,"jnpfKey":"input","suffixIcon":"","fullName":"培训讲师","label":"培训讲师","sortable":true,"align":"left","addonAfter":"","__config__":{"formId":108,"visibility":["pc","app"],"jnpfKey":"input","noShow":false,"tipLabel":"","dragDisabled":false,"className":[],"label":"培训讲师","trigger":"blur","showLabel":true,"required":false,"tableName":"study_training_info","renderKey":1711587625592,"layout":"colFormItem","tagIcon":"icon-ym icon-ym-generator-input","tag":"JnpfInput","regList":[],"span":24},"readonly":false,"prop":"trainingLecturer","width":null,"__vModel__":"trainingLecturer","showPassword":false,"fixed":"none","style":{"width":"100%"},"disabled":false,"id":"trainingLecturer","placeholder":"请输入","prefixIcon":"","addonBefore":"","on":{"change":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}","blur":"({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, onlineUtils }) => {\n    // 在此编写代码\n    \n}"}}]
export default columnList