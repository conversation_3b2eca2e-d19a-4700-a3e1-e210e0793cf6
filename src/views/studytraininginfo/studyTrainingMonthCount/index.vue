<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center bg-white p-10px">
      <Chart :options="options" class="mt-30px" height="500px" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { getCurrentMonthCount } from '/@/api/basic/user';
  import { reactive, onMounted, ref } from 'vue';
  import { Chart } from '/@/components/Chart/index';

  defineOptions({ name: 'extend-graphDemo-echartsBar' });

  const options = ref({
    title: {
      text: '本月培训完成情况（蓝色完成次数，黄色未完成次数）',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
      },
      {
        data: [],
        type: 'bar',
      },
    ],
  });

  // 生命周期钩子：组件挂载后调用 API
  onMounted(async () => {
    try {
      // 替换为您的后台API端点，获取数据
      const response = await getCurrentMonthCount();
      const departments = response.data;

      // 赋值
      let trainingNameArr = departments.map(item => item.trainingName);
      let finishCountArr = departments.map(item => item.finishCount);
      let noFinishCountArr = departments.map(item => item.noFinishCount);
      options.value.xAxis.data = trainingNameArr;
      options.value.series[0].data = finishCountArr;
      options.value.series[1].data = noFinishCountArr;
    } catch (error) {
      console.error('Error fetching department data:', error);
      // 在这里您可以处理错误，比如显示一个错误消息给用户
    }
  });
</script>
