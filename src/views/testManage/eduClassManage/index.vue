<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button @click="addOrUpdateHandle()" type="primary" preIcon="icon-ym icon-ym-btn-add">{{ t('common.addText') }}</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>`
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload"/>
  </div>
</template>
<script lang="ts" setup>
import { BasicTable, useTable, BasicColumn, FormProps, TableAction, ActionItem } from '/@/components/Table';
import { testEduClassTreeApi } from '/@/api';
import { useMessage } from "/@/hooks/web/useMessage";
import { useI18n } from "/@/hooks/web/useI18n";
import { genInput } from "/@/utils/formUtils";
import Form from "./Form.vue";
import { useModal } from "/@/components/Modal";
import {genDeleteBtn} from "/@/utils/tableUtils";
import {useUserStore} from "/@/store/modules/user";

defineOptions({ name: 'testManage-eduClassManage' });

const {t} = useI18n();
const {createMessage} = useMessage();

const [registerForm, {openModal: openFormModal}] = useModal();

const columns: BasicColumn[] = [
  // { title: '编码', dataIndex: 'id', width: 400 },
  { title: '名称', dataIndex: 'name', width: undefined },
  // { title: '编号', dataIndex: 'sourceData.classNum',key: 'sourceData.classNum', width: undefined },
];
const [registerTable, { reload }] = useTable({
  api: testEduClassTreeApi.allTreeForTable,
  columns,
  useSearchForm: true,
  isTreeTable: true,
  defaultExpandAllRows: false,
  pagination: false,
  formConfig: getFormConfig(),
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
  },
});

function getFormConfig(): Partial<FormProps> {
  return {
    schemas: [
      genInput('关键词', 'keyword', false, true),
      genInput('名称', 'name', false, true),
    ],
  };
}

function getTableActions(record): ActionItem[] {
  const userInfo = useUserStore().getUserInfo;

  let arr =  [
    {
      icon: 'icon-ym icon-ym-btn-upload',
      // label: '上移',
      onClick: moveUpHandle.bind(null, record.id),
    },
    {
      icon: 'icon-ym icon-ym-btn-download',
      // label: '下移',
      onClick: moveDownHandle.bind(null, record.id),
    },
    {
      label: '添加下级',
      onClick: addOrUpdateHandle.bind(null, null, record.id),
    },
    {
      label: t('common.editText'),
      onClick: addOrUpdateHandle.bind(null, record.id, record.parentId),
    },
  ]

  if (record.creatorUserId === userInfo.userId || userInfo.isAdministrator) {
    arr.push(
      {
        label: t('common.delText'),
        color: "error",
        modelConfirm: {
          onOk: handleDelete.bind(null, record.id),
        },
      }
    )
  }

  return arr;
}

function addOrUpdateHandle(id='', parentId='') {
  openFormModal(true, {id, parentId});
}

function handleDelete(id:any) {
  testEduClassTreeApi.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function moveUpHandle(id:any) {
  testEduClassTreeApi.moveUp(id).then(res => {
    createMessage.success(res.msg);
    reload();
  })
}

function moveDownHandle(id:any) {
  testEduClassTreeApi.moveDown(id).then(res => {
    createMessage.success(res.msg);
    reload();
  })
}
</script>
