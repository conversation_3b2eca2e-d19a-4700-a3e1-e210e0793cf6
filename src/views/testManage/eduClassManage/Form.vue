<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit" :destroyOnClose="true" :width="800" :height="400">
    <BasicForm @register="registerForm"/>
  </BasicModal>
</template>
<script lang="ts" setup>
import {computed, ref, unref} from 'vue';
import {BasicModal, useModalInner} from '/@/components/Modal';
import {BasicForm, useForm} from '/@/components/Form';
import {useMessage} from '/@/hooks/web/useMessage';
import {genCommon, genInput, genInputNumber} from "/@/utils/formUtils";
import {testEduClassTreeApi} from '/@/api';


const emit = defineEmits(['register', 'reload']);
const [registerForm, {setFieldsValue, resetFields, validate}] = useForm({
  schemas: [
    {
      ...genCommon('上级分类', 'parentId', 'FaCascader'),
      componentProps: {api: testEduClassTreeApi, showRoot: true},
    },
    genInput('分类名称', 'className'),
    genInput('分类编号', 'classNum'),
    genInputNumber('排序', 'sort'),
  ],
});
const [registerModal, {closeModal, changeLoading, changeOkLoading}] = useModalInner(init);
const id = ref('');
const {createMessage} = useMessage();

const getTitle = computed(() => (!unref(id) ? '新建分类' : '编辑分类'));

function init(data) {
  console.log('eduClassManage', data)
  resetFields();
  id.value = data.id;
  if (id.value) {
    changeLoading(true);
    testEduClassTreeApi.getById(id.value).then(res => {
      const data = {
        ...res.data,
      };
      setFieldsValue(data);
      changeLoading(false);
    });
  } else {
    setFieldsValue({parentId: data.parentId})
  }
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const query = {
    ...values,
    id: id.value,
  };
  const formMethod = id.value ? testEduClassTreeApi.update : testEduClassTreeApi.save;
  formMethod(query)
    .then(res => {
      createMessage.success(res.msg);
      changeOkLoading(false);
      closeModal();
      setTimeout(() => {
        emit('reload');
      }, 300);
    })
    .catch(() => {
      changeOkLoading(false);
    });
}
</script>
