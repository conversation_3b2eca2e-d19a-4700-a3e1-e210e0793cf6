<template>
  <!-- 每列前面的两个输入 勾选以及单题分值 -->
  <BasicModal v-bind="$attrs" @register="registerModal" @ok="handleSubmit" title="试题选择" :footer="null" @close="handleClose" defaultFullscreen class="jnpf-full-modal full-modal">
    <a-space class="fa-mb-12">
      <div>选择试题：已选择 <span style="color: #dd0000; font-weight: bold;">{{ selList.length }}</span> 道试题</div>
      <a-button @click="handleSubmit" type="primary">确定</a-button>
    </a-space>
    <BasicTable @register="registerTable">
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, unref } from "vue";
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicColumn, BasicTable, useTable } from '/@/components/Table';
import { testEduQuestionApi } from "/@/api";
import { genQueryInput } from "/@/utils/tableUtils";
import { trim } from "lodash-es";
import { QUESTION_TYPE_MAP } from "/@/enums/zzEnums";

const emit = defineEmits(['register', 'selectQuestion']); // 需要暴露的事件
const [registerModal, {closeModal}] = useModalInner(init);

const selList = ref<any[]>([]); // 选中的试题列表
const searchInfo = reactive({
  "questionBankId#$in": [], // 试题库id
  questionType: undefined, //
})

function init(data: any) {
  searchInfo["questionBankId#$in"] = data.questionBankIds || []
  searchInfo.questionType = data.questionType
  reload()
}

const columns: BasicColumn[] = [
  {title: '所属题库(教材)', dataIndex: 'questionBank', width: 200},
  {title: '试题名称', dataIndex: 'questionContent'},
  {
    title: '题目选项', dataIndex: 'questionChoices',
    customRender: ({record}) => {
      if (trim(record.questionChoices) === '') return '';
      try {
        const array = JSON.parse(record.questionChoices);
        const str = array.map(i => `${i.value}: ${i.option}`).join(' | ')
        return str
      } catch (e) {
      }
      return '';
    },
  },
  {
    title: '题目答案', dataIndex: 'questionAnswer', width: 100,
  },
  {
    title: '题目类型', dataIndex: 'questionType', width: 100,
    customRender: ({record}) => (QUESTION_TYPE_MAP[record.questionType])
  },
];
const [registerTable, { reload }] = useTable({
  api: testEduQuestionApi.page,
  columns,
  searchInfo,
  immediate: false,
  useSearchForm: true,
  clickToRowSelect: true, // 点击选中条目
  rowSelection: { // 选中行
    onChange: (_selectedRowKeys, selectedRows) => { // 选中行change
      selList.value = selectedRows;
    },
  },
  formConfig: {
    schemas: [
      genQueryInput('试题名称', 'questionContent'),
    ],
  },
  // actionColumn: {
  //   width: 100,
  //   title: '操作',
  //   dataIndex: 'action',
  // },
});

function handleClose() {
  closeModal();
}

async function handleSubmit() {
  emit('selectQuestion', {questions: unref(selList.value)}); // 发布reload事件，外部组件接受此事件
  closeModal();
}
</script>
