<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-left" style="width: 250px;">
      <BasicLeftTree
        title="培训分类"
        ref="leftTreeRef"
        :treeData="treeData"
        :loading="treeLoading"
        :fieldNames="{ key: 'id', title: 'name' }"
        @reload="reloadTree"
        @select="handleTreeSelect"
        showToolbar/>
    </div>

    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">新增</a-button>
            <a-button type="link" @click="handleImport"><i class="icon-ym icon-ym-btn-upload button-preIcon" />{{ t('common.importText') }}</a-button>
            <a-button type="link" @click="handleExport"><i class="icon-ym icon-ym-btn-download button-preIcon"></i>{{ t('common.exportText') }}</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handleBatchDelete">批量删除</a-button>
            <a-button type="primary" preIcon="icon-ym" @click="syncUserQuestionAnalysis()">同步问题解析</a-button>
            <a-button type="primary" preIcon="icon-ym" @click="syncEduType()">同步培训类型</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)"/>
            </template>
          </template>
        </BasicTable>
      </div>
      <Form @register="registerForm" @reload="reload"/>
      <ImportModal @register="registerImportModal" @reload="reload"/>

    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref, toRefs, unref} from "vue";
import {BasicLeftTree, TreeActionType} from "/@/components/Tree";
import {ActionItem, BasicColumn, BasicTable, TableAction, useTable} from "/@/components/Table";
import {useI18n} from "/@/hooks/web/useI18n";
import {useMessage} from "/@/hooks/web/useMessage";
import Form from "./Form.vue";

import {testEduClassTreeApi, testEduFileApi, testEduQuestionApi, testExamRecordApi} from "/@/api";
import {genQuerySearch, genQuerySelect} from "/@/utils/tableUtils";
import {useModal} from "/@/components/Modal";
import {QUESTION_STATUS_MAP, QUESTION_TYPE_MAP, QUESTION_TYPE_OPTIONS} from "/@/enums/zzEnums";
import {trim} from "lodash-es";
import ImportModal from "/@/views/testManage/eduFileManage/ImportModal.vue";

defineOptions({name: 'testManage-questionManage'});

interface State {
  treeData: any[];
  treeLoading: boolean;
}

const state = reactive<State>({
  treeData: [],
  treeLoading: false,
});
const {treeData, treeLoading} = toRefs(state);
const leftTreeRef = ref<Nullable<TreeActionType>>(null);

async function init() {
  setLoading(true);
  await reloadTree();
  searchEduTypeId = state.treeData[0]?.id;
  const leftTree = unref(leftTreeRef);
  leftTree?.setSelectedKeys([searchEduTypeId.value]);
  await updateQuestionBankList(searchEduTypeId);
  searchEduTypeId && reload();
}

// ----------------------------------- left tree -----------------------------------
/** 重新加载Tree数据 */
async function reloadTree() {
  state.treeLoading = true;
  const ret = await testEduClassTreeApi.allTree();
  state.treeData = [
    {id: '', name: '全部'},
    ...ret.data,
  ];
  state.treeLoading = false
}

/** Tree节点点击 */
async function handleTreeSelect(id: any) {
  console.log('handleTreeSelect', id)
  // if (!id || searchEduTypeId === id) return;
  searchEduTypeId = id;

  //设置课件搜索框
  await updateQuestionBankList(id);
}

/** 设置课件搜索框 */
async function updateQuestionBankList(eduTypeId: any) {
  searchInfo.eduTypeId = eduTypeId

  const res = await testEduFileApi.page({eduTypeId});
  if(!res || !res.data || !res.data.list) return

  const options = res.data.list.map(i => ({
    id: i.id,
    fullName: i.fileName,
  }))

  console.log('options', options)
  getForm().updateSchema({field: 'questionBankId', componentProps: {options}});
  getForm().resetFields();
}

// ----------------------------------- right table -----------------------------------
const {t} = useI18n();
const {createMessage, createConfirm} = useMessage();
const [registerForm, {openModal: openFormModal}] = useModal();
const [registerImportModal, {openModal: openImportModal}] = useModal();


const columns: BasicColumn[] = [
  {title: '所属题库(教材)', dataIndex: 'questionBank', width: 200},
  {title: '培训分类', dataIndex:'eduTypeName'},
  {title: '题目内容', dataIndex: 'questionContent'},
  {
    title: '题目选项', dataIndex: 'questionChoices',
    customRender: ({record}) => {
      if (trim(record.questionChoices) === '') return '';
      try {
        const array = JSON.parse(record.questionChoices);
        const str = array.map(i => `${i.value}: ${i.option}`).join(' | ')
        return str
      } catch (e) {
      }
      return '';
    },
  },
  {
    title: '题目答案', dataIndex: 'questionAnswer', width: 100,
  },
  {
    title: '题目类型', dataIndex: 'questionType', width: 100,
    customRender: ({record}) => (QUESTION_TYPE_MAP[record.questionType])
  },
  {
    title: '题目状态', dataIndex: 'questionStatus', width: 100,
    customRender: ({record}) => (QUESTION_STATUS_MAP[record.questionStatus])
  },
  // {title: '创建者', dataIndex: 'creatorUserId'},
  {title: '创建日期', dataIndex: 'creatorTime', width: 100, format: 'date|YYYY-MM-DD'},
];
const searchInfo = reactive({
  questionBankId: undefined, // 右table所属左treeId
  eduTypeId : undefined,
});

let searchEduTypeId = ref('');
const selList = ref<any[]>([]); // 选中的试题列表

const [registerTable, {reload, setLoading, getForm}] = useTable({
  api: testEduQuestionApi.page,
  columns,
  searchInfo,
  useSearchForm: true,
  immediate: false,
  ellipsis: false,
  clickToRowSelect: true, // 点击选中条目
  rowSelection: { // 选中行
    onChange: (selectedRowKeys, selectedRows) => { // 选中行change
      console.log('selectedRowKeys', selectedRowKeys)
      selList.value = selectedRowKeys;
    },
  },
  formConfig: {
    schemas: [
      genQuerySelect('课件', 'questionBankId'),
      genQuerySelect("题目类型", "questionType", QUESTION_TYPE_OPTIONS),
      genQuerySearch(),
    ],
  },
  actionColumn: {
    width: 100,
    title: '操作',
    dataIndex: 'action',
  },
});


function getTableActions(record): ActionItem[] {
  return [
    {
      label: t('common.editText'),
      onClick: addOrUpdateHandle.bind(null, record.id),
    },
    {
      label: t('common.delText'),
      color: 'error',
      modelConfirm: {
        onOk: handleDelete.bind(null, record.id),
      },
    },
  ];
}


function handleDelete(id) {
  testEduQuestionApi.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function addOrUpdateHandle(id = '') {

  let questionBankId = getForm().getFieldsValue().questionBankId;
  openFormModal(true, {id, questionBankId});
}

function handleExport() {
  setLoading(true);
  testEduQuestionApi.exportExcel({
    "id#$in": selList.value,
    _dataType: 0,
    _sorter: "f_question_type Asc"
  })
    .then(_res => {
      // setLoading(true);
      // if (!res.data.url) return;
      setLoading(false);
    })
    .catch(() => {
      setLoading(false);
    });
}

function handleBatchDelete() {
  createConfirm({
    iconType: 'warning',
    title: '删除',
    content: '是否确认删除所有选中试题？',
    onOk: () => {
      setLoading(true)
      testEduQuestionApi.removeBatchByIds(selList.value).then(_res => {
        setLoading(false);
        reload();
      }).catch(() => setLoading(false));
    },
  });
}

async function syncUserQuestionAnalysis(){
  let res = await testExamRecordApi.syncUserQuestionAnalysis()
  createMessage.info('后台用户考试记录问题解析已同步')
}

async function syncEduType(){
  let res = await testEduQuestionApi.syncEduType()
  createMessage.info('试题培训类型已同步')
}

async function handleImport() {
  openImportModal(true, {});
}

onMounted(() => init());
</script>

<style scoped>

</style>
