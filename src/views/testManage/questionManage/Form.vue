<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" showOkBtn @ok="handleSubmit" :width="800">
    <!--    <a-alert message="提醒info" type="warning" show-icon/>-->
    <BasicForm @register="registerForm" class="!px-10px !mt-10px" @field-value-change="handleFieldValueChange">
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
import {computed, nextTick, ref, unref} from 'vue';
import {BasicForm, FormSchema, HelpComponentProps, useForm} from '/@/components/Form';
import {useMessage} from '/@/hooks/web/useMessage';
import {testEduFileApi, testEduQuestionApi} from "/@/api";
import {BasicModal, useModalInner} from "/@/components/Modal";
import {genCommon, genInput, genSelect} from "/@/utils/formUtils";
import { QUESTION_TYPE, QUESTION_TYPE_OPTIONS } from "/@/enums/zzEnums";

const id = ref('');

const schemas: FormSchema[] = [
  {
    field: 'questionBankId',
    label: '所属题库(教材)',
    component: 'Select',
    // helpComponentProps:{maxWidth:'800'},
    componentProps: {placeholder: '请选择所属题库(教材)', showSearch: true},
    rules: [{required: true, trigger: 'change', message: '所属题库(教材)不能为空'}],
  },
  genSelect('题目类型', 'questionType', true,'string',QUESTION_TYPE_OPTIONS),
  genCommon('题目状态', 'questionStatus', 'Radio'),
  genInput('题目内容', 'questionContent'),
  {
    field: 'questionChoices',
    label: '题目选项',
    component: 'QuestionChoiceList',
    rules: [{ type: 'array' }],
    show: false,
  },
  genInput('题目答案', 'questionAnswer'),
  genInput('题目解析', 'questionAnalysis', false),
];
const getTitle = computed(() => (!unref(id) ? '新建试题' : '编辑试题'));
const emit = defineEmits(['register', 'reload']);
const {createMessage} = useMessage();
const [registerForm, {getFieldsValue, setFieldsValue, validate, resetFields, updateSchema}] = useForm({labelWidth: 130, schemas: schemas});
const [registerModal, {closeModal, changeLoading, changeOkLoading}] = useModalInner(init);

const questionBankMap = {};

async function init(data) {
  resetFields();
  id.value = data.id;
  await initOption();
  const questionBankId = data.questionBankId ? data.questionBankId : undefined;

  if (id.value) {
    changeLoading(true);
    await testEduQuestionApi.getById(id.value).then(res => {
      const dataDb = res.data
      const questionType = `${dataDb.questionType}`
      switch (questionType) {
        case QUESTION_TYPE.JUDGE: {
          updateSchema({ field: 'questionChoices', show: false })
          updateSchema({
            field: 'questionAnswer',
            component: 'Radio',
            componentProps: {
              options: [
                {"fullName": "√", "id": "√"},
                {"fullName": "×", "id": "×"}
              ]
            },
          })
        } break;
        case QUESTION_TYPE.SINGLE: {
          const options = JSON.parse(dataDb.questionChoices).map(i => ({"fullName": i.value, "id": i.value}))
          updateSchema({ field: 'questionChoices', show: true })
          updateSchema({
            field: 'questionAnswer',
            component: 'Radio',
            componentProps: { options },
            rules: [{required: true, type: 'string'}]
          })
        } break;
        case QUESTION_TYPE.MULTIPLY: {
          const options = JSON.parse(dataDb.questionChoices).map(i => ({"fullName": i.value, "id": i.value}))
          updateSchema({ field: 'questionChoices', show: true })
          updateSchema({
            field: 'questionAnswer',
            component: 'Checkbox',
            componentProps: { options },
            rules: [{required: true, type: 'array'}]
          })
        } break;
      }
      // nextTick set field 避免表单校验为空bug
      nextTick(() => {
        setFieldsValue({
          ...dataDb,
          questionChoices: dataDb.questionChoices ? JSON.parse(dataDb.questionChoices) : [],
          questionType: questionType,
          questionStatus: `${dataDb.questionStatus}`,
          questionAnswer: questionType === QUESTION_TYPE.MULTIPLY ? dataDb.questionAnswer.split('/') : dataDb.questionAnswer,
        })
        changeLoading(false);
      })
    });
  } else {
    setFieldsValue({
      questionBankId: questionBankId,
      questionStatus: '0',
    });
  }
}

async function initOption() {
  const res = await testEduFileApi.all();
  const questionBankOptions = res.data.map(i => {
    questionBankMap[i.id] = i.fileName;
    return {
      "id": i.id,
      "fullName": i.fileName,
    }
  });

  updateSchema({field: 'questionBankId', componentProps: {options: questionBankOptions}});

  const questionStatusOptions = [
    {"fullName": "可用", "id": "0", "enCode": "0"},
    {"fullName": "禁用", "id": "1", "enCode": "1"}
  ];
  updateSchema({field: 'questionStatus', componentProps: {options: questionStatusOptions}});
}

function handleFieldValueChange(field, value) {
  // console.log('handleFieldValueChange', field, value)
  if (field === 'questionType') {
    switch (value) {
      case QUESTION_TYPE.JUDGE: {
        setFieldsValue({ questionAnswer: undefined })
        updateSchema({ field: 'questionChoices', show: false })
        updateSchema({
          field: 'questionAnswer',
          component: 'Radio',
          componentProps: {
            options: [
              {"fullName": "√", "id": "√"},
              {"fullName": "×", "id": "×"}
            ]
          },
          rules: [{required: true, type: 'string'}]
        })
      } break;
      case QUESTION_TYPE.SINGLE: {
        updateSchema({ field: 'questionChoices', show: true })
        updateSchema({
          field: 'questionAnswer',
          component: 'Radio',
          componentProps: { options: getQuestionAnswer() },
          rules: [{required: true, type: 'string'}]
        })
      } break;
      case QUESTION_TYPE.MULTIPLY: {
        updateSchema({ field: 'questionChoices', show: true })
        updateSchema({
          field: 'questionAnswer',
          component: 'Checkbox',
          componentProps: { options: getQuestionAnswer() },
          rules: [{required: true, type: 'array'}]
        })
      } break;
    }
  }

  if (field === 'questionChoices') {
    updateSchema({
      field: 'questionAnswer',
      componentProps: { options: getQuestionAnswer() },
    })
  }
}

function getQuestionAnswer() {
  const choices = getFieldsValue().questionChoices || []
  return choices.map(i => ({"fullName": i.value, "id": i.value}))
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const query = {
    ...values,
    id: id.value,
    questionChoices: JSON.stringify(values.questionChoices),
    questionBank: questionBankMap[values.questionBankId],
    questionAnswer: values.questionType === QUESTION_TYPE.MULTIPLY ? values.questionAnswer.join("/") : values.questionAnswer,
  };

  const formMethod = id.value ? testEduQuestionApi.update : testEduQuestionApi.save;
  formMethod(query)
    .then(res => {
      createMessage.success(res.msg);
      changeOkLoading(false);
      closeModal();
      emit('reload');
    })
    .catch(() => changeOkLoading(false));
}
</script>

