<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="" showOkBtn @ok="handleSubmit" @close="handleClose">
    <div class="fa-flex-row fa-m12" style="font-size: large">
      <div class="fa-zz-card-right fa-mr12">{{userInfo.organizeFullName}}</div>
      <div class="fa-zz-card-right">{{userInfo.realName}}</div>
    </div>

    <a-table :columns="columns" :data-source="cardList">
      <template #bodyCell="{ column, record,index }">
        <template v-if="column.key === 'key'">
          <div> {{ index+1 }}</div>
        </template>
        <template v-if="column.key === 'creatorTime'">
          <div>{{ formatToDate(record.creatorTime) }}</div>
        </template>
        <template v-if="column.key === 'action'">
          <a-button type="link" size="medium" @click="fileDetail(record)">记录详情</a-button>
<!--          <a-button type="link" size="medium" @click="videoDetail(record)">视频详情</a-button>-->
<!--          <a-button type="link" size="medium" @click="delEduRecord(record)">删除该记录</a-button>-->
        </template>
      </template>
    </a-table>

    <batchThreeLevelCard @register="registerBatchCard" @reload="reload" />
  </BasicPopup>
</template>
<script lang="ts" setup>
import {BasicPopup, usePopup, usePopupInner} from '/@/components/Popup';
import {ref} from "vue";
import {testEduThreeCardRecordApi as threeCardRecordApi, threeLevelCardApi} from "/@/api";
import {useMessage} from "/@/hooks/web/useMessage";
import {formatToDate} from "/@/utils/dateUtil";
import batchThreeLevelCard from './batchThreeLevelCard.vue'

defineOptions({name: 'threeLevelCardList'});
const emit = defineEmits(['register', 'reload']);
const [registerPopup, {closePopup}] = usePopupInner(init);
const [registerBatchCard, {openPopup: openCardPopup}] = usePopup();

const {createMessage, createConfirm} = useMessage();

const userInfo = ref<any>({});
const cardList = ref<any[]>([])

const columns = [
  {title: '序号', dataIndex: 'key', key: 'key', align: 'center',},
  {title: '参与培训时间', dataIndex: 'creatorTime', key: 'creatorTime',},
  {title: '培训组织', dataIndex: 'organizeName', key: 'organizeName',},
  {title: '培训岗位', dataIndex: 'posName', key: 'posName',},
  {title: '培训状态', dataIndex: 'status', key: 'status',},
  {title: '操作', dataIndex: 'action', key: 'action', width: 120}
]


async function init(data:any) {
  userInfo.value = data
  console.log('userInfo', userInfo)
  reload()
}

function fileDetail(record) {
  openCardPopup(true, new Array({
    userId:record.userId,
    id:record.id
  }));
}

function delEduRecord(record){
  console.log('delEduRecord')

  createConfirm({
    iconType: 'warning',
    title: '删除',
    content: '是否确认删除选中计划相关记录？',
    onOk: () => {
      threeCardRecordApi.remove(record.id)
        .then(_res => {
        reload()
      })
    },
  });
}

async function handleClose() {
  closePopup();
}

async function handleSubmit() {
  closePopup();
  emit('reload');
}

async function reload(){
  let _res = await threeCardRecordApi.page({userId: userInfo.value.id});
  cardList.value = _res.data.list
}

</script>

