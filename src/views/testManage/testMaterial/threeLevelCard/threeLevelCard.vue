<template>
  <div style="display: flex;flex-direction: row; justify-content: center;">
    <div class="fa-flex-column-center" style="width: 1250px;">
      <div style="width: 1200px;">
        <div class="fa-flex-row" style="width: 1200px;margin: 20px 200px;">
          <div style="width: 400px;">
            <img src="/resource/img/three-level-card-logo.png" style="height: 80px;width: 80px;position: relative;left: 200px;"/>
          </div>
          <div class="fa-flex-column " style="width: 800px;margin-top:20px;margin-left: 20px;">
            <text style="font-size: 20px;font-weight: 800;">中核五公司漳州核电项目部</text>
            <text style="font-size: 20px;font-weight: 800;">入场三级安全教育培训记录卡（线上）</text>
          </div>
        </div>
        <div class="fa-flex-center">
          <table class="fa-table">
            <tr class="fa-table-tr-p4" style="font-size: 16px;">
              <td style="width: 150px" class="fa-text-center fa-le-sp2">姓名</td>
              <td style="width: 150px" class="fa-text-center fa-le-sp2">{{ cardInfo.userEntity.realName }}</td>
              <td style="width: 150px" class="fa-text-center fa-le-sp2">身份证号码</td>
              <td style="width: 220px" class="fa-text-center fa-le-sp2">{{ cardInfo.userEntity.certificatesNumber }}</td>
              <td style="width: 150px" class="fa-text-center fa-le-sp2">性别</td>
              <td style="width: 200px" class="fa-text-center fa-le-sp2">{{ cardInfo.userEntity.gender === 1 ? '男' : cardInfo.userEntity.gender === 2 ? '女' : '不明' }}</td>
            </tr>

            <tr class="fa-table-tr-p4" style="font-size: 16px;">
              <td style="width: 200px" class="fa-text-center fa-le-sp2">部门/施工队</td>
              <td colspan="2" class="fa-text-center fa-le-sp2">{{ `${cardInfo.userEntity.organizeName}`.split('/')[3] }}</td>
              <td style="width: 200px" class="fa-text-center fa-le-sp2">建卡日期</td>
              <td colspan="2" class="fa-text-center fa-le-sp2">{{ formatToDate(cardInfo.assignDate) }}</td>
            </tr>

            <tr class="fa-table-tr-p4" style="font-size: 16px;">
              <td style="width: 200px" class="fa-text-center fa-le-sp2">岗位</td>
              <td colspan="2" class="fa-text-center fa-le-sp2">{{ cardInfo.userEntity.positionName }}</td>
              <td style="width: 200px" class="fa-text-center fa-le-sp2">培训类别</td>
              <td colspan="2" class="fa-text-center fa-le-sp2">新入职</td>
            </tr>

            <tr class="fa-table-tr-p4" style="font-size: 16px;">
              <td style="width: 200px" class="fa-text-center fa-le-sp2">类型</td>
              <td colspan="3" class="fa-text-center fa-le-sp2">三级安全教育内容</td>
              <td style="width: 200px" class="fa-text-center fa-le-sp2">级别</td>
              <td colspan="2" class="fa-text-center fa-le-sp2">受教育人</td>
            </tr>

            <!--一级教育-->
            <tr class="fa-table-tr-p4">
              <td style="writing-mode:vertical-lr;" rowspan="3" class="fa-text-center fa-le-sp2">一级教育</td>
              <td colspan="3" rowspan="3" class="fa-text-center fa-le-sp2 ">
                <div class="fa-flex-column-center">
                  <div>1.国家安全生产方针、政策，HSE法律、法规、标准规范；</div>
                  <div>2.公司及本项目安全环保情况及基本知识；</div>
                  <div>3.公司及本项目安全环保规章制度和劳动纪律；</div>
                  <div>4.从业人员有关健康安全环保的权利和义务；</div>
                  <div>5.事故应急救援、事故应急预案演练及防范措施；</div>
                  <div>6.包括公司及本项目安全环保事故事件在内的重要内外部事故案例和经验反馈；</div>
                  <div>7.其他需要培训的内容。</div>
                </div>
              </td>
              <td class="fa-text-center fa-le-sp2" style="height: 70px;">公司/项目部</td>
              <td class="fa-text-center fa-le-sp2">
                <div class="fa-flex-column-center">
                  <div>签名:</div>
                  <img :src=cardInfo.userSign1>
                  <!--                  <div>{{cardInfo.userSign1}}</div>-->
                </div>
              </td>
            </tr>
            <tr class="fa-table-tr-p4">
              <td colspan="2" class="fa-text-center fa-le-sp2" style="height: 70px;">
                <div>日期：{{ cardInfo.startTime1 ? `${formatToDate(cardInfo.startTime1)} 至 ${formatToDate(cardInfo.endTime1)}` : "/" }}</div>
                <div>（共{{ cardInfo.totalDuration1 ? (cardInfo.totalDuration1 / (45 * 60)).toFixed(1) : "/" }}学时）</div>
              </td>
            </tr>
            <tr class="fa-table-tr-p4">
              <td colspan="2" class="fa-text-center fa-le-sp2" style="height: 70px;">培训成绩:{{cardInfo.grade1}}  {{cardInfo.isPass1===1?'合格':cardInfo.isPass1===0?'不合格':''}}</td>
            </tr>

            <!--二级教育-->
            <tr class="fa-table-tr-p4">
              <td style="writing-mode:vertical-lr;" rowspan="3" class="fa-text-center fa-le-sp2">二级教育</td>
              <td colspan="3" rowspan="3" class="fa-text-center fa-le-sp2 ">
                <div class="fa-flex-column-center">
                  <div>1.本部门的安全环保状况及规章制度；</div>
                  <div>2.工作环境及危险因素；</div>
                  <div>3.所从事工种可能遭受的职业伤害和伤亡事故；</div>
                  <div>4.所从事工种的安全职责、操作技能及强制性标准；</div>
                  <div>5.自救互救、急救方法、逃生路线、疏散和现场紧急情况的处理；</div>
                  <div>6.安全设备设施、个人防护用品的使用和维护；</div>
                  <div>7.安全技术基础知识，预防事故和职业危害的措施及应注意的安全事项；</div>
                  <div>8.包括本部门安全环保事故事件在内的重要内外部事故案例和经验反馈；</div>
                  <div>9.其他需要培训的内容。</div>
                </div>
              </td>
              <td class="fa-text-center fa-le-sp2"  style="height: 70px;">部门/施工队</td>
              <td class="fa-text-center fa-le-sp2">
                <div class="fa-flex-column-center">
                  <div>签名:</div>
                  <img :src=cardInfo.userSign2>
                  <!--                  <div>{{cardInfo.userSign2}}</div>-->
                </div>
              </td>
            </tr>
            <tr class="fa-table-tr-p4">
              <td colspan="2" class="fa-text-center fa-le-sp2"  style="height: 70px;">
                <div>日期：{{ cardInfo.startTime2 ? `${formatToDate(cardInfo.startTime2)} 至 ${formatToDate(cardInfo.endTime2)}` : "/" }}</div>
                <div>（共{{ cardInfo.totalDuration2 ? (cardInfo.totalDuration2 / (45 * 60)).toFixed(1) : "/" }}学时）</div>
              </td>
            </tr>
            <tr class="fa-table-tr-p4">
              <td colspan="2" class="fa-text-center fa-le-sp2"  style="height: 70px;">培训成绩:{{cardInfo.grade2}}  {{cardInfo.isPass2===1?'合格':cardInfo.isPass2===0?'不合格':''}}</td>
            </tr>

            <!--三级教育-->
            <tr class="fa-table-tr-p4">
              <td style="writing-mode:vertical-lr;" rowspan="3" class="fa-text-center fa-le-sp2">三级教育</td>
              <td colspan="3" rowspan="3" class="fa-text-center fa-le-sp2 ">
                <div class="fa-flex-column-center">
                  <div>1.岗位安全环保职责、安全操作规程和操作技能以及劳动纪律；  </div>
                  <div>2.本岗位和相邻岗位的工作环境和危险因素；</div>
                  <div>3.岗位间工作衔接配合的安全环保与职业卫生事项；</div>
                  <div>4.安全警示标志标识；</div>
                  <div>5.劳动防护用品的正确使用；</div>
                  <div>6.岗位异常和故障的处理和应急处置；</div>
                  <div>7.包括本班组安全环保事故事件在内的重要内外部事故案例和经验反馈；</div>
                  <div>8.岗位应知应会的其他内容。</div>
                </div>
              </td>
              <td class="fa-text-center fa-le-sp2"  style="height: 70px;">班组/科室</td>
              <td class="fa-text-center fa-le-sp2">
                <div class="fa-flex-column-center">
                  <div>签名:</div>
                  <img :src=cardInfo.userSign3>
                  <!--                  <div>{{cardInfo.userSign3}}</div>-->
                </div>
              </td>
            </tr>
            <tr class="fa-table-tr-p4">
              <td colspan="2" class="fa-text-center fa-le-sp2"  style="height: 70px;">
                <div>日期：{{ cardInfo.startTime3 ? `${formatToDate(cardInfo.startTime3)} 至 ${formatToDate(cardInfo.endTime3)}` : "/" }}</div>
                <div>（共{{ cardInfo.totalDuration3 ? (cardInfo.totalDuration3 / (45 * 60)).toFixed(1) : "/" }}学时）</div>
              </td>
            </tr>
            <tr class="fa-table-tr-p4">
              <td colspan="2" class="fa-text-center fa-le-sp2"  style="height: 70px;">培训成绩:{{cardInfo.grade3}}  {{cardInfo.isPass3===1?'合格':cardInfo.isPass3===0?'不合格':''}}</td>
            </tr>

            <tr class="fa-table-tr-p4">
              <td colspan="6" class="fa-le-sp2"  style="height: 70px;">
                <div>经安全监督部核查，该员工三级安全教育培训合格，准许入场，可签订责任书，领取劳保用品。</div>
                <div style="position: relative;left: 800px;">
                  <text>核查人确认：</text>
                  <div></div>
                </div>
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>


</template>
<script lang="ts" setup>
import {ref} from "vue";
import {formatToDate} from "/@/utils/dateUtil";

const userId = ref('')
const cardId = ref('')
const cardInfo = ref({
  userEntity:{
    realName:'',
    certificatesNumber:'',
    organizeName:'',
    positionName:'',
    gender:undefined,
  },
  createTime:'',
  userSign1:'',
  teacherSign1:'',
  startTime1:'',
  grade1:undefined,
  isPass1:undefined,
  userSign2:'',
  teacherSign2:'',
  startTime2:'',
  grade2:undefined,
  isPass2:undefined,
  userSign3:'',
  teacherSign3:'',
  startTime3:'',
  grade3:undefined,
  isPass3:undefined,
})
defineOptions({name: 'testManage-threeLevelCard'});
defineProps(['cardInfo'])

// async function init(data:any) {
//   console.log('init data',data)
  // userId.value = data.userId
  //
  // await threeLevelCardApi.getCardInfoByUser({
  //   userId: userId.value,
  //   threeLevelCardId: data.id
  // })
  //   .then(res=>{
  //     console.log('res',res)
  //     cardInfo.value = res.data
  //   })
// }

// const cardRef = ref();
// async function printCard() {
//   printHtml(cardRef.value)
// }

// async function handleClose() {
//   closePopup();
// }

</script>
