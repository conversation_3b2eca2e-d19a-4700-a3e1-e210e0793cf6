<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup"
              title="" showOkBtn @ok="handleSubmit" @close="handleClose">
    <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="printTheCard()">打印三级教育卡</a-button>

    <div class="fa-flex-row">
      <div ref="cardRef" style="font-variant: normal;">
        <div v-for="(item, index) in cardInfoList" :key="item.id">
          <threeLevelCard :card-info="item"/>
        </div>
      </div>
    </div>

  </BasicPopup>
</template>
<script lang="ts" setup>
import {ref} from 'vue';
import {BasicPopup, usePopupInner} from '/@/components/Popup';
import threeLevelCard from './threeLevelCard.vue'
import {threeLevelCardApi} from "/@/api";
import {printHtml} from "/@/utils/printUtils";

defineOptions({name: 'batchThreeLevelCard'});

const emit = defineEmits(['register', 'reload']);
const [registerPopup, {closePopup}] = usePopupInner(init);

const cardInfoList = ref<any[]>([])

async function init(data:any) {
  console.log('ids',data)
  console.log('type',typeof(data))
  if (!Array.isArray(data)) return;

  data.forEach(obj => {
    console.log('obj',obj)
    setCardInfo(obj)
  })
}

async function setCardInfo(obj) {
  let res = await threeLevelCardApi.getCardInfoByUser({
    userId: obj.userId,
    threeLevelCardId: obj.id
  })

  const cardInfo = res.data
  cardInfoList.value.push(cardInfo)
}

const cardRef = ref();

async function printTheCard() {
  // const printContent = cardRef.value;
  // const printWindow = window.open('', '_blank') as any;
  // printWindow.document.write(printContent.innerHTML);
  // printWindow.document.close();
  // printWindow.print();
  printHtml(cardRef.value)
}

async function handleClose() {
  cardInfoList.value = []
  closePopup();
}

async function handleSubmit() {
  cardInfoList.value = []
  closePopup();
  emit('reload');
}
</script>

