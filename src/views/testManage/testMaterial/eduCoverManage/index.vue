<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
<!--          <template #tableTitle>-->
            <!--            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn"-->
            <!--                      @click="handelBatchRemove()">批量删除</a-button>-->
<!--          </template>-->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'creatorTime'">
              <div>{{ formatDate(record.creatorTime) }}</div>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)"/>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload"/>
  </div>
</template>
<script lang="ts" setup>
import {BasicTable, useTable, TableAction, BasicColumn, ActionItem} from '/@/components/Table';
import {useI18n} from '/@/hooks/web/useI18n';
import {useMessage} from '/@/hooks/web/useMessage';
import {usePopup} from '/@/components/Popup';
import Form from './Form.vue';
import {testEduCoverApi} from "/@/api";

defineOptions({name: 'eduFileManage'});

const {t} = useI18n();
const {createMessage} = useMessage();
const columns: BasicColumn[] = [
  {title: '培训分类', dataIndex: 'eduTypeName'},
  {title: '培训名称', dataIndex: 'eduName'},
  {title: '培训编号', dataIndex: 'eduNum'},
  // {title: '审批编号', dataIndex: 'approvalNum'},
  {title: '创建时间', dataIndex: 'creatorTime'},
  {title: '创建者', dataIndex: 'creatorUserId'},
];
const [registerForm, {openPopup: openFormPopup}] = usePopup();
const [registerTable, {reload}] = useTable({
  api: testEduCoverApi.list,
  searchInfo: {
    _sorter: 'f_creator_time DESC'
  },
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
    ],
  },
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
  },
});

function formatDate(timestamp) {
  return new Date(timestamp).toLocaleString();
}

function getTableActions(record): ActionItem[] {
  return [
    {
      label: '材料封面',
      onClick: EduCover.bind(null, record),
    },
    // {
    //   label: t('common.delText'),
    //   color: 'error',
    //   modelConfirm: {
    //     onOk: handleDelete.bind(null, record.id),
    //   },
    // },
  ];
}

function handleDelete(id) {
  testEduCoverApi.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

//todo 答题详情弹窗，附加固定模版导出功能
function EduCover(data) {
  console.log(data);
  openFormPopup(true, data);
}
</script>
