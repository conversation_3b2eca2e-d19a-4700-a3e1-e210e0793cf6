<template>
  <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="printThePaper()">打印封面</a-button>

  <div class="fa-flex-column fa-flex-center">
    <div style="width: 700px;" ref="paperRef">
      <table class="fa-table fa-table-evaluate">
        <tr class="fa-table-tr-p4">
          <td colspan="5">
            <div class="fa-table-title fa-img-center">
              <img src="/resource/img/培训封面-图片1.png" alt="Logo"/>
            </div>
          </td>
        </tr>
        <tr class="fa-table-tr-p4" style="height: 100px">
          <td colspan="5">
            <div class="fa-table-title-plus fa-text-center">{{eduCoverInfo.projName ? eduCoverInfo.projName:'漳州核电项目部'}}</div>
          </td>
        </tr>

        <tr class="fa-table-tr-p4 fa-pos-rel" style="height: 150px">
          <td colspan="5">
            <div style="position: absolute; top:2px;left: 4px">培训名称:</div>
            <div class="fa-text-center fa-table-title-plus text-danger">{{ eduCoverInfo.eduName }}</div>
          </td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td class="fa-text-center fa-le-sp2">
            <img :src=eduCoverInfo.initSign width="100px">
          </td>
          <td class="fa-text-center fa-le-sp2">
            <img :src=eduCoverInfo.auditSign width="100px">
          </td>
          <td class="fa-text-center fa-le-sp2">
            <img :src=eduCoverInfo.approveSign width="100px">
          </td>
          <td class="fa-text-center fa-le-sp2">
            <div style="flex: 1" class="fa-text-center">{{ formatToDate(eduCoverInfo.creatorTime) }}</div>
          </td>
          <td class="fa-text-center fa-le-sp2">
            <div style="flex: 1" class="fa-text-center">{{ formatToDate(eduCoverInfo.planStartTime) }}</div>
          </td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td style="width: 140px" class="fa-text-center fa-le-sp2">编制</td>
          <td style="width: 140px" class="fa-text-center fa-le-sp2">审核</td>
          <td style="width: 140px" class="fa-text-center fa-le-sp2">批准</td>
          <td style="width: 140px" class="fa-text-center fa-le-sp2">批准日期</td>
          <td style="width: 140px" class="fa-text-center fa-le-sp2">实施日期</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td class="fa-text-center fa-le-sp2">文件类别</td>
          <td class="fa-text-center fa-le-sp2">培训记录</td>
          <td class="fa-text-center fa-le-sp2">内部编号：</td>
          <td class="fa-text-center fa-le-sp2" colspan="2">{{ eduCoverInfo.eduNum }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td colspan="5">
            <div class="fa-col13">
              <div class="fa-flex-center fa-le-sp2">培训类别：</div>
              <div class="fa-col-right">
                <div class="fa-checkbox-col">
                  <div class="fa-checkbox">
                    <span v-if="eduCoverInfo.eduType===0">√</span>
                  </div>
                  基础培训
                </div>
                <div class="fa-checkbox-col">
                  <div class="fa-checkbox">
                    <span v-if="eduCoverInfo.eduType===1">√</span>
                  </div>
                  技能培训
                </div>
                <div class="fa-checkbox-col">
                  <div class="fa-checkbox">
                    <span v-if="eduCoverInfo.eduType===2">√</span>
                  </div>
                  专项培训
                </div>
              </div>
            </div>
          </td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td style="width: 175px" class="fa-text-center fa-le-sp2">授课人员</td>
          <td style="width: 175px" class="fa-text-center fa-le-sp2">{{ emptyToNA(eduCoverInfo.eduTeacherName) }}</td>
          <td style="width: 175px" class="fa-text-center fa-le-sp2">受训人员</td>
          <td style="width: 175px" class="fa-text-center fa-le-sp2" colspan="2">{{ eduCoverInfo.eduPeople }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td style="width: 175px" class="fa-text-center fa-le-sp2">培训时间</td>
          <td style="width: 175px" class="fa-text-center fa-le-sp2">{{ formatToDate(eduCoverInfo.planStartTime) }}</td>
          <td style="width: 175px" class="fa-text-center fa-le-sp2">培训地点</td>
          <td style="width: 175px" class="fa-text-center fa-le-sp2" colspan="2">{{ emptyToNA(eduCoverInfo.eduLocation) }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td style="width: 175px" class="fa-text-center fa-le-sp2">培训学时</td>
          <td style="width: 175px" class="fa-text-center fa-le-sp2">{{ eduCoverInfo.eduHours % 0.5 === 0 ? eduCoverInfo.eduHours : Math.round(eduCoverInfo.eduHours * 2) / 2 }}</td>
          <td style="width: 175px" class="fa-text-center fa-le-sp2" colspan="2">{{ eduCoverInfo.eduContent }}</td>
        </tr>

        <tr style="height: 100px" class="fa-table-tr-p4">
          <td style="width: 175px" class="fa-text-center fa-le-sp2">培训目的</td>
          <td colspan="4" class="fa-text-center fa-le-sp2">
            {{ eduCoverInfo.eduPurpose }}
          </td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td style="width: 175px" class="fa-text-center fa-le-sp2">版权声明</td>
          <td colspan="4" class="fa-le-sp2">
            此文件内容属中国核工业第五建设有限公司所有，未经同意不得引用、复制、借阅或发表。
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {printHtml} from "/@/utils/printUtils";
import {formatToDate} from "/@/utils/dateUtil";
import {emptyToNA} from "/@/utils/str";

defineProps(['eduCoverInfo']);
const paperRef = ref();

async function printThePaper() {
  printHtml(paperRef.value)
}
</script>
