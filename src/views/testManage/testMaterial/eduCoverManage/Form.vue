<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="" showOkBtn @ok="handleSubmit" @close="handleClose">
    <EduCoverComponent :edu-cover-info="eduCoverInfo" />
  </BasicPopup>
</template>
<script lang="ts" setup>
import {computed, ref} from 'vue';
import {BasicPopup, usePopupInner} from '/@/components/Popup';
import testEduCover from "/@/api/testManage/testMaterial/testEduCover";
import EduCoverComponent from "/@/views/testManage/testMaterial/eduCoverManage/EduCoverComponent.vue";

const emit = defineEmits(['register', 'reload']);
const [registerPopup, {closePopup, changeLoading, changeOkLoading}] = usePopupInner(init);

const eduCoverInfo = ref({
  eduName: undefined,
  eduNum: undefined,
  initSign: undefined,
  auditSign: undefined,
  approveSign: undefined,
  creatorTime: undefined,
  planStartTime: undefined,
  eduLocation: undefined,
  eduTeacherName: undefined,
  eduType: undefined,
  organizeNames: undefined,
  eduHours: undefined,
  eduContent: undefined,
  eduPurpose: undefined,
})

const signSrc = ref("data:image/png;base64,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")

async function init(data) {
  console.log("data", data)

  if (data.approvalId) {
    await testEduCover.getEduCoverInfo(data.approvalId).then(res => {
      console.log("res", res)
      if (res.data) {
        eduCoverInfo.value = res.data
      }
    })
  }

  if (eduCoverInfo.value.creatorTime) {
    eduCoverInfo.value.creatorTime = new Date(eduCoverInfo.value.creatorTime).toLocaleDateString();
  }
  if (eduCoverInfo.value.planStartTime) {
    eduCoverInfo.value.planStartTime = new Date(eduCoverInfo.value.planStartTime).toLocaleDateString();
  }
  // eduCoverInfo.value.initSign = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAkQAAAEsCAYAAAAir6zwAAAgAElEQVR4Xu2dC/w221j3n1JCiVLKsSdFiHRwSqVn206FpIhe0nYshxyqFzluOzqoiE2O8UiFihyi0OYJqZS38Kqc8jgkVEiiN73p+tpz6drz3Pc9a2bWzKyZ9bs+n/W55/+/Z9bht9Y985trXYfPOSIRAkJACAgBISAEhEDlCHxO5ePX8IWAEBACQkAICAEhcESESItACAgBISAEhIAQqB4BEaLql4AAEAJCQAgIASEgBESItAaEgBAQAkJACAiB6hEQIap+CQgAISAEhIAQEAJCQIRIa0AICAEhIASEgBCoHgERouqXgAAQAkJACAgBISAERIi0BoSAEBACQkAICIHqERAhqn4JCAAhIASEgBAQAkJAhEhrQAgIASEgBISAEKgeARGi6peAABACQkAICAEhIAREiLQGhIAQEAJCQAgIgeoRECGqfgkIACEgBISAEBACQkCESGtACAgBISAEhIAQqB4BEaLql4AAEAJCQAgIASEgBESItAaEgBAQAkJACAiB6hEQIap+CQgAISAEhIAQEAJCQIRIa0AICAEhIASEgBCoHgERouqXgAAQAkJACAgBISAERIi0BoSAEBACQkAICIHqERAhqn4JCAAhIASEgBAQAkJAhEhrQAgIASEgBISAEKgeARGi6peAABACQkAICAEhIAREiLQGhIAQEAJCQAgIgeoRECGqfgkIACEgBISAEBACQkCESGtACAgBISAEhIAQqB4BEaLql4AAEAJCQAgIASEgBESItAaEgBAQAkJACAiB6hEQIap+CQgAISAEhIAQEAJCQIRIa0AICAEhIASEgBCoHgERouqXgAAQAkJACAgBISAERIi0BoSAEBACQkAICIHqERAhqn4JCAAhIASEgBAQAkJAhEhrQAgIASEgBISAEKgeARGi6peAABACQkAICAEhIAREiLQGhIAQEAJCQAgIgeoRECGqfgkIACEgBISAEBACQkCESGtACAgBISAEhIAQqB4BEaLql4AAEAJCQAgIASEgBESItAaEgBAQAkJACAiB6hEQIap+CQgAISAEhIAQEAJCQIRIa0AICAEhIASEgBCoHgERouqXgAAQAkJACKwOgQtYj8+y8kdWXmblP1c3AnW4OAREiIqbEnVICAgBISAEOhD4qH1/keacj9jnlwoxITAWARGisQjqeiEgBISAEJgTgfO1NEKfsr/PP2cH1NY2ERAh2ua8alRCQAgIga0i0CZEbJ09bKuD1bjmQ0CEaD6s1ZIQEAJCQAiMR+CiVgXbZMh/WYEgSYTAaAREiEZDqAqEgBCoCIHr2livZuXsisZc2lCvYh16c9Op99nnZUrroPqzTgREiHbPGz+477byqHVOq3otBITABAicbnX+YVPvb9nnrSdoQ1V2I3BjO+X3m9POsc/rd1+iM4RANwIiRKdixNvGe5p/49J5rBtGnSEEhEAFCPysjfEBzTj/1j6vVMGYSxzina1TT206dtw+71BiJ9Wn9SEgQnTqnN3L/vXY5t8ft88Lr29a1WMhIAQmQOB/WZ2/0dT7Evu86QRtqMpuBB5upzy0Oe0R9vmQ7kt0hhDoRkCE6FSM7mr/enLz73+2zy/rhlFnCAEhUAECkRD9po33thWMucQh/qp16o5Nx3403K9L7Kv6tCIERIhOnazr2b/Yl0beYOXqK5pPdVUICIHpEBAhmg7bPjUTmfqGzQVo6dDWSYTAaAREiA4Tolfa1xhSSoSAEBACIkRlrIG3WDeu3HTlG+3zjWV0S71YOwIiRCJEa1/D6r8QmAsBEaK5kD7czr/Y11/cnIJJA6YNEiEwGgERIhGi0YtIFQiBShAQIVp+omNQxn+z7nzR8l1SD7aCgAiRCNFW1rLGIQSmRkCEaGqEu+uPQRnfaqdfsfsSnSEE0hAQIRIhSlspOksICAERouXXwJJBGe9mw3/i8hCoB1MhIEIkQjTV2lK9QmBrCIgQLT+jMSjjM607Z8zUpZPWzldZ+aSVC83UppqZGQERIhGimZecmhMCq0VAhGj5qTvTuuCZ7R9pxw+eoUu/aG38RGjnEnb8gRnaVRMzIyBCJEI085JTc0JgtQiIEC0/dXMFZYT0/IiV+1u5QGvYF7S//315KNSD3AiIEIkQ5V5Tqu9cBIh4jkfMcSsfEiibQECEaPlpnDoo4zfbEIl+fZcdQ2W77LlWlDtt+XUwSQ9EiESIJllYlVf6Khv/sQaDT9snMVPIiydZNwIiRMvP319bFzypbs6gjES8hgjdZMcQ/9X+92IrStWy/PxP2gMRIhGiSRdYpZW/18Z96TB28i49o1IstjRsEaLlZ/NT1oXPa7oxNigjLyoPsAIZuuqOob3a/vckK89eftjqwRwIiBCJEM2xzmpr4342YDJyu+3Bd9oxN1fJuhEQIVp2/rDreX/TBTSvnzugO9eya67flGN7rn9OQ4T+aED9umTFCIgQiRCtePkW3XVuptdteihCVPRUJXdOhCgZqklOvIHV+vKm5r+zz69JaOVSdg7XOQn6ij3XEPX6yQ0RentCvTplgwiIEIkQbXBZFzEkEaIipiFrJ0SIssLZu7L72hWPbq56in3iBbZLnPxAhDCSPiTkQXuJlbtbgRRJKkZAhEiEqOLlP+nQRYgmhXeRyn/MWn1c0/Kf2ee1F+lFvY0+3YbuHl73suOzGygwsoYEuSYIt/h98vf2xR+GonhC9a6nU0YuQiRCpJ/DNAiIEE2D65K1Rg3R26wjX7dkZyps+/U25ms04yaFxvmsXM/K13ZgEQnQGyrETUNORECE6FSgYq6cdyb82BKh1mmVISBCtL0Jj4ToD2x437W9IRY7oi+3nqHd+fyEHv5towF6RfP5iYRrdIoQOCJCdOoi4I3jnObfBNTbZ4Sn5SMEDiHwLvvyaHMCa4rYRJJ1IyAbonnnj9/Pzax8jxW2xPYJcYLQAjkBklH0vPO0mdZEiA4Tojfa1wT/kgiBvgj8o11AnBSEhJSkHJCsGwERounn7+sbEgQRus6B5j7akKAn2OeJ6bulFmpAQIToMCF6pX19eg0LQWPMjsCbrcarNLXK7T47vItUKEI0DezXbEgQmqBvSGiClwteMiRCICsCIkQiRFkXlCr7LAKyIdreYhAhyjenpwUSdCie0O/beaTNIJr0dzfN/7B9/lq+rqgmIXAuAiJEIkT6LUyDgAjRNLguWasI0Tj0ITVuE/SVe6r6fw0BggS9yApbY8hbrVyhOf4W+/w/47qiq4XAqQiIEIkQ6XcxDQIiRNPgumStIkT90L+InX4nKwRHZDvswnsu/3BDgiBAECHylUW5kP0RgyZ+gf39H/26orOFQDcCIkQiRN2rRGcMQUCEaAhqZV8jQnR4fsgtdswK22GUbztwOgmQXQv0so5px8aIQJgI2e4xvJYIgewIiBCJEGVfVKrwMwjkJkQ3sTpx3VdMleUWmAjRqdhDViIJQnuzT4gP5CTotT2mES3T05rzn2uft+lxrU4VAskIiBCJECUvFp3YC4GchIjs27e2Qobvy1k52asnOjkXAiJER46QJgPtj5MgDy2xD2NsgH7PCjnI/nLgRDzGrrtPc+1D7PMRA+vRZULgIAIiRCJE+olMg0BOQvQe6+Jlmm4+2D4fOU2XVWsHAjUSoku3CNBXd2D0N/Y9mkwvJE8dKwRd9PAnt7DjF4ytUNcLgV0IiBCJEOmXMQ0COQkR2wtuj/G9dvzCabqsWkWIjnyRYQDxu3JDhLriAr2vRYBOTrCK/t3q9K048paRUkkiBLIjIEIkQpR9UanCzyCQkxDxRnzzBlcRouUW2FY1RJAMYvyQm41cjofkX1oEiACkUwqeaR9rGmDLGMNtiRCYBAERIhGiSRaWKhUh2uAa2BIh+o6GAEGCDqUn+q8WAXrdzPPKFt3fNW3inn+xmdtXcxUhIEJ06mSTRJAkgcg7rFy+ovWgoeZDQBqifFiWUtOaCRFbYZAf1wQdSlqNRuYlVp7ZkKElY/5c3frw580CIBgjQRklQmASBESIToU1ZrsnQefFJ0FelW4dARGi7c3w2ghR6lbYf9pUkSKD8lIr7y5o6tjCo18IL6o3LKhv6srGEBAhOkyI3mRfX21jc67hzIMAan73yBmb3FU2RPPMWVcrayBEqVthGEM7AeKTlBklyu2sU89qOvZs+2QOJEJgEgREiA4ToqWz3RP6nozpfzzJ7KvSKRH4J6vc7R3uasdPHdGYCNEI8DJeWiIh4h5OjJ6rWmFLbF+OMGBg68k1QX+aEZcpq7q3Vf7LTQNn2+e9pmxMddeNgAhROYQIbwpuaMSbIfcP5YpN97iRERFWsh4E3mJdxXUZkYZoPfN2qKd3sy9/pTnhT+zzOgsP69utfV7aPn9PP0reCkuF7qftRGJvIWdaeXjqhTpPCPRFQIToVMSiDdGUGqJvsqYhOddoyqF4Hx+3c/YlRuw75zp/HgRkQzQPznO2EjVEJThcQHjO1wKArTDsgFwTVOpWWOq8QUAhosg9rTwh9UKdJwT6IiBCNA8h+qoW+YEIkcE5RSBDz7AiVXEKWuWcI0JUzlzk6kkkRC+3Sm+Uq+IB9ZDPC5sal7PsABK0lq2w1CH/lp14q+bkH7RP0thIhMAkCIgQnQrrWA0R7q14RuDh4Rogwt+nCG6leLYRiZhjyqdSLtQ5xSEgQlTclIzuUGk2RGzbXcHKk608cPToyqzgHOsW92TkBlZI4yERApMgIEJ0Kqx9CRHan2+1gj0BJTVOxkk79/VWsA+icPzJSWZZlS6BQE5C9Fc2APd21FvyErN5bpulEaLlkJiv5TdaU25OgJkBvwWJEJgEARGi/oQIY2cnQHx2JTukBQKdOelxEvT3k8yoKi0FgZyE6L02KNcynmnHMixdZpZFiObHnfvkJZtmL2uf/BYkQmASBESIDhMiHmpkFo8E6IsTZoKtrvdb+d2GCE2d7yehSzplZgRyEiLCLrhHk3KZzTyRoTkRovmx///WpOcvw+5SWvT556CaFkWIzjvVbH/d2Yq7eaYshH+zk8jvw36+f3oywpTrdU4/BPDK81D+/a6c9+ychEhxiOadu32tiRDNOw8XCARIiV3nxb7K1monREO2vwhrD/lxAvQXVa6cZQbt9gRk3L7oMl1IblWEKBmq1ZwoQjTvVEVCRIiBffGW5u3Vua2xa/ClVujXBa3w0iJZOQI1ESK8v063gnEq2w8s6NTtr0iA3rXyOV9z9//dOv8FzQCIT3KPggcjQlTw5AzsmgjRQOAGXhYJEb99iMeScovmuUHkebIIRCE8CjZOH1myg2p7HAJbJkTR++u6DRHqQovtr7+14p5i77Rj3OeXEn50ELgP7ukAhrYEYtsnRL3eZ4RItutXLzWwge3+jl33/eHaM+z4mQPrmvoyEaKpEZ6/fhGieTFfkhBBvqLtKC/T/jK2D4V/tS9SXrLnRVGtJSOwJUI0dvsLLRC2KdHtnnxUX56M5rATT2t+REftExIXP1HJTimoewnk5jGPeAt77pQNZqibIJVnNPX8h31CXv9vhnpzVwGZvlxTqVJ35EZ3mfpEiObFfU5CxMul7xxAhK7VMdT/Ct+70Tf/uraVP5sXJrWWC4G1EiK2vyJ777P9xZYX3l8QIDKSRyEM/m2tuNYB77BDKTXGzgMaqdSI1WPbSr3+PXYixKxUOb91DLstklkiGLJ/W4Gd/bD16Uuafv2ofRI8b6jIqHoocnmvEyHKi2dXbVMSIoI8ov3n2UFJ2QkgDQp2jORX+73Q+RgWA638Ia1915j1/YIIrIUQxe0vFu/VEzBjT9dtf/wTY9xDEm1UOG/KXGZshXUFGSNKNd4V/2yl7bn2ZfY/iCFj2rVvfeh7HtSMlT3vXQJR/JoEjJc6hTUAEXIpMQv2X1vnrtR0sBQNETGzZAM3fNWKEA3HbsiVuQjR51njaH94ceLzhlZ4seoSyE98hrx9zwUiRF1IruT7UglRru2vPtNALqCHtC6YkhDR1F9a+TorbK+8xgoebCfD5wf6DGDAuZewa8CacjsrpAFwKXVteP9I9AgRcjnDDkqyJyrFhuhShgvE9yesYH9FXJdvtFLiNuOAJTzrJSJEs8J9ZCghwtTAyY9/tpPgtkdCfKNIfnjhSjWQFiGad11M1loJDz2M0Fi0xJcZ6v21a/urL2jPswu+r3XR1ISobx+nPP8LrXK0ashaYn5EeyLU2ayhUoJgLkmI0ARhWwVpRDvVlsfaP+4z5WLaaN0iRPNObCohYr1HDZCnuTnUW+wP0eL+mhXIzxi7H4yp0dYj2jKbd41kbW0JQoR3EwSIwj6u24IcGlh7+4sFnDP4ISpV2mh7EdRKiLhZdHlUZF2IAytb2p7okJdfTkKE0Tt5nJAfsPLbe/BCA0SSTzR++4S33qmN9QdO5yKXcf/BeeIfEloXIUoAKeMpkRCh2eQ+jeD4ckUrToKOJrQJ+eG5QdR3Pt+WcE3qKZg2eN/QvrLVJlkhAnMQImxRnADx+fUJOMXgh+79lXDZ4FNubld6YC3sZ9w7qFZChLG3v/EMBnWmC5eyJ8Ko8iZWwAobN+y8ouQkRBhpsvWFPNzKmaGhK9sx2504A+yzCcNQHpsyHvoQAKU/OFebyL3Ft1JI0dMVoV6EaKYfddMMYUc+GprEI5Z569r+4hIPnOskaF/okhwjoo8el0gaohyILlTHFIQIdWUkQCkeSzxUeMC82AoLeG7Dz6dYm3dp5uDZ9klGcUSEaKGF2bPZtj3RHez64z3r6Hs6W3RumHkjO355q4KchIjfBMQP8VxmbDUTlwkt6y7BLZjEmD9khb5IzovA4+zPHwv/eosdX6UDJBGi6VYRhJ1nBxoWL13z4b1hyypqf9AC4TQyl8iGaC6kJ24nByGK5Ifji3X0GdUnCzaW+BYw8ZB3Vh8zKt/bzsDGAkkhRLyhs8X0jiU6nrHNaENUWpj8lGG24xPhiTiVPRG2ZticIWzdtqPW8v+chKjtds+2M66/F28Bw40Zw3LsIvZ5xKRgWcM5bHHgtOD3KzAmEvEhESHKszKOWjWR/HCMHVCqcL/FKYB1znNk6fRJIkSpM1f4eX0JEfu2LFzeVp0Iddma4BbeJkA8cEuR77COeMRmiNHtrZzTdK6LEH3CzvNw8i+1Y7ZQ1iprNKqOWLftiSAqrNcU25C+cxYjZu/bapmKEL3JOtuOjcWDHW0H/ZKkI4BWGO0w8lQrpGQ4JCJE6dj6mTwnjlpxrQ/kZ0iwW54Z3F+xz5ly+6v/CM/NBoA9IaItsyEIFnJNKiG6mfX3hVZSzn+/nfdaK67CXJq9d0H9c3bC/ZuTuDkSqTmFEPHjjMG52KLgh05AvjVKJERrMapu49y2J8IejLd+SEQuQTsTwyFgE4fBZluGEiJuqIQ/4JNgcRS25HYls0UrSaiI5+QaXGX1iBDlm3BibqEt98Lf2KvFKM6HWuOeQ1w2CA+fFNIouW1eCbnM9vVfhCjfOlq0phSCQwe54d56T0+x1o8aIBbxmoSHpXu6YVyNt1kKIWKMqG25ATiOj7DjdiyjtWARCdGajKp34UvYABcIKp5ZPqdj5wN39cc0lZywz9P2VNiXEPEG/TQrnkevq59rXmtdY5vr+y0QoqMG1sm5ALN2IvGJx30y0fObbJOfXS8t+7zMZhxuUlMiREkwlX9SKiHiLZUb/FdaYZuI1BcvsQIRWnOYcsgMxpQIbyB4VhG3JZUQcV1Uo+O9gzdQakCvklbI2m2IIpa3tD9+y0pc38wTBvNj5fVWAZ4uyI9Y8S2Xdr2phAjXYVJ7YPycIv9oJ13fSk6tV0q7WzxnbYToqE0CW0cXtoLdGlv6bNm/yAovc32EezrX7rtX4bFI4FbukUOJD/3BJR0NKn10zU87ZdK+fkdCxDmpz6s+OOQ4V4QoB4oF1FHqApsLmv9tDT2qaQwjWR6kMblrlw2R95NtQX+zx9uILY61ySWtw9hQIWz/pbi2ljxGiC3bn2xxuWAwj3fRUIEIQYgQHkzE88HDZZewneXpT9qpO7ABQrN0NytEKm8Lb9CsIwyjqefOVrB1Q9zLbOgYar2OBzwOHQgG1cwfJNl//79px/ezgi0a2ze7BOwf33yBETbejW0hNyEvjV3CCwia2H1C6A+2TrmvEFfqmlb23a/7xpZCC077OYXQDhCfdulKl9TVB9f2lhwsVoSoaxZX8n3thOiEzZNH8r2jHeOpFAkR24G7Hljt6Y1aon1eR6UviZtaBwl7gBAH6mjpHU7oH2+3kKLovjtmqylqfZ5l9WKAv094SLndD8TnSc2JGN9/156L0Lw+2go2eFGU3DVhsvecgl0ZThMeOG94TeVeiXbx1xO7RxZ34vkMlamIz77+sAuB1v2klWNDOz3xdYpUPTHAc1VfMyG6vIEco5WiSfiQlUiIeFPvCiPAXOFhwFsCknrNXHOc2s5D7cSHNyf/sn3eN/XCws8jvgnbZ9HWh8zzbFP1EbwpebP2B+sd7Pj4gQr+xr7Dyw1xDRHbBjgotAUCSnDFNhHy80SI+szUec9lrru8x4bXPu+VmCfguIGhMlpcytOt7NJUHeoZ4Sh4WUDrwgtcW1NF8meID/L7VqLWZ6zGZ17E5mlNkarnwXnyVmomRDwkb9UgjBsn9lFIJEQYTaekFmF7KYYS4G9uVmuS+NBF84EGZEuCpgjjahe2pPAUTA0BcS871+NTpZDetg3R3e366JjAgwXvRrRCEPFD8gb70tNxkJ7j+VuamInHAiHGc4kXGx7+2AlCKMCf7THX4rEOeLDxHeft2jbD5sbTnrAt1ra/gUhAmLmewJ37xM9Ds7ArgCB95d5MyhZINJ8UUoxIykNAkarLm5NBPaqVEEXSA3BoRwh0hwyxIeI64t04qUJj5PY4gyZmgYtiTCW2mNzYfIGuTNYk9h/3CLXzUMRtPmWueEvGuBThjfwJHb1sEyJs1Hg4I5GApww2klXWKetVMh6BGHJDcYjG41lrDbIh2sjM10qIyHNz7WYO0RTFN/ehhCgm4MQA8s9XtEbQUmAwipRsvJgD0rg1SH1sV6F1QQuzTzC294SqaIfYXu3SLLUJ0Y3tGlz2IV9s1/aRuF5lVN0HucPnEunYvfvuZMdsPx0SBWbMh/2WahIh2shs1kiIftLm7hea+cPrBKPpd4b5HEqIPNknVeECi6p7DcLWUcyHhc3Ad6+h4yP6iJHzr4TrsQ26jRVCSeySP7R/nt588TP2+aCEtlPd7hOq+kziYXerFiFKQSztHLxI3bbshnb8io7LRIjScK3tLBGijcx4bYToqM0bhtQeROwBdvzzrbkcSohQueMejUSvopKXStQM0U+CSuKFVYMQwZqAo56glTEzf7/aGjypB6KxM8mK3eD0EE4iROWvIu4Frq3DyBhD+EMiQlT+nC7RQxGiJVCfoM3aCBGxRjyTPVtabG21ZSghOqshFNTH8cMmmK+cVbY1Qw+2ysnLVZPgkg0pIkaNSxuHuK1y3E7CuyxFiCFE8DukHYco5fp4jjREfRFLOz/azX2xXbIvppTXJkKUhmttZ4kQbWTGayJE2InE5Jf7VORDCRFu3E9s1kWKgeZcS4icWO0cQ/yNp4sLWwXgUaMQAI8I1pEcY3xNstSvthKj6mJ39meJIEUNEYbccYsusYrPniZC1Bex7vNxX3fvPryE4u9h39XEKnMN4mvs+LrdzeiMChAQIdrIJNdEiMix5kEWD8WhGUqIsPHgwYWkuutPtYzwckMT9lMJN/qatsn24U10YUjR94QTmEu0PEQzR/pGII/rTRqiqVb68Hq/yS7FEQIhLg/Rw7skaojeZSdDpiVCQIRoI2ugFkLEVtADmzkjezLEyLMot6dyKCFCw+DaA4x0yTc0t2AYDBGKD/Z9fSBO0susbN2Aus8ctIP44XHnv5Hvs2NsrlJFNkSpSC1z3m2tWY/uTJwikut2SSRE7bQ+aJhYL2ibJHUhIEK0kfmugRDFN0Gm7VBCTr4fSogIpuaB0wjKRmLCOQS1PQEHIUIeNC62y02afFjcwGPE2ffP0bkVtkG07nacHwLwEa+IWFOpIhuiVKSWOS/aEw7REHE9pArbI9YFWkbkbCsE8ZTUg4AI0UbmugZChBs5MWCQFFuZoYSI+slE7sH3UoIz4rEUhfQQuyLcQq5Qz3vBtoXEoQQKdI+59pJ8lf0DmynKrqi7RNPepyXbyPIePIzoMeiVkN7gTCtPs5KiBZAN0WD4Z7kwhslIDXa5y6gazXN0RsBQm3vAJ2cZhRopAQERohJmIUMftk6I7mIYPSXgRLZyMtMfkjGEKAbQO2aN8FDcJwT4SzHkzDDNe6sgxQAk0VMD8NmVRmLK/pRSNyldCNi5T1KMcGVDVMps7u4HGtJLNF+Rnge7vy7Z52VGsM1Lhot/yY6JdyapAwERoo3M89YJEZoR16AQb4i4Q10SCdFb7WRP0Nl1Hd+T/4sknciumDZeR3sbL6Xuuc7BWDQSpBpJUgzESITqK1i5WmsCrm9/n3NgUrRlNteK7d8ONoQQVoRtbjzOUuSQ2z2/+5j/b23R6lPGr3N2IyBCtJGVsWVCRBh+jxmDJgRtTErC1SHZ7n05nGkHHn+IPEl4ee0T7HrY9tol2P24+BzRd9JFeJZrPom0TcJJEkpC/nYlitxVP1twbM+lzj9ZttlWwBOrK1bLgSGv4qt2IEZiFHHDw6CaTPUk/zxppcvDKJKqM+z8Z44YPVrNb2muV6TqEUA2lzIfz2iOX2yfKU4InN4Vh4i6btrUi8OCb9WP77FqKBkBciJiS4YQ5oT7pWSFCKQ+ENc2NMaFoSM5p5BHW/mJxEFEQoQRMsa0qRLfEtGs+EMs9fq5z8OzhizqsUCU9gmEDFKEQekfzN3ZmdobGoix3T22as9o1mHbVqzvUGIcIiKJEyqhJkGDg31eLmFu2E5HeGnh5SVFughRW/PLC7oXtjwAACAASURBVNnxlIp1zqoR4L6ITSbCPRWvRckKEdgqISKo3uOa+WDrgi2PVBljQ4RrNlnNkU9ZgVxEbU9qH5Y8L5Ik3px549klbK1BjCgQxy3ImECMU46fcA4eOLImDRExoMgdhwaUhw4Ew13lx+CNV9lVmgqO2echW7/YThch4lzI1f2bizyRrwysx8xW+ddG0wzCrRB2RbJCBLZKiLD9cRJ0bzt2cpQyRWMIEfXzY/jCpqG726dHr05pu8RzLm6d4kGAW/+uVCf0GQPVX7biwQxLHEdKn+LDrG8gxpT6h56z9UjV7rLu+OClheeW2+P5/9uxf4bgSd2ubeJlhe3jXV6Yu+pOIUTYLJIj7WhTQQ0G1hiU42jA2HkRRL7ISg0OGuRCdM9gxh5zIw5Zn7pmQQS2SIhub3i6vQY/SDxJUmyHfBrGEqL7WkVs0SGpAd8WXAK9mr66nQ0x4sHwlXuuxNMOe5u1kSO0eR+0cpFmXH0DMfYCsufJWyREpxkGpLshpY5vNxyChbAHrD3sdMYIdmAvaip4nX1iM5YqKYSIumoysI7eem0c0WyS7mbLwgsj9w0Eos3fkpUisEVC9HqbC9zrEQLsYQzcR8YSIoyc+YF4pOqtbnFAGAhi6FsPuzB+g/0TErUGQZOIlgtZOvVKG6+tECK2YwkiSlgDT3zbtTbQsP64lVSHga76ftZOcG/TvtqbVEJEH2KcI7wR8UrcmvDbJkn2ITnkbbsFPNiJYEcCwVHm8lsYVK1j2BohivnEUGOiHSLKcB8ZS4ho6xeseBwSAkNuOT3GRW18kL5bWNnlrTMkjxfbGh71u8/cjTkXOygCXSL3tPKEMZVlvpaHjhPLtRFsDMqdBPmLShseNLj8Xj1VCto67G7uZ2VMUtxd03DC/smaRNBOPb/HXPUhRJC/vwx1j0n4TMwyfmfYT6EBL0lIfoztnQvbRjxXsPty+XY7+OOSOp2xLzFlE96g+9Z4xiZV1VQIbI0QYfdxgwasvm9/jnEOQoQ7PW8LLrXEJHFydJYN3I2xcVnHdT1V8M7DW+fdVo6mXjTyvBiIkejdbAdixFuKREKEbc2DS+nYnn7gguwk6IZ7zmF7geCXxHlKNWoeO2zudxAvj02G7UufdCx9CBF9xSOT/III7UIc+rTHdWhYIFMIL3e70vOMxWXs9TiucM8j1MB3WcGOhq1zvFcRvv9WK1uMjM/6ZtwIoTb8+TMWU12/AAJbIkS89Z0IGBInBk+ovpKDENEmb3PkOkKIiXSnvh1Z8fnxrYlhHLfiMaEODYstK7auXIbOYV/oYswgvJoe1LeCic/n7fo6TRv0jT6WKGhcIJeQoV33FkimkyC2AecWMHRNRV+iTl/7EiKuIW4XBsYIBIk6+sgxO5k0PAhkKkbE7lPP3Od+gzUIKXKDecJ0QJa2Jqz15zaDIk0S61+yUgS2RIh407xlMw+/ap+8WQ2RXIToO6zxV4cOpOQ2G9LfUq9h7GDgctwODpEi1iKBJn1NvtOOU+1MxmCwLxDjmDpzX0uUdYz10RB4bK3cbfStDy0L2FEIRnitMHftungYQoQoGEcvJdEW603WiXb08a5+DSFEYBMNwc+wv/sG6fTQHSkpY7rGMOf37RQ42HeyTrYkMT0UeQ49vtWWxljNWLZCiNr79Wy5/NXAWcxFiGgeN2G8aZAa1am/aOOOATGP29+HSBH2B257wNZAX/uvIVOeKxDjkLbXdA2R3p0A+SdRu/cJHkZOgkqJ3Bs9ojDUfkzPCRhCiGjibCvYpSHYAxHs9QOJbcd72xCtVmIzk53W1vpiX4lt2FaEWFmPagbD/Y6/JStFYCuECGbuW1Jj1ZY5CREB2jwKLqpzD+8+93LBrRntyxLSJkX7PLh4uGJX4jYSc9gQlRqIcYl5areJRtOJD0axvGR0CYbQbBVDAAh+WJJg8P/CpkMETGR8fWUoIUKb9hYr7oFE8FbXZnf14aid4Fv/c/wmuvoz5PuY6Jjrt0SKsOl7YAMKEeSJJC9ZKQJbIETth9oxm4sxRpqREP2N1XXlEXMbQ/kvdTOjXTdqXsoLok2KeBh4RO8IL4bwvLm7EC8Gz8GphK2cGzWVlxSIcarxHqqXJMZRA5QS3Z2HPDY5lNdaweOoVEFb5fYdqYme22MZSoiop711lup1hqOCa0rJmcXfa5Tj1ukfDh0nWG60F2yPCRJ41EppITDa/cQblQC8CBkSHr/GyVGfz0VgC4QoPkRfYWPa59WSOueREI316oiECBdc97pI7cvY89C6RM0QtgiHtjn2tYc9DwbOYwhiTJfwHqtrX34vDIZjUlxSg7hx+lg82tfHkPt3tC894WfudkqsDzf+SIBSjHXZBnMCxKcHpCtxfLFP2F3FLSoMfodosMYQIvoTbZj4XbIdxgP/kHCPjoFl+f2uLR2Qj++4HURStC9wI4bY0das5OfUb1hf3VD+h+w4R2qZ0n9Pm+1fyQstBXTsGohX4w95tAkehTbl+l3nbE1DRLRukmMiQwhRDE1PHUPXTDuI2yGjafbk4148b/e3Hjqhe67j5oX9ELLmN+8UWPBywu0ZLyt+IzyIu+aRQIiR/HC8pEF0yjj3nYMdG1pK5IQVt+vrW+dYQsRvifuVB20FU7YjuwRjao+gPpdtXVefhn6PZpigri677G4iIcIz0cMkDG1zyuteYpV7nDmioBOQU7JSBLpuiqUPC+0B4fyRXGHTc9oQoQU52fQPbcShTPJTYI2BMnmV2FZEhmyZRUI0hFDFceEyHvfY2abxKK/t8bc1RajQ0VLlkuhqjw0AEYy3Iqw7J0B8pkQLJ0aMb33xSVqLrYjHtmI8eAFhczhEIok+YRUMIVbR9Z8+YG91r47OnLTvXaM6VyiKIfikXnPcToyaosfa3/cJF0dC9An7v+eGTK1/zvPYKvb0L3jV8rdkpQismRAdNcxjnKGhdgHtqctJiKibN20nQte149fMuFYiqcDDBruQvm/5BBrDvgYZS4ioI25TkffHE23ugqVNinj74i1srLBlQp45l0vZAfisVdiK5UEL+aE4AT40HtYlpJBI6hCgiMdacdjVbzQw/pvD6BsNy9A0IFFDdDIR5119ggBBAly6woTgMeshApjrGAF7rXMVt5oYQ3TJj4So9ISpxIbyvI5XteOuLdC1zlcV/V4zIXqyzdBdm1ki5o2H4x87cbkJUewnxt7HxnYw8XpuoDH0AF54BIjsKzGeENoctDpjBNsJ3+JkK6eLoE1hUxTtzqbYjhuDT9e1cfvLCVCK9yJaEgLleSnZALoLgz7fkwuNJLJIqiHzvvpj0taxv2Wu5wXJhfuE97Pd/gn7h9/fuD95oMY+OJR4bnv7DOIKJj9i5UlNh3O8hE05drb0PDkx4RRIASRZKQJrJUTRWBnoiYCKx1AOyU2Ibmyd4i0cIXz/BXJ0MqEObKlcmzLU2Jy8STGIHOH5xz5IP251uAq8S0Pkw4y54fjf2CSK2HFcrKn8Jvb50gQ8lzplyPYXGEfyw/bXx5YawILtYntC3B+PFH3Mjsd4oI61IYpQcB9gHcbtIIynn2Llbi3MojE29je/uyCmuZs+bhXG7TNyIhJk1+ORDQmgmbuP++r7OvuCkALInPf2ucZXXTtrJUQxKjWxRUh4mUtyEyLImj9w57IjijmUwIXosKik+wr2Rx47JVdKi7hllqIh8j63NUUE1Ysu+qlji3YgOTReqe2mnseWiGt+2AZL2f4itEIkQF0ZyFP7svbzzrABuOdgjgdrTkIEtiQxZtvfCRv/w3PPt2Ac/+N24KRhi96QbMl7DjA0xk4S0Wp+S8GLMGoMMa4mtIJkxQiskRCdbnhj++BybTvAfTOX5CZE9AuXX7QhhPCfMq4ObcUcavyNep0x9ZUYVJKbNA9mbDDGStQQ9SFEtIumA7LgQogFtF99ZEljarRSBASkkPzWj/kk3hUPwpTfZNz+ApMhOfv6YLbWc+Ncs549ovDQ8eQmRPQDUoSWgbWBhgiDb7aMosRoz0MibA8d71zXXX/P75gXAoh+qYIdmBvEn2nHDy+1o+pXGgIpN9+0muY7K97kjluzd8jc9BSEKHMX91bXNlQkSnaM6ZPaj/abKz96vGFyyBhCRPvnWHGCBxHAg4ptkRRpG1PnzC8Xyc4+0jPEW2br2188SFijeDMikG62hcZugaMV/dOwKHIYzk9BiLyL2NXFeENxPYPRw5p/8NDl760Jscl4SXAh7IaHSih1rPEFrfSt91IxLKpfayNERDhmu8yFPVy2dXLKWgnRswwEVLguQ8kQ18c3UryPiFuTS8YSIjRVhA/wFB997ImGGlNPRXZ2YQohYBuYN2NuuIx1S3IlGwyGs2wl8xBxW5E4xhx5/0jh8/1NpWMCisZ+TUmIDs0xLumed63tor6FtYFTAPeZo81gcptBTIERhtTYDblBNbHesAmTrBiBtRGiNxjWHu15qA1J13StkRBFA2rGN4YM4TqKvYUL6Q54uOSSsYSIfrSJMZGsiUnVJbj4o/1C/I2uTXbaW1loe4Zodnb1hQCQJDptF5J2+v84Z0viBAgSREnJIYaHUduwuC8mpBXxtDsPtmNyTo2VpQjRGdZxt4XCyYG/tyQESMW2D4FU4DRTSkLgfThjquHbeSXaIm5pfcw2ljURIlzscU1FeIvG++ZQDJuhIK6NELVthlKj3+7Dx9N08D3bFrzJ55QchIj+kAuJ3EEINlqX6OhkNKbmVLYnhqQx2dfMPrLDjd0Jz9bIzi4siKiMbRfaStZOV5Rh3JbxrOK37NtCZ4XjIWsvbo3yFo8GAmP+sbIUIcJpxD3LlkgBNBa3Q9cTgwlDcRcwximkdOHewz0IQTuPR65k5QisiRDh7u0eN1NmFY6EqHTm3yZDY7RmeHNAOKNXB0HtIFi55L5W0aNDZZBabAeGyFG7KBoTd6U0iLZnfdurUbPTByO0tpAgSlf0Zuy9iG3lBa0vwm8aIoSMJUQQqzObup5jnx7Nvs+Ydp27FCE6Zp3x2EOk8SBl0RaEuFB3DgPBO/KaKxlYNFHIaWO5kuFvs5trIUTR4+nvbSp4kMakpTlnJ+Y94ubt8WpytpGjLjzWopvnmG2yqH3zvqEOxssjl/BQittauIofHVn5K+16HsBddbWNqWOzaCggvnEbK25h8f8aNDt9psK1QE6CLnvgYn6nBNxjWxcS5ASofclD7R/upTOWEMXIzuTAI/hmDlmKEBGU8UQzgD42cznGPFUdbTKEtuXeUzU2Qb14BmLDiuD5Gg34J2hOVc6BwBoIETdf1Omuer+nHT9hQnAwxHSbmVLjYLQ1Q+TPIY/OEImRtLmeyLBEzGUbI5dAWiAvLjmMZvv0bZcxtQfIHJrGoU/7Wzi3jxaIMBjElqGk5naKCX0PRW3uwnKq7TLaLYEQjY2Q3YXf1N+j3YI8kEbIZW2G4tghurkGL1SkZtrnITg1nqo/IwJrIEQxgSsJKN0oNiMM56kKFS5vL0hXjqGp+nCoXt4QiRjtMnSbbNcWGR5NkKF9b/FDxotR7QkrxGFCyPVzzApzOZfsMqaeq+21tsPvjGB5kIAbWTlkCwS+kB9iQr3MCjZdfSUmyaQOIrwPkam2y+iLCNGQGfmfa9DWQX6+IlSzNjJE18luTyBGJLcmfRzCunoUAqUToova6NgiI9kfgjs4dihTCvEvPIAbmoWfnLKxnnW3Y6sM3SbbtUWGRqgdEK5n9045nbfBE1Z4a0fwIDlmBQ+guaT0yNRz4ZDSDlpGSBCB8mIAzF3X8pbvWqAcdma4xnuePAxWH5/S4R3nxO2y29j3zx1Yz67LRIiGgYlLOvNyyR2X58xDOax3/a+KpHttW339R1vRFaUTImwJMLZE5vKuiCkicrnr5lxSnl156JtJe4uMvkGEcm6R+Xh508fOxIUHLYEV55QlI1PPOc4hbV3OLnICxNzwArJP2BpAA4SRMkRoiBZoX904S3iOPGyOCHOAd1hfibY2eJVd2EoO7zLvhwhR+oxEG5tDV7HV5LF80mtf9sw4NrwpCYgr2QACJRMibs4YtHoMmLkWXsyOfQ9r/1cKnGc8qlKjM3v3d22RsTUGGcq5RebtES8luqLONX9xuqaMTF3gsujsErYOEB8KRIjs3IcEY2gIJURoyhQKUWNJImS2JIbI1NpAEaK0WUGjT06yFMkd+DWlzbHnfMoq8ICi17DjrQVPHYvPaq8vmRAtoR1iInkDZq8bWUtMjK4FiF0QRC/KFFtkXv8JO+Bt3YVtR7Yf55ahkann7ueU7RFlPJKgQ7GX0NJAgJwE4eI9h8So0myJszU+RH7eLrpfcyGeZf47HlLXrmtEiNKRRIMYbYX8SjSArhGCaEcNcnrty56JVsufnbxk5NRCLjuyylsvlRC1tUO8+eFZNYfEbR4Cy43NqTRHn7vawHMsylRbZLTBG9PrQ2PkI8Iuawmp0ZgaTSDhEm5hBZugXakxfC64kfNQchKEwfsS8q/WqGd8J7o09kRDBLdtJ1NTGOuKEPWbFbYsmVe2WxFChWAHiRB/DDu19/ercvGzCcPiKTo+YseeQmjxjqkD4xEolRAtpR0CUR7mPNQRwrPjQrxWeYF1/Oah81NukXkz5CH6nnDTI2bUEjL19skSY2q3ydsp5OfbwidRmQ8JWxSRBE0VzysVn9PtRAgZQpT0r029cMd5cxIiwkjQ9zkk2kat0e0eo2q8CKOrPdjFUBxz4JijDQz/nbC/zY49FlGOulXHwgiUSIiW1A4xHW8PN+UpksfOMeXEbmIbgq0Slzn26iFCECIXtBXEclpCojH1g6wDGMuvXXiwRPKTEjiTt3O0nqR+gAgNjQw+FXY4S3jy4DHxtOjfT4V5JlwHOe5yStQQESUdo/Q5ZM2ECPswvGFjdO19tpnco65ihYjVvIyWKGhd8YxDxqZJKnF8VfepREK0pHaIxRC3WYidM0W+tCkX3VGrHDIUU3BABiAFUwvaNA+9j83S3aducE/9WzGmRlsSNUA8LLqEqN2vawo3bAhHqUKC3d8LnRu7NR4JUa4M9xG7O9gfT2/+gcE5yWrnkEiI/toa7DKGn6NPXW1Acn/WSjueFOEa9oV08K19PnPmGezqa5/vY+BeNPBsTUs2gkBphAjtEPuzbnQ39gY5ZJp4o/b2CUbn+99D6pr7GrJEQ4bim+uP298Eb5xaoqcQtim4Ui9lH4Cnkt+IpzCuHYMlti1gxW+PRLe75Pz2T7wrUx4Kb7bzID5Ogth2WotEAk0wVHAZI2giSPODdKVzGdJOCTZEa8hlRgoWUrFEwVUdo/fje4DH3uhjzXd4cfEbKFGig0qONVviGKvtU2mE6Gk2E3dqZuND9rnLS2HKyWKryT1rMPTssseYsi996yYpLWQoqqZ5o913A+pb/6HzIZBsIVymOYmb4U/nbKBHXRAJco85qf0BO/7tHtdPfSrrmq2vIcKbs5Mf/3QDzyH1LXkN2kNPwQOBJvo6YTbGSLRf4wFMlPScshQhihpPXjIulXNQGesiXAKk9KqtOlMCyEZCVPK9NyYhnkvznnGKVNUhBEojROwhe1Rj4v+w1zynHLXGPIM6thZLGQT3HXNU43Itb1i3shLtefrW2ef8GLmVN3O0Q23Ptj71jTn3AXYxqnqkxLfpmCW7a5wYPOPlGLfAmNu1C55HaLI8nUuuAKjYV2Goi2fdfaycnRmopQgRBMjJIu7sl8g8rrHV8fJBJOr2li5zwTZm9Drd19ZaCBGRqYmkjowJETEWc10/AQIlEaKYloI3RjQdn5hgzIeqZMvJjYBP2jEP9tLlLtbBGGUaDQRkyA3/pu7/pa0BSKS7d9MfNH1LCalePEUAeenIR1ei4L7bJXPme+vqS87vYyJXnBii91HOdnLXtRQhwpvQkxCXtp0ECSX6fbRrYisYItQn/cpaCNGzbVykhEGWCDabe02rvoBASYSI+CG4zSLHrbDdM7cQJAyPHARjan+Dnbsfqe0RhI59eZe32sEtrcwZTyaGKThkMJk6pjHn3dMudq0AQQZjEtwx9erafAhELx1qXdNDZSlCBE7Y10AaEGwt2RZeWgi4+gutTqDlZ+usr/3gWggRqYcwT0BIekwaG8lGECiJEEXbiqUWWkzsesLm+LSC55ltIbaHXCAjkCE0JHMJqTlI0eFCzKMXzdX4jnYghK5tkDp7wYk40HR8oExh5zPlqJckRGhhjzaDw/twSeN5XhQxaWCr3oXcc7yQDNUO5yZEmDtgU4m3G+Ecct3L2bp0Gy52FNgqlGwEgVIIEVs8eAMh77By+YXwJRq2xy65mx0/aaF+dDVLv2JmeryqIENzbjFyM8BLCE+8peeN9s+w8oymL5Br7CwIsS8pC4E3WXfc6BZCHzWcZfX01N4sSYiIzXP1pktLBowl1hhkKBp2EywSI3lCAgyVXISI7UXuh20ClOtZh12fe3+igfakxEPHresKQiDXIhk7pOdbBR7PgThEGOkuIbgwu2Eg8V+mTGg5dHwx7xN1TBGArqtv2AtBhr65OfEt9kn8oTkJWbuPROH2/uAJ8oiuQej72RGIDz0aJ+0B6Q/WIksSohhKgvhNL10ANLyqsA2KkuJBltLVuDawl7pgykWtc9BY49162db/x0ZA9+oI1ItWE8HOFfIl2RACJRAi3uTjfjOusr7o5oT6AtbYJ0ODuG8v+YDfNXaMpzFadsEAPAZgnAsvtsliJvsl31gZc/SyY85YUx7TZC5M1E43AjEQIwEjncB2X1nGGUsSoqi9/mGD49dmhIR7MlqhY6FN7tlohXJ5srIrQCoMBM2uh81IGSbriBcgck9GoY/EDSKHWg4B9+NNRdTpKYpy1K06CkCgBELED43tKWRJV3c0HJ63bIoot2Onu02GlgoK1jakLMGTC5W9Rw1mCybaVo3FXdfnQ4AXHc/9hBckEZjXJEsSouh0MlewVeYG5xbu0bwwuqDRhwx9MOPkRbKxL9YSHq0EnfWCFzBG+u3wKO+1/xEcMreHaXxW5QoVkRFCVTUWgRIIEaH7eXNEMMiLGpCx4+tzfXRfx7WSm18p0iZDU2TyThkrxu7ExXFZqh+xr+0+YdvQ18MlZew6ZxwC97LLWS8ua/Iu8z4vSYjmDgjIthOmC3dsTTuerW3PsjErA7MEtNzY/bldEtovjJUhPpAeJ0ApW1S/aOeDlYcpGNO39rVxW/4G9qUnJc7ZhupaEIESCBFsHuaPYDTIoltCiJnhgSBLMvYshQwR2wcXe79pzZnt+9B6eIl9iZsvQuRjPF0kZSFAkmHck/1+80g75g17bbIkIUKLjoYCIe4PW0FTCYQCMhS3rbCvRCuEx1YOIQAvWnk03TmElyC2sKZ6fhBMlAjaLmQxiH/nGIPqWBiBpQnRlW38GOQixNUgvsZSQrLGb28aZy86akKW6lMpZIjxR6NO3uYIpHlyKWCadgkKF2/QV7S/cb2XlIUAGgW2WpGSU090oRYJEdoBtARzSfTE5cWE319uIXckRKgdvwsPN5xMhuR1hDhcrSm4wPuxe6f2GQP3HcIP4NlF8WO27sZ4uKX04XQ7yTVCa7R/Sxlj9ecsTYhubTPwnGYWWNwxKenckxMDn6EN+Ye5O9BqryQy9Crr27HQP97EchkqjoH5uXYxucoQDL3PGFOZrp0EAX7TkFSPZD7Vw3ySzrcqjYRo7sCfBAMkhhOCZx4eerkE+zvyD/LQj0JsM8gsNkMpwjabEx7/7BtChXhG3G/apIe/l3SUeKC1j2YTIeyJ272m4KJzVoLA0oQIwzfPioxKHXuQJSS6U5bwBlsSGUJbFl18uTF4rrAl5srb5IYbg6Itud26JA6lt33COhiNp/EITH3AljY2HAh8i4fkuq5RnqOfkBacB5BcL494Y6L1QFMfhXxp3JsPxWFjy6tNfvokLWYMRLWmEBD3QqEDJYY8waOX2GsIhubH55h0tTEvAksTIjLbe2RTPAK44SwhaBnQNiBsDblNyhJ9iT882l/ScBkbAtKAsBWFvNvK0SVA2dEm64b1gzzPCoEp5xDa/UErvLG2s3rP0f7a2iCA6BOtkOwXw2rPcL+2cdDfJW2IIiHK4aFH8ls0HpGIMEa8NCFDHoLEt7zidhdEKHXLCxd6Jz4E5fRjz9NHkEOCHUbB0BpSVJLEgIzkbZt6i66ksVfTl5IIEZGq2UJbQrgxoPlAcgUaGzIOtGXcjFzIrOz53YbUN/aa6OqLupo3yTlTg+zrPzYORDR3OWYH/vY8dsxd15O4khhVSClbh119Xvp7iHX7obd0n4a0vwVChIE7wQuJHRYFooI7P2s72vyQJiRV2MpzwhM/D839Reyaj+5ogPvgmakNT3zeTa1+NxHg9+855SZuVtXPjcDShIigXh7cCk3EUm/c0fWft3+3a5pzPi5jjYEBb2TI0oZ77a0ytDFPnxOQA21FokYy3hvP2C/ced39tySN2YwQVNvU2glR/N34JBJx+VPNmnY7r5QJjlteTn6GpLGIhIjfVox3VMrWGbsXHn7gUXZ8/xSAdM76EFiSEMGyP2zFf4RLbg1F13+0IARmnFt+2xr0bZ+327EnKZ27H7TX3ipbUnvXHv9X2D8wePe1y9sbrvdzCd6H0dZt7qjBc41T7ZyKwJoJ0Wk2HEJl9BW2OqO2B7s9/ubenUMiIcLTmOC4N2wqxqj7W3M0MrIOxvolTR30h35JNojAkoSI4Ie3aTD9J/vsY5CXcyp4wGJEiODhEN9QcrZzqK5orMl5aDzQfCwl8U0SdTb53UrYKgMPjEDdG2Zuw1afj6gluq/9E7wk20dgzYSI2SFuDvF09gnrmod9e9tryu3ONiEilAaacpelt87idtnSL6rb/4UtPMKlCFFbO0TAMQ+KuAQkvAUhud1ZU8bS3ip7jF3EXv5SUvJWGZhEMgKhdmP4OfGKMatK0p7NiUGNbZVCW6udwAAAEQpJREFUiCAo2D0OSYKNWcK/7Zi8IdtdOdZAmxARi4773y+FypfcOtN2WY5ZXkkdSxGiGKgNdeTFFsTrFta2uwETJNKz3c/VpZjfCc8F2neCNlcfvJ2St8q8j/Etd6l4Ubgc8xbtQqT1UjRoc6+ZlPZ4oGGUysMP7yIMerFBWZuUQojAben7Zq6520WIqBsNeQlbZ9ouyzXTK6hnKUJEQr4TVnC5JPcMcSiWEn50vj01dywkcvYQHdZl6QjZJW+VOUbEiSJ+CrIUIaLtuHVXSmympX5Du9olZgtZyCE/Z1iJBrto9Xy7vKQ+d/WlJEK01PZ+F0Z9v99HiHBtj1tnS2SXv5n14UXNgLRd1ndmV3j+UoTIocJw+G0L47YUISIf0V3D2DEmbLvCzgnNWdYYOYxcSvIqiziUQohub50iOjaylbf1MeuNODnE84I08JDjZWefrNUQfUlCxIsTL1BRthAPp+1ldsEwQBLJEhfJhRfpXLnUUtY6oTxY14i8y1IQW/k5SxOiEuBbghDhqUGsDxdudDwkUoQfKdmheWMiREAOaZOhudMS9BlDKYSIPvOWfv6m8wS6i9nc+4xpjecS3R3jdgqxbTxcxKGxoIklUCPhCtYoSxIibGuwcYwCSfr1NQIZ+hwJEUEcY0JZTsPhBccXhMStRKSfS8jd5v1Z0o5prvFW344I0bn71HNumUWbIRbg2VaI4JsiX20nufFjLpV5mwwRkwlVcalSEiHCjRl35iVu1nPPD27HToD47MpRxYOMt3kirxPWYu0PbvC+nZVnNcAvEdGe7Zv42yTcBF5Qa5ZIiIiJ5DG+fExEyX+zFd9ynUtTg7aeiNnIJ6x4MNY1Y62+dyAgQjQvISI7NnZKLmSRvmaPVRrfEnfdPHpU9ZlTYy45/l6Dx1RJhIhQERhTexoDIq2D4Vh5kFXgnoYeEmJonWxB0E+C7w1JWIwGDJutrgfCO+0cko+iwXyFlX8c2uGCr4vOILnyifUZLlvavMC4EBMLu8M1yz4bojgmPJAfH/5BHLB4H51i/A+2SonojbA1fsYUjajOshAQIZqXEDH75Agi1hFvPXgr9RUebP62xNsUxGiIrJEMMc6SCBH9IdHtA5oJgLy4wfeQOfFr3mMHhGMoWUhhAAHCuJzPJYKZzo3PraxBJ7xLeKQy3hh24oT97RrKubHI1V4KIaKt37XyvU2jeONy75wyPtKrrP5jTXtoBn8j14BVT7kIiBCdlxDNlS4DNTBbZ0Mk7qlfyiqAIPSVtZIhxlkaIUL7wpy4EfHN7dg9U/rOi5/PzRd7ldKE3wfbNBCgE6V1bob+EMOHBKUIBLCdJX6GLhxB43ctK3hgEdB27ZJKiL7KBspLpOcR62Nq0BcjglcS3sOFl5yxmtq+fdD5CyAgQnReQvQhmwM34FtgOpKa5EaIdwnCWxI3iT6yZjLEOEsjRPSJB5PH0uINnjnpsxW6b/5yPXDpm2cX77NW/Fy2y05a2eI2WB88wNFJCBHcPZ1Dnzp07nkRSCVEXEU+MQIluqAxIh9mbuGl5gVNpX9hn9fI3YDqKxMBEaJz81KxF4+gtblSmVP12V6dsKPvbP66nn2i2k2VaAPBNWuwGWqPjQcRN1FkyThEsV94/2DfFQUXdPLTSbaBANvUeIdCUv2+iSZhV9TnbYx4nlH0IUT0KGpPT9rfaO3Yvs0pj7PKfqypELd/3xLP2YbqKhABEaL5bYjGLoPnWQXf11TS56Hb9ibjZoLX2tok2lChRsfeZmmJUXW9L8Qpco+kpfun9scj8FCrAu1qFDzt3jG+6qpr6EuI2KJGA+uafDRG5ILMKdiHuXYWL2ScBCQVICBCtD5CFAM63s3W6JMS1mmbDGEIi8fbGsVvVuzxp8S+mWuM2HMRgZnklMQvcc+zudpXO9Mi0M7xR2toal89bbObrz0SIhxOLpQwYqKckxzcBXu7+HdCFXtPuZx9g8ckQmgT+kN8JEkFCIgQrY8Q/Yyty59q1iZuuI/Ys06/1v5PZNd7W4lBINe4TdYeIrnD3lfo77PkvhUK2Wq6FQNx0umlkguvBrCEjnYFZtxXxVPtC9cMfdCOsafEBnSskD2Al07kpVZuMrZCXb8eBESI1keIYiZoco/dt7Xc8HBCzdsOcMZpa80htZ5flHq6ZQRiKgfGyW+P36BkOAJdgRn31YyhPw4mR5sTftM+bzu8G5+9khRK7hDBvfYxGepUFStBQITovIRoDR4FpPg43qwvov/G5LCPtL9JNLpLTto/12gztJKfkrpZAQJESY6JqKewX6kAxvMMsa8NUbw4eoPx/xz5F4lt5CE00Kx7mIXa5qXK8YoQnZcQEckXz6WSBRUu6TWQGKmWrTMiHLtgfIwxIDYOuFw/reRBqW9CYAUIfL/18XdCP5eIVr0CmHp1cQwhoqHoEYZdIV5nQ3PlfY1d60byOTIB9AJCJy+PgAjRkSN3sGl4ejMV5F0icWrJQlC2P206+Gn79GitHr2ar7aQ46jkOVDf6kQA+zDysrmUnAR5LTM0lhChzcHrzL3CiB90i4GDj5HIeZnE9EBSEQIiROdqVdwwea7EgWOWGMaDbzxQwXPsux8c04CuFQJCYC8CaB8u23xL/KkvFVajEBhLiGi8nSOSGEIx91lqB2MaHsUfSkVtQ+eJEB058kSbzx9t5pSs84SEL10IRLYr2SbuoniXSYSAEJgGAYx34wvHl9nfY6KAT9PL9dSagxAx2p+zcv9m2IS94MWxb369GH8oV6Lm9cyEevrZiKs1Q/FiG/xNGwBQtXrI9tIxwaCQvEZOatkyUyDA0mdN/Vs7AmgeyL7ucm07wDNJMgyBIXGI9rX05/bF1ZsvCZZ6455dwn4IOyKEjAVD8032bFanl4KANERHjpCw8hubCSFnDZ5mEiEgBITALgRuZv+MyXuVCX3cOhkah2hXqwRFfW344n52TLqiVMGQ2gOqkqeONEGSihAQITo3YSVqb0RZjSta/BqqEBiIABGVL9Bci9cZxriSYQhEQoRn7PmHVfPZqx5mR2eGOnCSwVkmRWKeRBGiFMQ2dk7thIiw7J6cUW6WG1vcGo4QmAiBqFUmOrLn1ZqouU1Xm8uGKIJEqBGi9COvsXLdRAQ/YedhhoCIECWCtqXTaidEV7DJfGszoWuIQbSltaexCIG1IoBWiJhEyBpCdZSM8xSE6JttwG8Ig36hHX9vAggxKCNJZP8p4RqdsiEEaidE17O5PKeZT7bOLr6hudVQhIAQmAYBYoH9iRXun88P5Gia1rZd6xSECMSI2E/kfhcILHN1SGRDtO211jm62glRDP2OpuiKnYjpBCEgBITAufaGeCK9UmCMQiCnl1m7IyR99ZdcTCOuY+VQKg7ZEI2ayvVfXDshIhIp7pnIy63caP1TqhEIASEgBFaDQE4vs/agIUNsnRFhHCGgLaQIW6FdIhui1SybaToqQvQ/hIgfjsewmAZt1SoEhIAQEAIRgdxeZm10IUB/HP75PDu+5Z4piDZECrhZ4TqtnRARmfqxzbx/wD5Rg0uEgBAQAkJgHgSmsiGKvb+9/fHM8I99aTlkQzTPnBfbSu2EiLcA3GbBQQlRi12m6pgQEAIbRWAOQgR0Z1l5SMDwLnb8tBamsiHa6CJLHVbthAiciEVEuHYyJkuEgBAQAkJgPgTmIkSMqJ2H7jT734kwVAyveR4gikM03xoopiURomKmQh0RAkJACFSHQCREGDXvSlqdCxTyPb7OCimakPdawcbofc3fsiHKhfRK6xEhWunEqdtCQAgIgQ0gEAnRp208nzvxmAitAilCA4S8ygrx6BDZEE0MfunVixCVPkPqnxAQAkJguwhc1Ib2kTC8OZ5JN7X2XhzafKod39WKbIi2u86SRjbH4kvqiE4SAkJACAiB6hC4jI34PTMTIpq7j5XHhHZfYMfYFKGxQmRDVN1SPNe7SiIEhIAQEAJCYCkEPmYNX9gKNj2XnbETj7e27hHaw4bofCJEM85AYU2JEBU2IeqOEBACQqBCBG5nY/71Bcb9fmtzV/y5i9n/P7xAf9TkggiIEC0IvpoWAkJACAiBxRF4jvXg1q1eEIrl7xbvmTowKwIiRLPCrcaEgBAQAkKgQASOWZ/wOHO5nB28q8B+qksTIiBCNCG4qloICAEhIARWgwBu/y5H7eDdq+m5OpoFARGiLDCqEiEgBISAEFg5Ahh1X9rKx61g5C2pDAERosomXMMVAkJACAiBvQjc2b5p5zgTXJUgIEJUyURrmEJACAgBISAEhMB+BESItDqEgBAQAkJACAiB6hEQIap+CQgAISAEhIAQEAJCQIRIa0AICAEhIASEgBCoHgERouqXgAAQAkJACAgBISAERIi0BoSAEBACQkAICIHqERAhqn4JCAAhIASEgBAQAkJAhEhrQAgIASEgBISAEKgeARGi6peAABACQkAICAEhIAREiLQGhIAQEAJCQAgIgeoRECGqfgkIACEgBISAEBACQkCESGtACAgBISAEhIAQqB4BEaLql4AAEAJCQAgIASEgBESItAaEgBAQAkJACAiB6hEQIap+CQgAISAEhIAQEAJCQIRIa0AICAEhIASEgBCoHgERouqXgAAQAkJACAgBISAERIi0BoSAEBACQkAICIHqERAhqn4JCAAhIASEgBAQAkJAhEhrQAgIASEgBISAEKgeARGi6peAABACQkAICAEhIAREiLQGhIAQEAJCQAgIgeoRECGqfgkIACEgBISAEBACQkCESGtACAgBISAEhIAQqB4BEaLql4AAEAJCQAgIASEgBESItAaEgBAQAkJACAiB6hEQIap+CQgAISAEhIAQEAJCQIRIa0AICAEhIASEgBCoHgERouqXgAAQAkJACAgBISAERIi0BoSAEBACQkAICIHqERAhqn4JCAAhIASEgBAQAkJAhEhrQAgIASEgBISAEKgeARGi6peAABACQkAICAEhIAREiLQGhIAQEAJCQAgIgeoRECGqfgkIACEgBISAEBACQkCESGtACAgBISAEhIAQqB4BEaLql4AAEAJCQAgIASEgBESItAaEgBAQAkJACAiB6hEQIap+CQgAISAEhIAQEAJCQIRIa0AICAEhIASEgBCoHgERouqXgAAQAkJACAgBISAERIi0BoSAEBACQkAICIHqERAhqn4JCAAhIASEgBAQAkJAhEhrQAgIASEgBISAEKgeARGi6peAABACQkAICAEhIAREiLQGhIAQEAJCQAgIgeoRECGqfgkIACEgBISAEBACQkCESGtACAgBISAEhIAQqB4BEaLql4AAEAJCQAgIASEgBESItAaEgBAQAkJACAiB6hEQIap+CQgAISAEhIAQEAJCQIRIa0AICAEhIASEgBCoHgERouqXgAAQAkJACAgBISAERIi0BoSAEBACQkAICIHqERAhqn4JCAAhIASEgBAQAkJAhEhrQAgIASEgBISAEKgeARGi6peAABACQkAICAEhIAREiLQGhIAQEAJCQAgIgeoRECGqfgkIACEgBISAEBACQkCESGtACAgBISAEhIAQqB4BEaLql4AAEAJCQAgIASEgBESItAaEgBAQAkJACAiB6hEQIap+CQgAISAEhIAQEAJCQIRIa0AICAEhIASEgBCoHgERouqXgAAQAkJACAgBISAERIi0BoSAEBACQkAICIHqERAhqn4JCAAhIASEgBAQAkJAhEhrQAgIASEgBISAEKgeARGi6peAABACQkAICAEhIAREiLQGhIAQEAJCQAgIgeoRECGqfgkIACEgBISAEBACQkCESGtACAgBISAEhIAQqB6B/wYT2uX/EFFOzAAAAABJRU5ErkJggg==";
}




async function handleClose() {
  closePopup();
  // emit('reload');
}

async function handleSubmit() {
  closePopup();
  emit('reload');
}
</script>

<style>
</style>

