<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym" @click="batchExportInCard()">批量打印教育卡</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)"/>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload"/>
    <threeLevelCardList @register="registerCardListForm" @reload="reload"/>
    <batchThreeLevelCard @register="registerBatchThreeLevelCard" @reload="reload" />

  </div>
</template>
<script lang="ts" setup>
import { ActionItem, BasicTable, TableAction, useTable } from '/@/components/Table';
import {testEduThree<PERSON>ard<PERSON><PERSON>ord<PERSON><PERSON> as threeCardRecord<PERSON>pi, userMaster<PERSON>pi} from '/@/api';
import { usePopup } from '/@/components/Popup';
import Form from './Form.vue';
import {userInfoCol} from "/@/enums/testMaterialEnum";
import { genQueryDept, genQueryInput, genQuerySearch } from "/@/utils/tableUtils";
import {ref} from "vue";
import {useMessage} from "/@/hooks/web/useMessage";
import threeLevelCardList from '/@/views/testManage/testMaterial/threeLevelCard/threeLevelCardList.vue';
import batchThreeLevelCard from '/@/views/testManage/testMaterial/threeLevelCard/batchThreeLevelCard.vue'


defineOptions({name: 'testManage-examRecordManage'});
const {createMessage} = useMessage();

const selList = ref<any[]>([]); // 选中的试题列表
const [registerBatchThreeLevelCard, {openPopup: openCardPopup}] = usePopup();

const [registerForm, {openPopup: openFormPopup}] = usePopup();
const [registerCardListForm, {openPopup: openCardListPopup}] = usePopup();
const [registerTable, {reload}] = useTable({
  api: userMasterApi.page,
  columns: userInfoCol,
  useSearchForm: true,
  ellipsis: false,
  clickToRowSelect: true, // 点击选中条目
  rowSelection: { // 选中行
    onChange: (selectedRowKeys, selectedRows) => { // 选中行change
      console.log('selectedRowKeys', selectedRowKeys)
      selList.value = selectedRowKeys;
    },
  },
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('姓名', 'realName'),
      genQueryDept('部门', 'organizeId'),
      genQueryDept('身份证号', 'identificationNumber'),
      genQueryDept('联系电话', 'mobilePhone'),
    ],
  },
  searchInfo: {
    '_sorter': 'f_creator_time DESC'
  },
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record): ActionItem[] {
  return [
    {
      label: '培训记录',
      onClick: showEduHistory.bind(null, record),
    },
    {
      label: '三级培训记录卡表',
      onClick: showThreeLevelCardList.bind(null, record),
    },
  ];
}

// 培训记录弹窗
function showEduHistory(record:any) {
  openFormPopup(true, record);
}

/* 三级记录卡弹窗 */
function showThreeLevelCardList(record) {
  openCardListPopup(true,record);
}


async function batchExportInCard(){
  console.log('batchExportInCard selList',selList.value)

  const cardInfoList = <any>[]
  for (let i = 0; i < selList.value.length; i++) {
    let userId = selList.value[i]
    let _res = await threeCardRecordApi.page({userId: userId});
    if (!_res || !_res.data || !Array.isArray(_res.data.list)){
      createMessage.info("当前用户无教育卡")
      return
    }
    _res.data.list.forEach(i=>{
      cardInfoList.push({
        userId: i.userId,
        id: i.id
      })
    })
  }
  console.log('cardInfoList',cardInfoList)

  openCardPopup(true,cardInfoList)
}
</script>
