<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="" showOkBtn @ok="handleSubmit" @close="handleClose">
    <div class="fa-flex-row fa-m12" style="font-size: large">
      <div class="fa-zz-card-right fa-mr12">{{userInfo.organizeFullName}}</div>
      <div class="fa-zz-card-right">{{userInfo.realName}}</div>
    </div>

    <a-table :columns="columns" :data-source="userProcessList">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'key'">
          <div> {{ index+1 }}</div>
        </template>
        <template v-if="column.key === 'eduClassName'">
          <div> {{record.eduClassName ? record.eduClassName : '/'}}</div>
        </template>
        <template v-if="column.key === 'eduNum'">
          <div>{{record.eduNum ? record.eduNum : '/'}}</div>
        </template>
         <template v-if="column.key === 'eduName'">
          <div>{{record.eduName ? record.eduName : '/'}}</div>
        </template>
         <template v-if="column.key === 'creatorTime'">
          <div>{{record.creatorTime ? formatToDate(record.creatorTime):'/'}}</div>
        </template>
         <template v-if="column.key === 'isFinish'">
          <div>{{record.isFinish ? '完成' : '未完成'}}</div>
        </template>
         <template v-if="column.key === 'formatStartTime'">
          <div>{{record.isNeedTest === 1 ? '是' : '否'}}</div>
        </template>
         <template v-if="column.key === 'testTime'">
          <div>{{ record.isNeedTest === 1 ? (formatToDate(record.testTime) ? formatToDate(record.testTime) : '') : '/'}}</div>
        </template>
         <template v-if="column.key === 'score'">
          <div>{{record.isNeedTest === 1 ? (record.score ? record.score : '') : '/'}}</div>
        </template>
         <template v-if="column.key === 'isPass'">
          <div>{{record.isNeedTest === 1 ? (record.isPass === 0 ? '不及格' : record.isPass === 1 ? '及格' : '') : '/'}}</div>
        </template>
        <template v-if="column.key === 'action'">
          <a-button type="link" size="medium" @click="fileDetail(record)">材料详情</a-button>
          <a-button type="link" size="medium" @click="videoDetail(record)">视频详情</a-button>
          <a-button type="link" size="medium" @click="delEduRecord(record)">删除该记录</a-button>
        </template>
      </template>
    </a-table>

    <EduRecordDetailDrawer @register="registerDetailDrawer" />
    <eduVideoStudy @register="registerEduVideoStudy" />
  </BasicPopup>
</template>
<script lang="ts" setup>
import {BasicPopup, usePopup, usePopupInner} from '/@/components/Popup';
import {reactive, ref} from "vue";
import {eduProcessApi, testUserEduRecordApi as userRecordApi} from "/@/api";
import {formatToDate} from "/@/utils/dateUtil";
import {useDrawer} from "/@/components/Drawer";
import {useMessage} from "/@/hooks/web/useMessage";
import {useModal} from "/@/components/Modal";
import EduRecordDetailDrawer from "/@/views/testManage/eduRecordManage/EduRecordDetailDrawer.vue";
import eduVideoStudy from "./eduVideoStudy.vue";

const emit = defineEmits(['register', 'reload']);
const [registerPopup, {closePopup}] = usePopupInner(init);
const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
const [registerEduVideoStudy, { openModal: openVideoModal }] = useModal();
const {createMessage, createConfirm} = useMessage();

const userProcessList = ref<any[]>([])
const userInfo = ref<any>({})

const columns = [
  {title: '序号', dataIndex: 'key', key: 'key', align: 'center',},
  {title: '培训编号', dataIndex: 'eduNum', key: 'eduNum', width: 150},
  {title: '培训名称', dataIndex: 'eduName', key: 'eduName',width: 150},
  {title: '参与培训时间', dataIndex: 'creatorTime', key: 'creatorTime',},
  {title: '视频进度', dataIndex: 'eduProcess', key: 'eduProcess',},
  {title: '培训状态', dataIndex: 'isFinish', key: 'isFinish',},
  {title: '是否需要考试', dataIndex: 'formatStartTime', key: 'formatStartTime',},
  {title: '考试时间', dataIndex: 'testTime', key: 'testTime',},
  {title: '考试成绩', dataIndex: 'score', key: 'score',},
  {title: '合格分数', dataIndex: 'passScore', key: 'passScore'},
  {title: '是否合格', dataIndex: 'isPass', key: 'isPass',},
  {title: '操作', dataIndex: 'action', key: 'action', width: 120}
]

const searchInfo = reactive({
  userInfo: '', // 右table所属左treeId
  // _sorter: 'f_creator_time DESC', // 按照创建时间倒序排列
});

async function init(data:any) {
  // searchInfo.userInfo = data;
  userInfo.value = data
  reload()
}

function fileDetail(record) {
  openDetailDrawer(true, {
    eduId: record.eduId,
    eduRecordId:record.id,
    eduRecord: record,
    userId: userInfo.value.id}
  );
}

function videoDetail(record) {
  console.log('videoDetail')
  openVideoModal(true,record)
}

function delEduRecord(record){
  console.log('delEduRecord')

  createConfirm({
    iconType: 'warning',
    title: '删除',
    content: '是否确认删除选中计划相关记录？',
    onOk: () => {
      userRecordApi.remove(record.eduRecordId)
        .then(_res => {
        reload()
      })
    },
  });
}

async function handleClose() {
  closePopup();
}

async function handleSubmit() {
  closePopup();
  emit('reload');
}

async function reload(){
  let _res = await eduProcessApi.getAllUserStatusList(userInfo.value.id);
  userProcessList.value = _res.data
}

</script>

