<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="" showOkBtn @ok="handleSubmit" @close="handleClose"
              defaultFullscreen class="jnpf-full-modal full-modal">

    <a-table :dataSource="videoList" :columns="columns" >
      <template #bodyCell="{ column, record,index }">
        <template v-if="column.key === 'key'">
          <div> {{ index+1 }}</div>
        </template>
        <template v-if="column.key === 'name'">
          {{record.name ? record.name : '/' }}
        </template>
        <template v-if="column.key === 'duration'">
          {{record.duration ? durationStr(record.duration) : '/' }}
        </template>
        <template v-if="column.key === 'watchedTime'">
          {{record.watchedTime ? durationStr(record.watchedTime) : '/' }}
        </template>
        <template v-if="column.key === 'percent'">
          {{ formatPercentage(record.watchedTime, record.duration) }}
        </template>
        <template v-if="column.key === 'action'">
          <div>
            <a-tooltip>
              <template #title>已观看时长大于0时，进度方可变更</template>
              更变后台进度：<JnpfTimePicker v-model:value="record.formatWatchedTime" @update:value="updateProcess($event,record)"/>
            </a-tooltip>
            <a-button type="primary" size="medium" @click="videoWatchDetail(record)" style="margin-left: 15px;">人脸识别详情</a-button>
          </div>
        </template>
      </template>
    </a-table>

    <eduVideoWatch @register="regisEduVideoWatchModal"/>
  </BasicModal>
</template>
<script lang="ts" setup>
import {BasicModal, useModal, useModalInner} from '/@/components/Modal';
import {ref} from "vue";
import {testEduFileApi, testUserEduRecordVideoApi as userVideoApi} from "/@/api";
import eduVideoWatch from './eduVideoWatch.vue'
import {formatPercentage} from "/@/utils/formUtils";
import {durationStr, durationStrNew, timeStrToSeconds} from "/@/utils/dateUtil";
import {useMessage} from "/@/hooks/web/useMessage";

defineOptions({name: 'eduVideoStudy'});

const emit = defineEmits(['register', 'reload']);
const [registerModal, {closeModal}] = useModalInner(init);
const [regisEduVideoWatchModal, {openModal: openVideoWatchModal}] = useModal();
const {createMessage} = useMessage();

const userId = ref('')
const eduId = ref('')
const userEduRecordId = ref('')
const eduNum = ref('')
const videoList = ref<any[]>([])
const userVideoRes = ref<any[]>([])

const columns = [
  {title: '序号', dataIndex: 'key', key: 'key', align: 'center',},
  {title: '文件名', key: 'name', width: 500},
  {title: '视频总时长', key: 'duration'},
  {title: '已观看时长', key: 'watchedTime'},
  {title: '当前进度', key: 'percent'},
  {title: '操作', key: 'action',width:400,},
]

async function init(data: any) {
  console.log('data', data)
  userId.value = data.userId
  eduId.value = data.eduId
  userEduRecordId.value = data.id
  reload();
}

async function reload() {
  // console.log('reload')
  const res = await testEduFileApi.getFileDataByEduId(eduId.value);

  if (res && res.data && Array.isArray(res.data)) {
    videoList.value = []
    // totalWatchTime = 0;
    res.data.forEach(item => {
      let videoArr = JSON.parse(item.fileScreenshotLink).map((v) => {
        // 从服务器获取每个视频的观看历史位置状态
        const duration = v.duration || 0
        return {
          ...v,
          eduId: eduId.value,
          eduNum: eduNum.value,
          eduRecordId: userEduRecordId.value,
          userId: userId.value,
          videoFileId: v.fileId,
          videoName: v.name,
          duration, // 视频总时长
          // watchStatus, // 0-未开始/1-观看中/2-已看完
          // initialTime, // 指定视频初始播放位置，单位为秒（s）。
          watchedTime: 0, // 记录已经观看完成的视频时长
          formatWatchedTime:''
          // watchedPer, // 观看进度百分比
          // duration: 0, // 视频总时长（FIXME: 这个要后台每次上传视频的时候解析获取视频的时长，单位为秒（s））
        }
      })
      videoList.value.push(...videoArr);
    })
  }

  userVideoRes.value = await userVideoApi.page({
    userId:userId.value,
    eduRecordId: userEduRecordId.value
  })

  videoList.value.forEach(v => {
    const userVideo = userVideoRes.value.data.list.find(i => i.videoFileId === v.videoFileId)
    if (!userVideo) return
    v.watchedTime = userVideo.watchedTime
    v.formatWatchedTime = durationStrNew(userVideo.watchedTime)
  })

  console.log('videoList', videoList)
}

function updateProcess(e,record) {
  console.log('updateProcess e',e)
  console.log('updateProcess e second',timeStrToSeconds(e))

  const userVideo = userVideoRes.value.data.list.find(i => i.videoFileId === record.videoFileId)

  if (timeStrToSeconds(e) > record.duration){
    userVideo.watchedTime = record.duration
    userVideo.watchStatus = 2
  }else {
    userVideo.watchedTime = timeStrToSeconds(e)
    userVideo.watchStatus = 1
  }

  userVideoApi.update({
    ...userVideo
  }).then(res=>{
    createMessage.success('更新进度成功');
    reload()
  })
}

function videoWatchDetail(record) {
  console.log('videoDetail')
  openVideoWatchModal(true,record)
}

async function handleClose() {
  closeModal();
}

async function handleSubmit() {
  closeModal();
  emit('reload');
}
</script>

