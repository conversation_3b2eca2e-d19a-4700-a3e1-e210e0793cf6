<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="" showOkBtn @ok="handleSubmit" @close="handleClose"
              defaultFullscreen class="jnpf-full-modal full-modal">

    <a-table :dataSource="videoWatchPicList" :columns="columns" >
      <template #bodyCell="{ column, record,index }">
        <template v-if="column.key === 'key'">
          <div> {{ index+1 }}</div>
        </template>
        <template v-if="column.key === 'watchTime'">
          {{record.watchTime ? durationStrNew(record.watchTime) : '0' }}
        </template>
        <template v-if="column.key === 'creatorTime'">
          {{record.creatorTime ? formatToDateTime(record.creatorTime) : '/' }}
        </template>
        <template v-if="column.key === 'watchPhotoFileId'">
          <div >
            <a-image :src="fileSaveApi.getFileLocal(record.watchPhotoFileId)" style="width: 120px;height: 180px;"></a-image>
          </div>
        </template>
        <template v-if="column.key === 'action'">
           <a-button type="primary" size="middle" @click="handleDel(record)" danger>删除该记录</a-button>
        </template>
      </template>
    </a-table>

  </BasicModal>
</template>
<script lang="ts" setup>
import {BasicModal, useModalInner} from '/@/components/Modal';
import {reactive, ref} from "vue";
import {fileSaveApi, testUserEduRecordVideoWatchApi as userVideoWatchApi} from "/@/api";
import {durationStrNew, formatToDateTime} from "/@/utils/dateUtil";
import {useMessage} from "/@/hooks/web/useMessage";

defineOptions({name: 'eduVideoWatch'});

const emit = defineEmits(['register', 'reload']);
const [registerModal, {closeModal}] = useModalInner(init);
const {createMessage,createConfirm} = useMessage();

const searchInfo = reactive({
  userId:undefined,
  eduRecordId:undefined,
  videoFileId:undefined,
})
const eduId = ref('')
const videoWatchPicList = ref<any[]>([])

const columns = [
  {title: '序号', key: 'key', align: 'center',},
  {title: '视频已播放时长', key: 'watchTime'},
  {title: '识别时间', key: 'creatorTime'},
  {title: '图片', key: 'watchPhotoFileId'},
  {title: '操作', key: 'action'},
]

async function init(data: any) {
  console.log('data', data)
  searchInfo.userId = data.userId
  searchInfo.eduRecordId = data.eduRecordId
  searchInfo.videoFileId = data.videoFileId
  reload();
}

async function reload() {
  const ret = await userVideoWatchApi.page(searchInfo)
  videoWatchPicList.value = ret.data.list
  console.log('videoWatchPicList', videoWatchPicList)
}

async function handleDel(record){
  createConfirm({
    iconType: 'warning',
    title: '提示',
    content: '此操作将通过该记录，是否继续?',
    onOk: () => {
      userVideoWatchApi.remove(record.id)
      reload()
    },
  });
}

async function handleClose() {
  closeModal();
}

async function handleSubmit() {
  closeModal();
  emit('reload');
}
</script>

