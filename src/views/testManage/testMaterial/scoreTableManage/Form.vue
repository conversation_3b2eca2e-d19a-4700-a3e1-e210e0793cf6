<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="" showOkBtn @ok="handleSubmit" @close="handleClose">
    <ScoreTableComponent :score-info="scoreInfo"/>
  </BasicPopup>
</template>
<script lang="ts" setup>
import {computed, ref} from 'vue';
import {BasicPopup, usePopupInner} from '/@/components/Popup';
import {getInfo as getUserInfo} from '/@/views/permission/user/helper/api'
import  ScoreTableComponent from './ScoreTableComponent.vue'
import {testScoreTableApi} from "/@/api";

const emit = defineEmits(['register', 'reload']);
const [registerPopup, {closePopup, changeLoading, changeOkLoading}] = usePopupInner(init);


const eduName = ref();
const eduTime = ref();
const eduLocation = ref();
const eduTeacherName = ref();
const userScoreList = ref<any[]>([]);
const scoreInfo = ref({
  eduName:undefined,
  eduTime:undefined,
  eduLocation:undefined,
  eduTeacherName:undefined,
  userScoreList:undefined
})

async function init(data) {
  // console.log("data", data)
  scoreInfo.value.eduName = data.eduName
  scoreInfo.value.eduTime = data.eduTime
  scoreInfo.value.eduLocation = data.eduLocation

  if (data.eduTeacherId) {
    getUserInfo(data.eduTeacherId).then(res => {
      if (res.data.realName) {
        scoreInfo.value.eduTeacherName = res.data.realName
      }
    })
  }

  if (data.approvalId) {
    testScoreTableApi.getTestUserScore(data.approvalId).then(res => {
      scoreInfo.value.userScoreList = res.data
    })
  }

}


const paperRef = ref();

async function printThePaper() {
  // window.print(this.$refs.paperRef);

  const printContent = paperRef.value;
  const printWindow = window.open('', '_blank');
  printWindow.document.write(printContent.innerHTML);
  printWindow.document.close();
  printWindow.print();

}


async function handleClose() {
  userScoreList.value = [];
  closePopup();
  // emit('reload');
}

async function handleSubmit() {
  userScoreList.value = [];
  closePopup();
  emit('reload');
}
</script>

<style>
</style>

