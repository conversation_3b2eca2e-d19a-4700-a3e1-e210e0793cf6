<template>
  <a-button type="primary" @click="printThePaper()">打印成绩表</a-button>
  <a-button style="margin-left: 10px;" type="primary"  @click="exportScoreTable()">导出成绩表(包含考试用时)</a-button>

  <div style="display: flex;flex-direction: row; justify-content: center;" ref="paperRef">
    <div style="padding: 24px; width: 800px;">
      <div style="display: flex;flex-direction: row;justify-content: center; margin-bottom: 24px;">
        <div style="font-size: 24px;font-weight: 500;">成绩表</div>
      </div>

      <div style="display: flex;flex-direction: row; margin-bottom: 24px;">
        <div style="flex: 1; margin: 4px;">
          <div style="display: flex; flex-direction: row;">
            <div>主题：</div>
            <div style="flex: 1; border-bottom: 1px solid #333">{{ scoreInfo.eduName }}</div>
          </div>
        </div>

        <div style="flex: 1; margin: 4px;">
          <div style="display: flex; flex-direction: row;">
            <div>时间：</div>
            <div style="flex: 1; border-bottom: 1px solid #333">{{ scoreInfo.eduTime? formatToDate(scoreInfo.eduTime):'N/A' }}</div>
          </div>
        </div>

        <div style="flex: 1; margin: 4px;">
          <div style="display: flex; flex-direction: row;">
            <div>地点: </div>
            <div style="flex: 1; border-bottom: 1px solid #333">{{ emptyToNA(scoreInfo.eduLocation) }}</div>
          </div>

        </div>
        <div style="flex: 1; margin: 4px;">
          <div style="display: flex; flex-direction: row;">
            <div>主持人：</div>
            <div style="flex: 1; border-bottom: 1px solid #333">{{ emptyToNA(scoreInfo.eduTeacherName) }}</div>
          </div>
        </div>
      </div>

      <div>
        <!-- table  -->
        <table style="border-collapse: collapse;border: 1px solid #333;width: 100%;">
          <thead>
          <tr style="border: 1px solid #333;">
            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">序列</td>
            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">姓名</td>
            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">部门/单位</td>
            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">是否合格</td>
            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">成绩</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in scoreInfo.userScoreList" :key="item.id">
            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">{{ index + 1 }}.</td>
            <td style="border: 1px solid #333; padding: 4px; text-align: center;">{{ item.realName }}</td>
            <td style="border: 1px solid #333; padding: 4px; text-align: center;">{{ item.organizeName }}</td>
            <td style="border: 1px solid #333; padding: 4px; text-align: center;">{{ item.isPass }}</td>
            <td style="border: 1px solid #333; padding: 4px; text-align: center;">{{ item.score }}</td>
          </tr>
          </tbody>
        </table>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import {printHtml} from "/@/utils/printUtils";
import {formatToDate} from "/@/utils/dateUtil";
import {emptyToNA} from "/@/utils/str";
import {colKeys, exportTable} from "/@/utils/file/table";
import {ref} from "vue";
import {useMessage} from "/@/hooks/web/useMessage";
import {useI18n} from "/@/hooks/web/useI18n";

const {createConfirm} = useMessage();
const { t } = useI18n();

const {scoreInfo} = defineProps(['scoreInfo']);

const paperRef = ref();

async function printThePaper() {
  printHtml(paperRef.value)
}

const scoreCols: colKeys[] = [
  {title: '姓名', dataIndex: 'realName', key: 'realName'},
  {title: '部门/单位', dataIndex: 'organizeName', key: 'organizeName'},
  {title: '是否合格', dataIndex: 'isPass', key: 'isPass'},
  {title: '成绩', dataIndex: 'score', key: 'score'},
  {title: '考试开始时间', dataIndex: 'formatTestStartTime', key: 'formatTestStartTime'},
  {title: '考试结束时间', dataIndex: 'formatTestEndTime', key: 'formatTestEndTime'},
  {title: '考试用时', dataIndex: 'formatTestUseTime', key: 'formatTestUseTime'},
]


async function exportScoreTable(){
  createConfirm({
    iconType: 'info',
    title: t('common.okText'),
    content: '确认导出培训成绩表？',
    onOk: () => {
      // console.log('userScoreList', scoreInfo.userScoreList)
      exportTable(scoreCols, scoreInfo.userScoreList, '培训成绩表.xlsx')
    },
  });

}
</script>
