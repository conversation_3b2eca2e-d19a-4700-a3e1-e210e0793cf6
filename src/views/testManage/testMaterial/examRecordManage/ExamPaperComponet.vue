<template>
  <div style="display: flex;flex-direction: row; justify-content: center;">
    <div style="padding: 24px; width: 800px;" id="fa-exam-record">
      <div style="display: flex;flex-direction: row;justify-content: center; margin-bottom: 24px;">
        <div style="font-size: 24px;font-weight: 500;">{{ paperData.eduName }}测试试卷</div>
      </div>

      <div style="display: flex;flex-direction: row; margin-bottom: 24px;">
        <div style="flex: 1; margin: 4px;">
          <div style="display: flex; flex-direction: row;">
            <div>部门：</div>
            <div style="flex: 1; border-bottom: 1px solid #333">{{ `${paperData.organizeName}`.replace('漳州核电项目部/', '').split('/')[2] }}</div>
          </div>
        </div>

        <div style="flex: 1; margin: 4px;">
          <div style="display: flex; flex-direction: row;">
            <div>姓名：</div>
            <div style="flex: 1; border-bottom: 1px solid #333">{{ paperData.realName }}</div>
          </div>
        </div>

        <div style="flex: 1; margin: 4px;">
          <div style="display: flex; flex-direction: row;">
            <div>考试时间：</div>
            <div style="flex: 1; border-bottom: 1px solid #333">{{ formatToDate(paperData.testTime) }}</div>
          </div>

        </div>
        <div style="flex: 1; margin: 4px;">
          <div style="display: flex; flex-direction: row;">
            <div>考试分数：</div>
            <div style="flex: 1; border-bottom: 1px solid #333">{{ paperData.examScore }}</div>
          </div>
        </div>
      </div>

      <div style="font-weight: 500; margin-bottom: 8px;">一、判断题(每题{{ paperData.judgeScore }}分)</div>
      <div v-for="(item, index) in paperData.judgeQuestionList" :key="item.id" style="display: flex;flex-direction: row; text-wrap: normal; margin-bottom: 6px;">
        <span v-if="item.isRight===0" style="color: red">✘</span>
        <div style="width: 20px;margin-left: 4px">{{ index + 1 }}.</div>
        {{ item.questionContent }} （ {{ item.myAnswer }} ）
      </div>

      <div style="font-weight: 500; margin-bottom: 8px;">二、单选题(每题{{ paperData.choiceScore }}分)</div>
      <div v-for="(item, index) in paperData.chooseQuestionList" :key="item.id" style="display: flex;flex-direction: column; text-wrap: normal; margin-bottom: 6px;">
        <div style="display: flex; flex-direction: row">
          <span v-if="item.isRight===0" style="color: red">✘</span>
          <div style="width: 20px;margin-left: 4px">{{ index + 1 }}.</div>
          {{ item.questionContent }} （{{ item.myAnswer }} ）
        </div>
        <div style="margin-left: 20px;">
          <div v-for="q in item.questionChoices" :key="q.value" style="display: flex; flex-direction: row">
            <div>
              <span style="width: 20px; display: inline-block;">{{ q.value }}.</span>
              <span>{{ q.option }}</span>
            </div>
          </div>
        </div>
      </div>

      <div style="font-weight: 500; margin-bottom: 8px;">三、多选题(每题{{ paperData.multChoiceScore }}分)</div>
      <div v-for="(item, index) in paperData.multChooseQuestionList" :key="item.id" style="display: flex;flex-direction: column; text-wrap: normal; margin-bottom: 6px;">
        <div style="display: flex; flex-direction: row">
          <span v-if="item.isRight===0" style="color: red">✘</span>
          <div style="width: 20px;margin-left: 4px">{{ index + 1 }}.</div>
          {{ item.questionContent }} （{{ item.myAnswer }} ）
        </div>
        <div style="margin-left: 20px;">
          <div v-for="q in item.questionChoices" :key="q.value" style="display: flex; flex-direction: row">
            <div>
              <span style="width: 20px; display: inline-block;">{{ q.value }}.</span>
              <span>{{ q.option }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import {formatToDate} from "../../../../utils/dateUtil";

  defineProps(['paperData'])

</script>
