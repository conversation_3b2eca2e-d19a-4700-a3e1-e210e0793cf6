<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="" showOkBtn @ok="handleSubmit" @close="handleClose">
    <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="printThePaper()">打印考试试卷</a-button>

    <div class="fa-flex-row">
      <div ref="paperRef" style="font-variant: normal;">
        <div v-for="(item, index) in paperDataList" :key="item.id">
          <examPaperComp :paper-data="item"/>
        </div>
      </div>
    </div>

  </BasicPopup>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue';
import { BasicPopup, usePopupInner } from '/@/components/Popup';
import { cloneDeep } from "lodash-es";
import { testGradeApi } from '/@/api';
import { QUESTION_TYPE } from "/@/enums/zzEnums";
import examPaperComp from './ExamPaperComponet.vue'
import {printHtml} from "/@/utils/printUtils";


const emit = defineEmits(['register', 'reload']);
const [registerPopup, {closePopup}] = usePopupInner(init);

const paperDataList = ref<any[]>([])
const testExamRecordList = ref<any[]>([]);

const judgeScore = ref<any[]>([]);
const choiceScore = ref<any[]>([]);
const multChoiceScore = ref<any[]>([]);
const testTime = ref<any>("");
const examScore = ref<any[]>([]);

const judgeQuestionList = computed(() => {
  return testExamRecordList.value.filter(i => `${i.questionType}` === QUESTION_TYPE.JUDGE)
})

const chooseQuestionList = computed(() => {
  return testExamRecordList.value.filter(i => `${i.questionType}` === QUESTION_TYPE.SINGLE)
})

const multChooseQuestionList = computed(() => {
  return testExamRecordList.value.filter(i => `${i.questionType}` === QUESTION_TYPE.MULTIPLY)
})

async function init(data:any) {
  console.log('ids',data)
  if (!Array.isArray(data)) return;

  data.forEach(gradeId => {
    setPaperData(gradeId)

    // testExamRecordList.value = data.testExamRecordList;
    console.log("testExamRecordList.value", testExamRecordList.value)
  })
}

async function setPaperData(gradeId){
  let grade =await testGradeApi.getDetailById(gradeId);

  const ret = await testGradeApi.getQuestionsById(gradeId)
  testExamRecordList.value = parseData(ret.data);

  // 取值
  testExamRecordList.value.forEach(item => {
    testTime.value = new Date(item.creatorTime).toLocaleString().split(' ')[0]

    if (`${item.questionType}` === QUESTION_TYPE.JUDGE) {
      judgeScore.value = item.score
    }

    if (`${item.questionType}` === QUESTION_TYPE.SINGLE) {
      choiceScore.value = item.score
    }

    if (`${item.questionType}` === QUESTION_TYPE.MULTIPLY) {
      multChoiceScore.value = item.score
    }
  })

  let organizeName = '';
  if (grade.data.userEntity && grade.data.userEntity.organizeName) {
    organizeName = grade.data.userEntity.organizeName
  }

  let realName='';
  if (grade.data.userEntity && grade.data.userEntity.realName){
    realName = grade.data.userEntity.realName
  }

  paperDataList.value.push({
    eduTypeName: grade.data.eduTypeName ? grade.data.eduTypeName : '',
    eduName: grade.data.eduName ? grade.data.eduName : '',
    organizeName: organizeName,
    realName: realName,
    testTime: grade.data.creatorTime,
    examScore: grade.data.score,

    judgeScore:judgeScore.value,
    choiceScore:choiceScore.value,
    multChoiceScore:multChoiceScore.value,

    judgeQuestionList:judgeQuestionList.value,
    chooseQuestionList:chooseQuestionList.value,
    multChooseQuestionList:multChooseQuestionList.value,
  })

}

function parseData(list) {
  return cloneDeep(list).map((item) => {
    if (item.questionType === 1 || item.questionType === 2) {
      // questionChoices由字符串转化成数组
      item.questionChoices = JSON.parse(item.questionChoices)
    }
    return item;
  })
}

const paperRef = ref();

async function printThePaper() {
  // const printContent = paperRef.value;
  // const printWindow = window.open('', '_blank') as any;
  // printWindow.document.write(printContent.innerHTML);
  // printWindow.document.close();
  // printWindow.print();
  printHtml(paperRef.value)
}

async function handleClose() {
  testExamRecordList.value = [];
  paperDataList.value = []
  closePopup();
}

async function handleSubmit() {
  testExamRecordList.value = [];
  paperDataList.value = []
  closePopup();
  emit('reload');
}
</script>

