<template>
  <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="printThePaper()">打印签到表</a-button>

  <div style="display: flex;flex-direction: row; justify-content: center;" ref="paperRef">
    <div style="padding: 24px; width: 800px;">
      <div style="display: flex;flex-direction: row;justify-content: center; margin-bottom: 24px;">
        <div style="font-size: 24px;font-weight: 500;">签到表</div>
      </div>

<!--      <div style="display: flex;flex-direction: row; margin-bottom: 24px;">-->
<!--        <div style="flex: 1; margin: 4px;">-->
<!--          <div style="display: flex; flex-direction: row;">-->
<!--            <div>主题：</div>-->
<!--            <div style="flex: 1; border-bottom: 1px solid #333">{{ signInfo.eduName }}</div>-->
<!--          </div>-->
<!--        </div>-->

<!--        <div style="flex: 1; margin: 4px;">-->
<!--          <div style="display: flex; flex-direction: row;">-->
<!--            <div>时间：</div>-->
<!--            <div style="flex: 1; border-bottom: 1px solid #333">{{ formatToDate(signInfo.eduTime) }}</div>-->
<!--          </div>-->
<!--        </div>-->

<!--        <div style="flex: 1; margin: 4px;">-->
<!--          <div style="display: flex; flex-direction: row;">-->
<!--            <div>地点: </div>-->
<!--            <div style="flex: 1; border-bottom: 1px solid #333">{{ emptyToNA(signInfo.eduLocation) }}</div>-->
<!--          </div>-->

<!--        </div>-->
<!--        <div style="flex: 1; margin: 4px;">-->
<!--          <div style="display: flex; flex-direction: row;">-->
<!--            <div>主持人：</div>-->
<!--            <div style="flex: 1; border-bottom: 1px solid #333">{{ emptyToNA(signInfo.eduTeacherName) }}</div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->

      <div>
        <!-- table  -->
        <table style="border-collapse: collapse;border: 1px solid #333;width: 100%;">
          <thead>
          <tr style="border: 1px solid #333;height: 60px;">
<!--            <td colspan="6" style="border: 1px solid #333; font-size:22px;font-weight: 600; text-align: center;">{{ signInfo.eduName }}</td>-->
            <td colspan="5" style="border: 1px solid #333; font-size:22px;font-weight: 600; text-align: center;">{{ signInfo.eduName }}</td>
          </tr>
          <tr style="border: 1px solid #333;height: 60px;">
<!--            <td colspan="4" style="text-align: center;">培训教师（签名）：{{ emptyToNA(signInfo.eduTeacherName) }}</td>-->
            <td colspan="3" style="text-align: center;">培训教师（签名）：{{ emptyToNA(signInfo.eduTeacherName) }}</td>
            <td colspan="2" style="text-align: center;">
<!--              <div>培训时间：{{ signInfo.eduTime ? formatToDate(signInfo.eduTime):'N/A' }}</div>-->
            </td>
          </tr>
          <tr style="border: 1px solid #333;">
            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">序号</td>
            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">姓名</td>
            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">部门/单位</td>
            <!--              <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">职务/职称</td>-->
            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">签字</td>
            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">签字时间</td>
<!--            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;">培训时间</td>-->
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in signInfo.signedUserInfoList" :key="item.id">
            <td style="border: 1px solid #333; font-weight: 600; padding: 4px; text-align: center;"> {{ index + 1 }}.</td>
            <td style="border: 1px solid #333; padding: 4px; text-align: center;">{{ item.realName }}</td>
            <td style="border: 1px solid #333; padding: 4px; text-align: center;">{{ item.organizeName }}</td>
            <!--              <td style="border: 1px solid #333; padding: 4px; text-align: center;">{{ item.positionName }}</td>-->
            <td style="border: 1px solid #333; padding: 4px; text-align: center;">
              <img :src="item.signImgBase64" style="height: 40px;display:inline-block;" alt="Base64 Image"/>
            </td>
            <td style="border: 1px solid #333; padding: 4px; text-align: center;">{{ formatToDate(item.signTime) }}</td>
<!--            <td style="border: 1px solid #333; padding: 4px; text-align: center;">{{ formatToDate(item.eduStartTime)+'——'+formatToDate(item.eduEndTime) }}</td>-->
          </tr>
          </tbody>
        </table>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {printHtml} from "/@/utils/printUtils";
import {formatToDate} from "/@/utils/dateUtil";
import {emptyToNA} from "/@/utils/str";

defineProps(['signInfo']);

const paperRef = ref();

async function printThePaper() {
  printHtml(paperRef.value)
}

</script>
