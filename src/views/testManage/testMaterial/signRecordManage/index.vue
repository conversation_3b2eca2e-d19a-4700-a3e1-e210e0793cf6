<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <!--            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">新增</a-button>-->
            <!--            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn"-->
            <!--                      @click="handelBatchRemove()">批量删除</a-button>-->
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'creatorTime'">
              <div>{{ formatDate(record.creatorTime) }}</div>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)"/>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload"/>
  </div>
</template>
<script lang="ts" setup>
import {BasicTable, useTable, TableAction, BasicColumn, ActionItem} from '/@/components/Table';
import {useI18n} from '/@/hooks/web/useI18n';
import {useMessage} from '/@/hooks/web/useMessage';
import {usePopup} from '/@/components/Popup';
import Form from './Form.vue';
import {testSignApi} from "/@/api";
import {reactive} from "vue";

defineOptions({name: 'eduFileManage'});

const {t} = useI18n();
const {createMessage} = useMessage();
const columns: BasicColumn[] = [
  {title: '培训分类', dataIndex: 'eduTypeName', key: 'eduTypeName'},
  {title: '培训名称', dataIndex: 'eduName', key: 'eduName'},
  {title: '培训编号', dataIndex: 'eduNum', key: 'eduNum'},
  // {title: '审批编号', dataIndex: 'approvalNum', key: 'approvalNum'},
  {title: '参与人数', dataIndex: 'joinPersonNum', key: 'joinPersonNum'},
  {title: '已签到人数', dataIndex: 'signedPersonNum', key: 'signedPersonNum'},
  {title: '创建时间', dataIndex: 'creatorTime', key: 'creatorTime'},
  {title: '创建者', dataIndex: 'creatorUserId', key: 'creatorUserId'},
];
const [registerForm, {openPopup: openFormPopup}] = usePopup();

const searchInfo = reactive({
  _sorter: 'f_creator_time DESC', // 按照创建时间倒序排列
});
const [registerTable, {reload}] = useTable({
  api: testSignApi.list,
  columns,
  // searchInfo,
  useSearchForm: true,
  formConfig: {
    schemas: [
    ],
  },
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
  },
});

function formatDate(timestamp) {
  return new Date(timestamp).toLocaleString();
}

function getTableActions(record): ActionItem[] {
  return [
    {
      label: '签到详情',
      onClick: signDetail.bind(null, record),
    },
    // {
    //   label: t('common.delText'),
    //   color: 'error',
    //   modelConfirm: {
    //     onOk: handleDelete.bind(null, record.id),
    //   },
    // },
  ];
}


//todo 答题详情弹窗，附加固定模版导出功能
function signDetail(data) {
  console.log(data);
  openFormPopup(true, data);
}
</script>
