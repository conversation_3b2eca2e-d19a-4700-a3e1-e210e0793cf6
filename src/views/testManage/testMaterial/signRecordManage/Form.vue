<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="" showOkBtn @ok="handleSubmit" @close="handleClose">
    <SignRecordComponent :sign-info="signInfo" />
  </BasicPopup>
</template>
<script lang="ts" setup>
import {computed, ref} from 'vue';
import {BasicPopup, usePopupInner} from '/@/components/Popup';
import {getInfo as getUserInfo} from '/@/views/permission/user/helper/api'
import SignRecordComponent from './SignRecordComponent.vue'
import {testSignApi} from "/@/api";

const emit = defineEmits(['register', 'reload']);
const [registerPopup, {closePopup, changeLoading, changeOkLoading}] = usePopupInner(init);

const signedUserInfoList = ref<any[]>([]);
const signInfo = ref<any>({})

async function init(data) {
  console.log("data", data)
  signInfo.value.eduName = data.eduName
  signInfo.value.eduTime = data.eduTime
  signInfo.value.eduLocation = data.eduLocation

  if (data.eduTeacherId ) {
    getUserInfo(data.eduTeacherId).then(res => {
      if (res.data.realName) {
        signInfo.value.eduTeacherName = res.data.realName
      }
    })
  }

  if (data.approvalId) {
    testSignApi.getSignedUserInfo(data.approvalId).then(res => {
      signInfo.value.signedUserInfoList = res.data
    })
  }
}


// const paperRef = ref();
//
// async function printThePaper() {
//   // window.print(this.$refs.paperRef);
//
//   const printContent = paperRef.value;
//   const printWindow = window.open('', '_blank');
//   printWindow.document.write(printContent.innerHTML);
//   printWindow.document.close();
//   printWindow.print();
// }


async function handleClose() {
  signedUserInfoList.value = [];
  closePopup();
  // emit('reload');
}

async function handleSubmit() {
  signedUserInfoList.value = [];
  closePopup();
  emit('reload');
}
</script>

<style>
</style>

