<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable"
                    :row-selection="{selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange}"
                    rowKey="id">
          <template #tableTitle>
            <a-button @click="batchPrintPaper" style="color: #1890ff">批量打印评估表</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'sourceUnit'">
              <div>{{ record.userEntity ? record.userEntity.sourceUnit : '' }}</div>
            </template>
            <template v-if="column.key === 'realName'">
              <div>{{ record.userEntity ? record.userEntity.realName : '' }}</div>
            </template>
            <template v-if="column.key === 'gender'">
              <div>{{ record.isRight === '1' ? '男' : record.isRight === '2' ? '女' : '保密' }}</div>
            </template>
            <template v-if="column.key === 'organizeName'">
              <div>{{ record.userEntity ? record.userEntity.organizeName : '' }}</div>
            </template>
            <template v-if="column.key === 'roleName'">
              <div>{{ record.userEntity ? record.userEntity.roleName : '' }}</div>
            </template>
            <template v-if="column.key === 'identificationNumber'">
              <div>{{ record.userEntity ? record.userEntity.identificationNumber : '' }}</div>
            </template>
            <template v-if="column.key === 'mobilePhone'">
              <div>{{ record.userEntity ? record.userEntity.mobilePhone : '' }}</div>
            </template>

            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)"/>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload"/>
  </div>
</template>
<script lang="ts" setup>
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from '/@/components/Table';
import { testUserEvaluationApi} from '/@/api';
import { usePopup } from '/@/components/Popup';
import Form from './Form.vue';
import {reactive} from "vue";
import {evaluTabCols} from "/@/enums/testMaterialEnum";

defineOptions({name: 'testManage-examRecordManage'});

type Key = string | number;

const state = reactive<{
  selectedRowKeys: Key[];
  loading: boolean;
}>({
  selectedRowKeys: [], // Check here to configure the default column
  loading: false,
});

const onSelectChange = (selectedRowKeys: Key[]) => {
  console.log('selectedRowKeys changed: ', selectedRowKeys);
  state.selectedRowKeys = selectedRowKeys;
};

const [registerForm, {openPopup: openFormPopup}] = usePopup();
const [registerTable, {reload}] = useTable({
  api: testUserEvaluationApi.page,
  columns:evaluTabCols,
  useSearchForm: true,
  ellipsis: false,
  formConfig: {
    schemas: [
      {
        field: 'userId',
        label: '用户',
        component: 'UserSelect',
        componentProps: {
          submitOnPressEnter: true,
        },
      },
    ],
  },
  searchInfo: {
    '_sorter': 'f_creator_time DESC'
  },
  actionColumn: {
    width: 80,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record): ActionItem[] {
  return [
    {
      label: '评估表',
      onClick: evaluationTable.bind(null, record),
    },
  ];
}

function batchPrintPaper(){
  console.log('selectedRowKeys',state.selectedRowKeys)
  openFormPopup(true,state.selectedRowKeys)
}

// 答题详情弹窗，附加固定模版导出功能
function evaluationTable(record:any) {
  openFormPopup(true, new Array(record.id));
}
</script>
