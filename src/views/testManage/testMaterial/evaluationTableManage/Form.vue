<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="" showOkBtn @ok="handleSubmit" @close="handleClose">
    <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="printThePaper()">打印评估表</a-button>
    <div class="fa-flex-row">
        <div ref="paperRef" style="font-variant: normal;">
          <div v-for="(item, index) in evaluateInfoList" :key="item.id">
            <evaluateTableComp :evaluate-info="item"/>
          </div>
        </div>
      </div>

  </BasicPopup>
</template>
<script lang="ts" setup>
import {computed, ref} from 'vue';
import {BasicPopup, usePopupInner} from '/@/components/Popup';
import {testEduRecordApi, testUserEvaluationApi as api, userMasterApi} from "/@/api";
import evaluateTableComp  from './EvaluateTableComponent.vue'
import {printHtml} from "/@/utils/printUtils";
import {isString} from "/@/utils/is";

const emit = defineEmits(['register', 'reload']);
const [registerPopup, {closePopup,changeLoading, changeOkLoading}] = usePopupInner(init);

const userName = ref();
const eduName = ref();
const eduTeacherName = ref();
const eduTime = ref();
const eduLocation = ref();
const evaluateDetail = ref({});
const evaluateInfoList = ref<any[]>([])

async function init(data) {
  console.log("ids", data)
  if (!Array.isArray(data)) return;

  data.forEach(id=>{
    setEvaluateInfo(id)
  })

}

/* 设置培训相关参数 */
async function setEvaluateInfo(id){
  const evaluateInfo =await api.getDetailById(id);
  if (!evaluateInfo.data) return;

  if (isString(evaluateInfo.data.experienceNeedImprove)){
    evaluateInfo.data.experienceNeedImprove = evaluateInfo.data.experienceNeedImprove.split('/')
  }
  console.log('experienceNeedImprove',evaluateInfo.data.experienceNeedImprove)

  if (!evaluateInfo.data.eduId) return;
  const res = await testEduRecordApi.getById(evaluateInfo.data.eduId)
  if (!res.data) return;

  eduName.value = res.data.eduName
  eduLocation.value = res.data.eduLocation

  if (res.data.eduTeacherId) {
    const res2 = await userMasterApi.getById(res.data.eduTeacherId);
    if (res2.data.realName) {
      eduTeacherName.value = res2.data.realName
    }
  }

  eduTime.value = new Date(evaluateInfo.data.creatorTime).toLocaleDateString()
  userName.value = evaluateInfo.data.userEntity.realName
  evaluateDetail.value =evaluateInfo.data;
  console.log(evaluateDetail.value)

  evaluateInfoList.value.push({
    eduTime:eduTime.value,
    eduName: eduName.value,
    eduLocation: eduLocation.value,
    eduTeacherName: eduTeacherName.value,
    userName: userName.value,
    evaluateDetail: evaluateDetail.value,
  })

}


const paperRef = ref();

async function printThePaper() {
  // const printContent = paperRef.value;
  // const printWindow = window.open('', '_blank') as any;
  // printWindow.document.write(printContent.innerHTML);
  // printWindow.document.close();
  // printWindow.print();
  printHtml(paperRef.value)
}


async function handleClose() {
  evaluateInfoList.value = []
  closePopup();
  // emit('reload');
}

async function handleSubmit() {
  evaluateInfoList.value = []
  closePopup();
  emit('reload');
}
</script>

<style>
.normal_tb {

}
</style>

