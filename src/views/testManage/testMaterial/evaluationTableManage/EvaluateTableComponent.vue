<template>
  <div class="fa-flex-column fa-flex-center" style="margin: 30px 100px;">
    <div style="width: 800px;" ref="paperRef">
      <table class="fa-table fa-table-evaluate" style="width: 750px;">
        <tr class="fa-table-tr-p4">
          <td colspan="6">
            <div class="fa-table-title fa-text-center">培训效果评估表</div>
          </td>
        </tr>
        <tr class="fa-table-tr-p4">
          <td colspan="6">
            <div>您好！</div>
            <div>本问卷的主要目的在于了解您对此次培训的整体评价，我们期望能够通过本问卷了解您真实、具体的感受和建议，以便我们可以更好改善，以便在以后的培训中提高。感谢您的支持与帮助！</div>
          </td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td style="width: 100px;" class="fa-text-center">培训时间</td>
          <td style="width: 150px;" class="fa-text-center">{{ evaluateInfo.eduTime }}</td>
          <td style="width: 100px;" class="fa-text-center">培训地点</td>
          <td style="width: 150px;" class="fa-text-center">{{ emptyToNA(evaluateInfo.eduLocation) }}</td>
          <td style="width: 130px;" class="fa-text-center">评价人（可匿名）</td>
          <td class="fa-text-center">{{ evaluateInfo.userName }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td rowspan="2" colspan="2" style="width: 200px;">
            调查内容(在相应的空格后填写评价分值，不超过每项的总分值)
          </td>
          <td rowspan="2" class="fa-text-center">总分值</td>
          <td colspan="3" class="fa-text-center">培训课程/讲师</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td colspan="3" class="fa-text-center">{{ evaluateInfo.eduName }}/{{ emptyToNA(evaluateInfo.eduTeacherName) }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td rowspan="4">课程设计40%</td>
          <td>
            课程内容对您的工作帮助程度
          </td>
          <td>10</td>
          <td colspan="3" class="fa-text-center">{{ evaluateInfo.evaluateDetail.classHelpDegree }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td>
            主题突出、条理清晰，案例丰富
          </td>
          <td>10</td>
          <td colspan="3" class="fa-text-center">{{ evaluateInfo.evaluateDetail.classClearDegree }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td>
            课程内容的深度适中，易于理解
          </td>
          <td>10</td>
          <td colspan="3" class="fa-text-center">{{ evaluateInfo.evaluateDetail.classDifficultDegree }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td>
            课件内容切合实际应用，实践性强
          </td>
          <td>10</td>
          <td colspan="3" class="fa-text-center">{{ evaluateInfo.evaluateDetail.classActualDegree }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td rowspan="4">培训讲师 40%</td>
          <td>
            语言表达清楚、准确、逻辑性强，肢体语言表达自然、恰当
          </td>
          <td>10</td>
          <td colspan="3" class="fa-text-center">{{ evaluateInfo.evaluateDetail.teacherAccurateDegree }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td>
            讲解过程思路清晰，重点突出，注重教与学之间的互动性
          </td>
          <td>10</td>
          <td colspan="3" class="fa-text-center">{{ evaluateInfo.evaluateDetail.teacherInteractDegree }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td>
            对培训内容有独特见解，案例分析运用恰当。
          </td>
          <td>10</td>
          <td colspan="3" class="fa-text-center">{{ evaluateInfo.evaluateDetail.teacherAppropriateDegree }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td>
            课前准备充分程度和教学态度的认真程度
          </td>
          <td>10</td>
          <td colspan="3" class="fa-text-center">{{ evaluateInfo.evaluateDetail.teacherSufficientDegree }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td rowspan="2">总评20%</td>
          <td>
            达到预期培训目标的程度
          </td>
          <td>10</td>
          <td colspan="3" class="fa-text-center">{{ evaluateInfo.evaluateDetail.summaryCompleteDegree }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td>
            你对讲师授课的整体评价
          </td>
          <td>10</td>
          <td colspan="3" class="fa-text-center">{{ evaluateInfo.evaluateDetail.summaryEvaluation }}</td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td colspan="2">合计得分</td>
          <td>100</td>
          <td colspan="3" class="fa-text-center">{{ evaluateInfo.evaluateDetail.totalScore }}</td>
        </tr>


        <tr class="fa-table-tr-p4">
          <td rowspan="2">心得体会</td>
          <td>
            您在本次培训中最大的收获是(在相应的括号画√)
          </td>
          <td colspan="4">
            <div class="fa-checkbox-col">
              <div class="fa-checkbox">
                <span v-if="evaluateInfo.evaluateDetail.experienceGain===0">√</span>
              </div>
              提高了相关技能，将在工作中得到应用( )
            </div>
            <div class="fa-checkbox-col">
              <div class="fa-checkbox">
                <span v-if="evaluateInfo.evaluateDetail.experienceGain===1">√</span>
              </div>
              学到了新知识、新理念，自身能力得到提升( )
            </div>
            <div class="fa-checkbox-col">
              <div class="fa-checkbox">
                <span v-if="evaluateInfo.evaluateDetail.experienceGain===2">√</span>
              </div>
              理顺了过去工作中的一些模糊概念( )
            </div>
            <div class="fa-checkbox-col">
              <div class="fa-checkbox">
                <span v-if="evaluateInfo.evaluateDetail.experienceGain===3">√</span>
              </div>
              感觉没什么收获( )
            </div>
            <div>其它：</div>
          </td>
        </tr>
<!--        "课程时间排配不合理",-->
<!--        "培训课时安排不合理",-->
<!--        "课程内容排配不合理，相关知识不系统",-->
<!--        "学员层次不一，授课内容深浅程度难以平衡",-->
        <tr class="fa-table-tr-p4">
          <td>
            您认为本课程有哪些地方需要改进(在相应的括号画√)
          </td>
          <td colspan="4">
            <div class="fa-checkbox-col">
              <div class="fa-checkbox">
                <span v-if="evaluateInfo.evaluateDetail.experienceNeedImprove.includes('0')">√</span>
              </div>
              课程内容排配不合理（ ）
            </div>
            <div class="fa-checkbox-col">
              <div class="fa-checkbox">
                <span v-if="evaluateInfo.evaluateDetail.experienceNeedImprove.includes('1')">√</span>
              </div>
              培训课时安排不合理（ ）
            </div>
            <div class="fa-checkbox-col">
              <div class="fa-checkbox">
                <span v-if="evaluateInfo.evaluateDetail.experienceNeedImprove.includes('2')">√</span>
              </div>
              课程内容排配不合理，相关知识不系统（ ）
            </div>
            <div class="fa-checkbox-col">
              <div class="fa-checkbox">
                <span v-if="evaluateInfo.evaluateDetail.experienceNeedImprove.includes('3')">√</span>
              </div>
              学员层次不一，授课内容深浅程度难以平衡( )
            </div>
            <div>其它：</div>
          </td>
        </tr>

        <tr class="fa-table-tr-p4">
          <td>
            您对此次培训有什么建议
          </td>
          <td colspan="5">
            {{ evaluateInfo.evaluateDetail.suggestion }}
          </td>
        </tr>
      </table>
      <div>（说明：“培训课程/讲师”的列数可根据实际情况调整。可用电子问卷替代，但需导出问卷调查结果。）</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {emptyToNA} from "/@/utils/str";

defineProps(['evaluateInfo'])
defineOptions("evaluateTableComp")

</script>
