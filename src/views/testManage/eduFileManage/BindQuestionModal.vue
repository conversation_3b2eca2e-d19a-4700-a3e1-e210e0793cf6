<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :footer="null"
              defaultFullscreen class="jnpf-full-modal full-modal">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">新增</a-button>
        <a-button type="link" @click="handleExport"><i class="icon-ym icon-ym-btn-download button-preIcon" />{{ t('common.exportText') }}</a-button>
        <a-button type="link" @click="handleImport"><i class="icon-ym icon-ym-btn-upload button-preIcon" />{{ t('common.importText') }}</a-button>
        <a-button type="link" @click="handleBatchDelete" :disabled="selList.length === 0"><i class="icon-ym icon-ym-btn-clearn button-preIcon" />批量删除</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="getTableActions(record)"/>
        </template>
      </template>
    </BasicTable>
    <Form @register="registerForm" @reload="reload"/>
    <ImportModal @register="registerImportModal" @reload="reload"/>
  </BasicModal>
</template>

<script lang="ts" setup>
import {BasicModal, useModal, useModalInner} from '/@/components/Modal';
import {useMessage} from "/@/hooks/web/useMessage";
import {ActionItem, BasicColumn, BasicTable, TableAction, useTable} from "/@/components/Table";
import {reactive, ref} from "vue";
import {testEduQuestionApi} from "/@/api";
import {genQuerySearch, genQuerySelect} from "/@/utils/tableUtils";
import {useI18n} from "/@/hooks/web/useI18n";
import Form from "/@/views/testManage/questionManage/Form.vue";
import { QUESTION_TYPE_MAP, QUESTION_TYPE_OPTIONS} from "/@/enums/zzEnums";
import {trim} from "lodash-es";
import ImportModal from "./ImportModal.vue";


const emit = defineEmits(['register', 'reload', 'batchDeleteQuestion']); // 需要暴露的事件
const [registerModal, {closeModal, changeLoading, changeOkLoading}] = useModalInner(init);
const [registerForm, {openModal: openFormModal}] = useModal();
const [registerImportModal, {openModal: openImportModal}] = useModal();

const {t} = useI18n();
const {createMessage, createConfirm} = useMessage();


function init(data: any) {
  searchInfo.questionBankId = data.id
  reload()
}

const columns: BasicColumn[] = [
  {title: '所属题库(教材)', dataIndex: 'questionBank', width: 200},
  {
    title: '题目类型', dataIndex: 'questionType', width: 100,
    customRender: ({record}) => (QUESTION_TYPE_MAP[record.questionType])
  },
  {title: '题目内容', dataIndex: 'questionContent'},
  {title: '题目答案', dataIndex: 'questionAnswer', width: 100},
  {
    title: '题目选项', dataIndex: 'questionChoices',
    customRender: ({record}) => {
      if (trim(record.questionChoices) === '') return '';
      try {
        const array = JSON.parse(record.questionChoices);
        const str = array.map(i => `${i.value}: ${i.option}`).join(' | ')
        return str
      } catch (e) {
      }
      return '';
    },
  },
  {title: '题目解析', dataIndex: 'questionAnalysis', width: 100},
  // {
  //   title: '题目状态', dataIndex: 'questionStatus', width: 100,
  //   customRender: ({record}) => (QUESTION_STATUS_MAP[record.questionStatus])
  // },
  // {title: '创建者', dataIndex: 'creatorUserId', width: 100},
  {title: '创建日期', dataIndex: 'creatorTime', width: 100, format: 'date|YYYY-MM-DD'},
];
const searchInfo = reactive({
  _sorter: "f_question_type Asc",
  questionBankId: undefined, // 右table所属左treeId
});
const selList = ref<any[]>([]); // 选中的试题列表

const [registerTable, {reload}] = useTable({
  api: testEduQuestionApi.page,
  columns,
  searchInfo,
  useSearchForm: true,
  immediate: false,
  ellipsis: false,
  clickToRowSelect: true, // 点击选中条目
  rowSelection: { // 选中行
    onChange: (selectedRowKeys, selectedRows) => { // 选中行change
      console.log('selectedRowKeys', selectedRowKeys)
      selList.value = selectedRowKeys;
    },
  },
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQuerySelect("题目类型", "questionType", QUESTION_TYPE_OPTIONS)
    ],
  },
  actionColumn: {
    width: 100,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record: any): ActionItem[] {
  return [
    {
      label: t('common.editText'),
      onClick: addOrUpdateHandle.bind(null, record.id),
    },
    {
      label: t('common.delText'),
      color: 'error',
      modelConfirm: {
        onOk: handleDelete.bind(null, record.id),
      },
    },
  ];
}

function handleDelete(id) {
  testEduQuestionApi.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function addOrUpdateHandle(id = '') {
  openFormModal(true, {id, questionBankId: searchInfo.questionBankId});
}

async function handleExport() {
  // changeOkLoading(false)
  await testEduQuestionApi.exportExcel({
    _dataType: 0,
    "id#$in": selList.value,
    _sorter: "f_question_type Asc"
  })
    .then(_res => {
      // changeOkLoading(false)
      // if (!res.data.url) return;
      // setLoading(false);
      // closeModal(true)
    })
    .catch(() => {
      // setLoading(false);
    });
}

async function handleImport() {
  openImportModal(true, searchInfo.questionBankId);
}

async function handleBatchDelete() {
  createConfirm({
    iconType: 'warning',
    title: '删除',
    content: '是否确认删除所有选中试题',
    onOk: () => {
      testEduQuestionApi.removeBatchByIds(selList.value)
        .then(_res => {
          createMessage.success(_res.msg);
          reload();
          emit('batchDeleteQuestion');
        })
    }
  });
}


</script>
