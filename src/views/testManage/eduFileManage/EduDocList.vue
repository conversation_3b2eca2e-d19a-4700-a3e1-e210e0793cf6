<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="文档列表" width="800px" class="full-drawer chat-drawer">
    <BasicTable @register="registerTable">
<!--      <template #tableTitle>-->
<!--        <a-button @click="addOrUpdateHandle()" type="primary" preIcon="icon-ym icon-ym-btn-add">新增</a-button>-->
<!--      </template>-->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'duration'">
          <AInputNumber :default-value="toDecimal(record.duration / 60)" :min="0" :step="1" @blur="(e) => handleChangeDuration(e, record)" />
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="getTableActions(record)" />
        </template>
      </template>
    </BasicTable>
  </BasicDrawer>
</template>
<script lang="ts" setup>
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from "/@/components/Table";
import { testEduFileDocApi as api } from '/@/api';
import { usePopup } from "/@/components/Popup";
import { useMessage } from "/@/hooks/web/useMessage";
import { genDeleteBtn, genEditBtn, genQueryInput, genQuerySearch } from "/@/utils/tableUtils";
import { reactive } from "vue";
import {toDecimal, toFileSize} from "/@/utils/jnpf";
import {integer} from "vue-types";

const emit = defineEmits(['register']);
const [registerDrawer, { changeLoading }] = useDrawerInner(init);

const searchInfo = reactive({
  eduFileId: '',
  _sorter: 'f_creator_time DESC', // 按照创建时间倒序排列
});

function init(data:any) {
  console.log('init', data)
  searchInfo.eduFileId = data.id
  reload()
}

const {createMessage} = useMessage();
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const columns: BasicColumn[] = [
  { title: '文档名称', dataIndex: 'name', width: undefined },
  { title: '文件大小', dataIndex: 'size', width: 100, customRender: ({value}) => toFileSize(value) },
  {title: '学习时间（分钟）', dataIndex: 'duration', width: 200},
  //{ title: '排序', dataIndex: 'sort', width: 100 },
  // { title: '创建时间', dataIndex: 'creatorTime', width: 170, format: 'date|YYYY-MM-DD HH:mm:ss' },
];
const [registerTable, { reload }] = useTable({
  api: api.page,
  columns,
  immediate: false,
  useSearchForm: false,
  searchInfo,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('名称', 'name'),
    ],
  },
  // actionColumn: {
  //   width: 90,
  //   title: '操作',
  //   dataIndex: 'action',
  // },
});

function getTableActions(record:any): ActionItem[] {
  return [
    genEditBtn(record, addOrUpdateHandle),
    genDeleteBtn(record, handleDelete),
  ];
}

function addOrUpdateHandle(id='') {
  openFormPopup(true, {id});
}

function handleDelete(id:any) {
  api.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function handleChangeDuration(e, record) {
  console.log('handleChangeDuration', e.target.value, record)
  api.update({ id: record.id, duration: e.target.value * 60 }).then(res => {
    createMessage.success('更新学习时间成功');
  });
}
</script>
<style lang="less">
</style>
