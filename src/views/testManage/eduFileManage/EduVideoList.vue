<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="视频列表" width="800px" class="full-drawer chat-drawer"
               @close="handleClose">
    <div style="padding: 12px;">
      <a-alert>
        <template #message>
          <a href="http://doc.dward.cn/open/dm/doc/view/lghk?chapter=438" target="_blank">视频尽量压缩后上传，可以减少加载速度和播放卡顿，点击查看压缩视频教程。</a>
        </template>
      </a-alert>
    </div>

    <BasicTable @register="registerTable">
<!--      <template #tableTitle>-->
<!--        <a-button @click="addOrUpdateHandle()" type="primary" preIcon="icon-ym icon-ym-btn-add">新增</a-button>-->
<!--      </template>-->
      <template #bodyCell="{ column, record }">
<!--        <template v-if="column.key === 'audioDuration'">-->
<!--          <AInputNumber :default-value="toDecimal(record.audioDuration / 60)" :min="0" :step="1" @blur="(e) => handleChangeDuration(e, record)" :disabled="false"/>-->
<!--        </template>-->
<!--        <template v-if="column.key === 'formatAudioDuration'">-->
<!--          <JnpfTimePicker v-model:value="record.formatAudioDuration" @update:value="updateDuration($event,record)"/>-->
<!--        </template>-->
        <template v-if="column.key === 'action'">
          <FaUpdateFileQiniu @success="handleChangeVideo($event, record)"/>
        </template>
      </template>
    </BasicTable>
  </BasicDrawer>
</template>
<script lang="ts" setup>
import {BasicDrawer, useDrawerInner} from '/@/components/Drawer';
import {BasicColumn, BasicTable, useTable} from "/@/components/Table";
import {fileSaveApi as api, testEduFileApi, testUserEduRecordVideoApi as userVideoApi} from '/@/api';
import {useMessage} from "/@/hooks/web/useMessage";
import {genQueryInput, genQuerySearch} from "/@/utils/tableUtils";
import {reactive, ref} from "vue";
import {toFileSize} from "/@/utils/jnpf";
import {isString} from "/@/utils/is";
import FaUpdateFileQiniu from '/@/components/Jnpf/Upload/src/FaUpdateFileQiniu.vue'
import {tryParseJSON} from "/@/utils/fa-utils";
import {isNil} from "lodash-es";
import {durationStrNew} from "/@/utils/dateUtil";

defineOptions({ name: 'EduVideoList' })

const emit = defineEmits(['register']);
const [registerDrawer, { changeLoading,closeDrawer }] = useDrawerInner(init);

const videoFileId = ref<string[]>([])
const eduVideoList = ref<any[]>([])
const searchInfo = reactive({
  "id#$in": videoFileId,
  _sorter: 'f_creator_time DESC', // 按照创建时间倒序排列
});
const eduFile = ref({})

function init(data:any) {
  console.log('init', data)
  eduFile.value = data.record
  videoFileId.value = []
  eduVideoList.value = []
  if (isString(data.record.fileScreenshotLink)) {
    const arr = tryParseJSON(data.record.fileScreenshotLink, [])
    if (!Array.isArray(arr)) return;

    if (isNil(arr) || arr.length === 0) {
      videoFileId.value.push('-1')
    } else {
      arr.forEach(item => {
        videoFileId.value.push(item.fileId)
        eduVideoList.value.push(item)
      })
    }
  }

  reload()
}

const {createMessage} = useMessage();
const columns: BasicColumn[] = [
  { title: '视频名称', dataIndex: 'filename', width: undefined },
  { title: '视频大小', dataIndex: 'size', width: 100, customRender: ({value}) => toFileSize(value) },
  { title: '播放时长', dataIndex: 'formatAudioDuration',width: 160},
  {
    title: '学时时长(45min/学时)', dataIndex: 'audioDuration',width: 160, customRender: ({value}) => {
      if (!value) return ""
      let num: number = value / (45 * 60)
      return num.toFixed(2);
    }
  },
];
const [registerTable, { reload }] = useTable({
  api: api.page,
  columns,
  immediate: false,
  useSearchForm: false,
  searchInfo,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('名称', 'name'),
    ],
  },
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
  },
});

// function getTableActions(record:any): ActionItem[] {
//   return [
//     // genEditBtn(record, addOrUpdateHandle),
//     // genDeleteBtn(record, handleDelete),
//     {
//       label: '',
//       onClick: addOrUpdateHandle.bind(null, record.id),
//     }
//   ];
// }

function handleDelete(id:any) {
  api.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

async function handleChangeVideo(videoList, record) {
  console.log('handleChangeVideo', videoList)
  record.url = videoList[0].fullUrl
  record.size = videoList[0].fileSize
  record.audioDuration = videoList[0].duration

  /* 1.更新fileSave表信息 */
  let res1=await api.update(record)
  if (!res1){
    createMessage.error('更新学习时间失败');
    return
  }

  /* 2.更新userEduRecordVideo表 */
  let res2= await userVideoApi.updateUVideoDuration(record.id)
  if (!res2){
    createMessage.error('更新学习时间失败');
    return
  }

  /* 3.更新eduFile、eduFileVideo表信息 */
  eduVideoList.value.forEach((item,index)=>{
    if (item.fileId === record.id){
      eduVideoList.value[index] = {
        id:record.id,
        fileId:record.id,
        duration:record.audioDuration,
        fileSize:record.size,
        url: record.url,
        name:record.filename
      }
    }
  })
  eduFile.value.fileScreenshotLink = JSON.stringify(eduVideoList.value)

  await testEduFileApi.updateVideo(eduFile.value).then(res=>{
    createMessage.success('更新视频文件成功');
    reload()
  })
}

// async function updateDuration(e, record) {
//   record.formatAudioDuration = e
//   let res = await api.update(record)
//   console.log('res', res)
//   if (!res) return
//
//   testEduFileApi.updateVideo(eduFile.value).then(res => {
//     if (res) createMessage.success('更新时间成功');
//   })
// }

function handleClose(){
  videoFileId.value = []
  eduVideoList.value = []
  closeDrawer()
}
</script>
<style lang="less">
</style>
