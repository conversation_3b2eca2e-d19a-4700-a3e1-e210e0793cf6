<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="批量导入"
    :width="1000"
    :show-cancel-btn="false"
    :show-ok-btn="false"
    destroyOnClose
    class="jnpf-import-modal">
    <FaImportHeaderStep v-model:current="activeStep"/>

    <div class="import-main" v-show="activeStep == 0">
      <FaUploadCard v-model:value="fileId"/>
      <FaDownloadCard download-url="/file/试题导入模板.xlsx"/>

      <a-alert>
        <template #message>
          <ol>
            <li>1. 导入从第4行开始，请注意；</li>
            <li>2. 判断题类型的答案为：对、错；</li>
            <li>3. 单选题类型的答案为：A、B、C、D、E、F；</li>
            <li>4. 多选题类型的答案为：A、B、C、D、E、F，使用斜杠<code>/</code>进行拼接，如：A/B/C；</li>
          </ol>
        </template>
      </a-alert>
    </div>

    <div class="import-main" v-show="activeStep == 1">
      <a-table :data-source="list" :columns="columns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '440px' }" class="import-preview-table">
        <template #bodyCell="{ column, record, index }">
          <template v-for="item in tableData">
            <template v-if="column.key === item.dataIndex">
              <a-input v-model:value="record[column.key]" style="background: #FFF" />
            </template>
          </template>
          <template v-if="column.key === 'action'">
            <a-button class="action-btn" type="link" color="error" @click="handleDelItem(index)" size="small">删除</a-button>
          </template>
        </template>
      </a-table>
    </div>

    <div class="import-main" v-show="activeStep == 2">
      <FaImportSuccessCard v-if="!result.resultType" :success-num="result.snum"/>
      <FaImportFailCard v-if="result.resultType" :success-num="result.snum" :fail-num="result.fnum">
        <a-table :data-source="resultList" :columns="resultColumns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '205px' }"/>
      </FaImportFailCard>
    </div>

    <template #insertFooter>
      <a-button @click="handleClose()" v-if="activeStep == 0">{{ t('common.cancelText') }}</a-button>
      <a-button @click="handlePrev" v-if="activeStep === 1">{{ t('common.prev') }}</a-button>
      <a-button type="primary" @click="handleNext" :loading="btnLoading" v-if="activeStep < 2" :disabled="activeStep === 0 && !fileId">{{ t('common.next') }}</a-button>
      <a-button type="primary" @click="handleClose(true)" v-else>关闭</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
import {reactive, ref, toRefs} from 'vue';
import {BasicModal, useModalInner} from '/@/components/Modal';
import {useMessage} from '/@/hooks/web/useMessage';
import {useI18n} from '/@/hooks/web/useI18n';
import {testQuestionApi} from '/@/api';

interface State {
  activeStep: number;
  fileId?: string;
  btnLoading: boolean;
  list: any[];
  result: any;
  resultList: any[];
}

const emit = defineEmits(['register', 'reload']);
const [registerModal, {closeModal}] = useModalInner(init);
const {createMessage} = useMessage();
const {t} = useI18n();
const tableData = [
  {title: '所属题库(课件名称)', dataIndex: 'questionBank', key: 'questionBank'},
  {title: '所属题库(课件编码)', dataIndex: 'questionBankNum', key: 'questionBankNum'},
  {title: '题目类型', dataIndex: 'questionTypeName', key: 'questionTypeName'},
  {title: '题目内容', dataIndex: 'questionContent', key: 'questionContent'},
  {title: '答案', dataIndex: 'questionAnswer', key: 'questionAnswer'},
  {title: '选项一', dataIndex: 'questionChoicesOne', key: 'questionChoicesOne'},
  {title: '选项二', dataIndex: 'questionChoicesTwo', key: 'questionChoicesTwo'},
  {title: '选项三', dataIndex: 'questionChoicesThree', key: 'questionChoicesThree'},
  {title: '选项四', dataIndex: 'questionChoicesFour', key: 'questionChoicesFour'},
  {title: '选项五', dataIndex: 'questionChoicesFive', key: 'questionChoicesFive'},
  {title: '选项六', dataIndex: 'questionChoicesSix', key: 'questionChoicesSix'},
  {title: '解析', dataIndex: 'questionAnalysis', key: 'questionAnalysis'},
  {title: '异常', dataIndex: 'errorMsg', key: 'errorMsg'},
];
const columns: any[] = [
  // {width: 50, title: '序号', align: 'center', customRender: ({index}) => index + 1},
  ...tableData,
  {title: '操作', dataIndex: 'action', key: 'action', width: 50, fixed: 'right'},
];
const resultColumns: any[] = [
  // {width: 50, title: '序号', align: 'center', customRender: ({index}) => index + 1},
  ...tableData
];

const state = reactive<State>({
  activeStep: 0,
  fileId: undefined,
  btnLoading: false,
  list: [],
  result: {},
  resultList: [],
});
const {activeStep, fileId, btnLoading, list, result, resultList} = toRefs(state);

const questionBankId = ref();

function init(data) {
  console.log('data', data)
  // questionBankId.value = data
  state.activeStep = 0;
  state.fileId = undefined;
  state.btnLoading = false;
}

function handlePrev() {
  if (state.activeStep == 0) return;
  state.activeStep -= 1;
}

function handleNext() {
  if (state.activeStep == 0) {
    if (!state.fileId) return createMessage.warning('请先上传文件');
    state.btnLoading = true;
    testQuestionApi.importPreview({
      fileId: state.fileId
      // questionBankId: questionBankId.value
    })
      .then(res => {
        state.list = res.data || [];
        state.btnLoading = false;
        state.activeStep += 1;
      })
      .catch(() => {
        state.btnLoading = false;
      });
    return;
  }
  if (state.activeStep == 1) {
    if (!state.list.length) return createMessage.warning('导入数据为空');
    state.btnLoading = true;
    state.list = state.list.map(i => {
      return {
        ...i,
        // questionBankId: questionBankId.value
      }
    })
    testQuestionApi.importData({list: state.list})
      .then(res => {
        state.result = res.data;
        state.resultList = res.data.failResult;
        state.btnLoading = false;
        state.activeStep += 1;
      })
      .catch(() => {
        state.btnLoading = false;
      });
  }
}

function handleDelItem(index) {
  state.list.splice(index, 1);
}

function handleClose(reload = false) {
  closeModal();
  if (reload) emit('reload');
}
</script>
