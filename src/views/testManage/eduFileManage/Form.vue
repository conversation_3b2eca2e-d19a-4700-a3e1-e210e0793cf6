<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" showOkBtn @ok="handleSubmit" :width="1000"
            >
    <!--    <a-alert message="提醒info" type="warning" show-icon/>-->
    <BasicForm @register="registerForm" class="!px-10px !mt-10px" @field-value-change="handleFieldValueChange">
    </BasicForm>

    <div style="padding: 12px;">
      <a-alert>
        <template #message>
          <a href="http://doc.dward.cn/open/dm/doc/view/lghk?chapter=438" target="_blank">视频尽量压缩后上传，可以减少加载速度和播放卡顿，点击查看压缩视频教程。</a>
        </template>
      </a-alert>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import {computed, ref, unref} from 'vue';
import {BasicModal, useModalInner} from '/@/components/Modal';
import {BasicForm, useForm} from '/@/components/Form';
import {useMessage} from '/@/hooks/web/useMessage';
import {organizeTreeApi, testEduClassTreeApi, testEduFileApi} from "/@/api";
import {genOrganOption} from "/@/api/permission/organize";
import {ORGANIZE_LIST_OPTION} from "/@/enums/zzEnums";
import {eduFileSchemas} from "/@/enums/eduEnum";
import {useUserStore} from "/@/store/modules/user";
import {genDept, genInput, genSelect} from "/@/utils/formUtils";
import {COMMON_OPTION, COMMON_OPTION_BOOLEAN} from "/@/enums/commonEnum";

const id = ref('');
const getTitle = computed(() => (!unref(id) ? '新建培训材料' : '编辑培训材料'));
const emit = defineEmits(['register', 'reload']);
const {createMessage} = useMessage();
const inFactory = ref<boolean>(false)

const [registerForm, {setFieldsValue, getFieldsValue, validate, resetFields, updateSchema}] = useForm({
  labelWidth: 130,
  schemas: [
    {
      field: 'eduTypeId',
      label: '培训分类',
      component: 'FaCascader',
      componentProps: {placeholder: '请选择培训分类', api: testEduClassTreeApi, showRoot: true},
      rules: [{required: true, trigger: 'change', message: '培训分类不能为空'}],
    },
    genInput('资料编号', 'fileNum'),
    genInput('资料名称', 'fileName'),
    genDept('所属部门', 'departmentId', false),
    {
      ...genSelect('是否通用课件', 'isGeneralFile', false,'number',COMMON_OPTION),
      show: inFactory.value,
    },
    genInput('材料分类(自定义)', 'fileClass', false, false),
    {
      field: 'fileLink',
      label: '文件上传',
      component: 'UploadFileQiniu',
      componentProps: {placeholder: ''},
      helpMessage: '文件大小超过500M，可能影响app端正常播放，建议压缩后上传',
    },
    {
      field: 'fileScreenshotLink',
      label: '视频上传',
      component: 'UploadFileQiniu',
      componentProps: {placeholder: ''},
      helpMessage: '文件大小超过500M，可能影响app端正常播放，建议压缩后上传',
    },
  ]
});
const [registerModal, {closeModal, changeLoading, changeOkLoading}] = useModalInner(init);

const departMapList = ref<{
  fullName: string,
  id: string
}[]>([]);

const userStore = useUserStore();
const userInfo = userStore.getUserInfo;


//设置部门选项
async function setDepartmentOp() {
  const res = await genOrganOption(ORGANIZE_LIST_OPTION);
  departMapList.value = res.data.map(item =>{
    return {
      fullName:item.fullName,
      id:item.id
    }
  })
  console.log('departMapList',departMapList.value)

  updateSchema({field: 'departmentId', componentProps: {options: departMapList.value}})
  resetFields();
}

async function handleFieldValueChange(field, value) {
  if (field === 'departmentId' || field === 'eduTypeId' || field === 'isGeneralFile') {
    if (field === 'eduTypeId') {
      await judgeInFactory(value)
    }
    if (field === 'isGeneralFile') {
      // await judgeInFactory(value)
      console.log('FieldsValue',getFieldsValue())
    }
    await genFileNumByOrg();
  }
}

async function judgeInFactory(typeId: string) {
  const typeArr = ["入场一级安全教育培训", "入场二级安全教育培训", "入场三级安全教育培训"]
  const eduType = await testEduClassTreeApi.getDetailById(typeId);
  if (!eduType || !eduType.data || !eduType.data.className) return;
  inFactory.value =  (typeArr.indexOf(eduType.data.className) !== -1)
  console.log('inFactory',inFactory.value)

  updateSchema([
    {
      field: 'isGeneralFile',
      show: inFactory.value, // 动态更新显示状态
    }
  ])
}


async function init(data) {
  console.log('userInfo', userInfo)
  resetFields();
  id.value = data.id;
  await setDepartmentOp();

  if (id.value) {
    changeLoading(true);
    testEduFileApi.getById(id.value).then(res => {
      const data = {
        ...res.data,
      };
      data.fileLink = (data.fileLink !== '[]') ? JSON.parse(data.fileLink) : []
      data.fileScreenshotLink = (data.fileScreenshotLink !== '[]') ? JSON.parse(data.fileScreenshotLink) : []
      setFieldsValue(data);
      changeLoading(false);
    });
  } else {
    //
    let organ = await organizeTreeApi.getDepartOrgan(userInfo.organizeId || '0');
    if (!organ.data) return;

    await setFieldsValue({
      eduTypeId: data.eduTypeId,
      departmentId: data.departmentId ? data.departmentId : organ.data.id,
      fileLink: [],
      fileScreenshotLink: [],
      // inFactory:true
    })

    if (getFieldsValue().departmentId && getFieldsValue().eduTypeId) {
      await genFileNumByOrg();
    }
  }
}

//部门字段一旦有值，即生成课件编号
async function genFileNumByOrg() {
  await testEduFileApi.genFileNum({
    eduTypeId: getFieldsValue().eduTypeId,
    departmentId: getFieldsValue().departmentId,
    isGeneralFile: getFieldsValue().isGeneralFile,
  }).then(res => {
    setFieldsValue({
        fileNum: res.msg
      }
    )
  })
}


async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const query = {
    ...values,
    id: id.value,
  };

  query.eduTypeName = '';
  testEduClassTreeApi.getById(query.eduTypeId).then(res => {
    query.eduTypeName = res.data.className;
  })

  query.fileLink = query.fileLink ? JSON.stringify(query.fileLink) : '[]'
  query.fileScreenshotLink = query.fileScreenshotLink ? JSON.stringify(query.fileScreenshotLink) : '[]'

  // if (query.departmentId) {
  //   let depart = departMapList.value.find(depart => depart.id === query.departmentId)
  //   if (!depart) return;
  //   query.departmentName = depart.fullName
  // }
  const formMethod = id.value ? testEduFileApi.update : testEduFileApi.save;
  formMethod(query)
    .then(res => {
      createMessage.success(res.msg);
      changeOkLoading(false);
      closeModal();
      emit('reload');
    })
    .catch(() => {
      changeOkLoading(false);
    });
}
</script>
