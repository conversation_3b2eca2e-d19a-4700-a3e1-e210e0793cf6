<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :footer="null" @cancel="handleClose"
              defaultFullscreen class="jnpf-full-modal full-modal" :destroyOnClose="true">

    <div class="fa-file-doc-countdown">
      观看时间倒计时：{{ State.duration - State.watchtedTime }}秒
    </div>

    <div style="padding: 40px;" class="fa-flex-center">
      <video
        id="my-player"
        controls
        preload="auto"
        width="1000"
        height="800"
        data-setup="{}">
        <source :src=State.videoUrl type="video/mp4"/>
      </video>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import {BasicModal, useModal, useModalInner} from '/@/components/Modal';
import {useMessage} from "/@/hooks/web/useMessage";
import {useI18n} from "/@/hooks/web/useI18n";
import {reactive, ref} from "vue";
import {isString} from "/@/utils/is";
import { fileSaveApi, testUserEduRecordApi, testUserEduRecordVideoApi } from "/@/api";

const [registerModal, {closeModal, changeLoading, changeOkLoading}] = useModalInner(init);
const {t} = useI18n();
const {createMessage, createConfirm} = useMessage();

defineOptions({ name: 'eduVideoModal' });

interface State {
  videoUrl: string,
  userEduRecordId: string,
  userId: string,
  videoFileId: string,
  duration: number,
  watchtedTime: number,
  watchStatus: number,
  localTimer: number,
  progressTimer: number,
  videoItem: object
}

const State = reactive<State>({
  videoUrl: '',
  userEduRecordId: '',
  userId: '',
  videoFileId: '',
  duration: 0,
  watchtedTime: 0,
  watchStatus: 1,
  localTimer: 0,
  progressTimer: 0,
  videoItem: {},
})

function init(data: any) {
  console.log('initData',data)
  createMessage.info('视频播放进度，更新频率为每分钟更新一次')
  if (State.localTimer){
    clearInterval(State.localTimer)
  }
  if (State.progressTimer){
    clearInterval(State.progressTimer)
  }

  State.videoItem =data
  if(isString(data.fullUrl)){
    State.videoUrl = data.fullUrl
    if (!State.videoUrl.startsWith('http')) { // 不是http开头的外网URL，则使用本服务器的接口地址访问
      const url = window.location.origin + fileSaveApi.getFileLocal(data.id) + '?fullfilename=' + data.name;
      State.videoUrl = url;
    }
  }
  console.log('videoUrl',State.videoUrl)
  State.userEduRecordId=data.eduRecordId
  State.userId=data.userId
  State.videoFileId = data.videoFileId
  State.duration = data.duration

  timeCount()
  if (State.watchStatus !== 2){
    State.progressTimer = Number(setInterval(updateWatchProgress, 60 * 1000))
  }

}

//统计进度
async function timeCount(){
  let ret ;
  try {
    ret =await testUserEduRecordVideoApi.getWatchData(State.userEduRecordId, State.userId);
    // if ((ret as any).code !== 200) return;
    const cacheItem = ret.data.find(cacheItem => cacheItem.videoFileId === State.videoFileId);

    if(cacheItem.watchedTime && cacheItem.watchedTime !== 0){
      if (cacheItem.watchedTime >= State.duration || cacheItem.watchStatus === 2) {
        State.watchStatus = 2
        return
      }
      State.watchtedTime = cacheItem.watchedTime
    }
  }catch (err){
    console.log('报错')
  }

  State.localTimer = Number(setInterval(() => {
    State.watchtedTime++
    if (State.watchtedTime >= State.duration) {
      State.watchStatus = 2
      State.watchtedTime = State.duration
    }
  }, 1000))
}

//后台同步进度
function updateWatchProgress(){
  State.videoItem.watchedTime =State.watchtedTime
  State.videoItem.watchStatus =State.watchStatus
  let arr  = new Array(State.videoItem)
  testUserEduRecordVideoApi.saveWatchData(arr)
  testUserEduRecordApi.updateProgress(State.userEduRecordId)
}

async function handleClose(){
  console.log('handleClose')
  window.clearInterval(State.localTimer)
  window.clearInterval(State.progressTimer)
  State.localTimer= 0
  State.progressTimer= 0

  await testUserEduRecordApi.updateProgress(State.userEduRecordId)
  closeModal()
}

</script>
