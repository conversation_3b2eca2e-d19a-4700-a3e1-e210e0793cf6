<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :footer="null" @cancel="handleClose"
              defaultFullscreen class="jnpf-full-modal full-modal">

    <div class="fa-file-doc-countdown">
      阅读时间倒计时：{{ duration }}秒
    </div>
    <div class="basic-content bg-white">
      <iframe width="100%" height="100%" :src="docUrl" frameborder="0"></iframe>
    </div>

  </BasicModal>
</template>

<script lang="ts" setup>
import {BasicModal, useModal, useModalInner} from '/@/components/Modal';
import {useI18n} from "/@/hooks/web/useI18n";
import {ref} from "vue";
import {fileSaveApi, testUserEduRecordDocApi} from "/@/api";
import {encryptByBase64} from "/@/utils/cipher";

const [registerModal, {closeModal, changeLoading, changeOkLoading}] = useModalInner(init);
const {t} = useI18n();


defineOptions({ name: 'eduDocModal' });

const docUrl =ref('')
const userId =ref('')
const countDown =ref(true)
const recordDocId =ref('')
const duration =ref(0)
const localTimer = ref()

function init(data: any) {
  console.log('initDocModal',data)

  docUrl.value = previewUrl(data.docFileId, data.docName)
  recordDocId.value = data.id
  duration.value= Number(data.duration || 0);
  // previewUrl(data.docFileId,data.docName)

  if (localTimer.value){
    clearInterval(localTimer.value)
  }

  timeCount()
}

function previewUrl(fileId, filename) {
  const url = window.location.origin + fileSaveApi.getFileLocal(fileId) + '?fullfilename=' + filename
  console.log('url', url)
  return '/FileServer/onlinePreview?url=' + encodeURIComponent(encryptByBase64(url));
}


async function timeCount(){
  // 倒计时
  // countDown.value = option.countDown === '1';
  if (countDown.value) {
    clearTimer()

    localTimer.value = setInterval(() => {
      const nextTime = duration.value - 1
      duration.value = nextTime > 0 ? nextTime : 0
      if (duration.value === 0) {
        // 更新培训文档状态为已完成
        clearTimer()
        markDocDone()
      }
    }, 1000)
  }
}

function clearTimer() {
  if (localTimer.value) {
    clearInterval(localTimer.value)
  }
}

async function markDocDone() {
  const res = await testUserEduRecordDocApi.markDownById(recordDocId.value)
  alert('文档阅读完成')
}

async function handleClose(){
  console.log('handleClose')
  window.clearInterval(localTimer.value)
  localTimer.value= null

  closeModal()
}

</script>
