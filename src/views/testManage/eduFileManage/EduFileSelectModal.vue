<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @ok="handleSubmit" :footer="null" title="选择课件"
              @close="handleClose"
              defaultFullscreen class="jnpf-full-modal full-modal">
    <a-space class="fa-mb-12">
      <div>选择课件：已选择 <span style="color: #dd0000; font-weight: bold;">{{ selList.length }}</span> 个课件</div>
      <a-button @click="handleSubmit" type="primary">确定</a-button>
    </a-space>
    <BasicTable @register="registerTable">
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
defineOptions({ name: 'eduFileSelectModal' });

import {reactive, ref, watch} from "vue";
import { BasicModal, useModalInner } from '/@/components/Modal';
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from "/@/components/Table";
import { testEduFileApi } from "/@/api";
import { genQuerySearch } from "/@/utils/tableUtils";
import { useMessage } from "/@/hooks/web/useMessage";


const emit = defineEmits(['register', 'selectEduFile']); // 需要暴露的事件
const [registerModal, { closeModal }] = useModalInner(init);
const { createConfirm } = useMessage();
const selList = ref<any[]>([])

const searchInfo = reactive({
  organizeId: '', // 右table所属左organizeId
  level: '1', // 层级
  posId: '', // 岗位
  _sorter: 'f_creator_time DESC', // 按照创建时间倒序排列
});

function init(data:any) {
  console.log('data', data)
  searchInfo.level = data.level
  searchInfo.organizeId = data.organizeId
  searchInfo.posId = data.posId
  reload()
}

const columns: BasicColumn[] = [
  {title: '课件编号', dataIndex: 'fileNum', width: 250},
  {title: '课件名称', dataIndex: 'fileName', width: 300},
  {title: '所属部门', dataIndex: 'departmentName', width: 300},
  {title: '培训分类', dataIndex: 'eduTypeName', width: 200},
  {title: '自定义分类', dataIndex: 'fileClass', width: 200},
]

const [registerTable, {reload, getSelectRowKeys}] = useTable({
  api: testEduFileApi.list,
  columns,
  searchInfo,
  useSearchForm: true,
  clickToRowSelect: true, // 点击选中条目
  immediate: false,
  formConfig: {
    schemas: [
      genQuerySearch(),
    ],
  },
  rowSelection: { // 选中行
    onChange: (_selectedRowKeys, selectedRows) => { // 选中行change
      selList.value = selectedRows;
    },
  },
});

function handleClose() {
  closeModal();
}
function handleSubmit() {
  console.log('selList',selList.value)
  createConfirm({
    iconType: 'warning',
    title: '确认',
    content: '确定绑定选中的课件吗?',
    onOk: () => {
      closeModal();
      emit('selectEduFile', selList.value);
    },
  });
}
</script>
