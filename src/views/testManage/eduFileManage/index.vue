<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-left" style="width: 250px;">
      <BasicLeftTree
        title="培训分类"
        ref="leftTreeRef"
        :treeData="treeData"
        :loading="treeLoading"
        :fieldNames="{ key: 'id', title: 'name' }"
        @reload="reloadTree"
        @select="handleTreeSelect"
        showToolbar/>
    </div>

    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">新增</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handleBatchDelete()" :disabled="selList.length === 0">批量删除</a-button>
            <a-button @click="syncOldData()" :loading="loadingSync">同步老数据</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)"/>
            </template>
          </template>
        </BasicTable>
      </div>
      <Form @register="registerForm" @reload="reload"/>
      <BindQuestionModal @register="registerBindQuestionModal" @batchDeleteQuestion="reload"/>
      <EduDocList @register="registerDocListDrawer" />
      <EduVideoList @register="registerVideoListDrawer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, toRefs, unref } from "vue";
import { BasicLeftTree, TreeActionType } from "/@/components/Tree";
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from "/@/components/Table";
import { testEduClassTreeApi, testEduFileApi, testEduRecordApi } from "/@/api";
import { useMessage } from "/@/hooks/web/useMessage";
import Form from "/@/views/testManage/eduFileManage/Form.vue";
import { useModal } from "/@/components/Modal";
import {genDeleteBtn, genEditBtn, genQueryDept, genQuerySearch, genQuerySelect} from "/@/utils/tableUtils";
import BindQuestionModal from "./BindQuestionModal.vue";
import { genOrganOption } from "/@/api/permission/organize";
import { ORGANIZE_LIST_OPTION } from "/@/enums/zzEnums";
import { useDrawer } from "/@/components/Drawer";
import EduDocList from "/@/views/testManage/eduFileManage/EduDocList.vue";
import EduVideoList from "/@/views/testManage/eduFileManage/EduVideoList.vue";
import {durationStr, formatToDateTime} from "/@/utils/dateUtil";
import {useUserStore} from "/@/store/modules/user";


defineOptions({name: 'edu-testManage-eduFileManage'});

interface State {
  treeData: any[];
  treeLoading: boolean;
}

const state = reactive<State>({
  treeData: [],
  treeLoading: false,
});
const {treeData, treeLoading} = toRefs(state);
const leftTreeRef = ref<Nullable<TreeActionType>>(null);


async function init() {
  setLoading(true);
  await reloadTree();
  searchInfo.eduTypeId = state.treeData[0]?.id;
  const leftTree = unref(leftTreeRef);
  leftTree?.setSelectedKeys([searchInfo.eduTypeId]);

  await updateDepartmentId();
  reload();
}

// ----------------------------------- left tree -----------------------------------
/** 重新加载Tree数据 */
async function reloadTree() {
  state.treeLoading = true;
  const ret = await testEduClassTreeApi.allTree();
  state.treeData = [
    {id: '', name: '全部'},
    ...ret.data,
  ];
  state.treeLoading = false
}

/** Tree节点点击 */
async function handleTreeSelect(id: any) {
  console.log('handleTreeSelect', id)
  // if (!id || searchInfo.eduTypeId === id) return;
  searchInfo.eduTypeId = id;
  reload()
}

async function updateDepartmentId() {
  const res = await genOrganOption(ORGANIZE_LIST_OPTION);
  getForm().updateSchema({field: 'departmentId', componentProps: {options: res.data}})
  getForm().resetFields();
}


// ----------------------------------- right table -----------------------------------
const {createMessage, createConfirm} = useMessage();
const [registerForm, {openModal: openFormModal}] = useModal();
const [registerBindQuestionModal, {openModal: openBindQuestionModal}] = useModal();
const [registerDocListDrawer, {openDrawer: openDocListDrawer}] = useDrawer();
const [registerVideoListDrawer, {openDrawer: openVideoListDrawer}] = useDrawer();
const loadingSync = ref(false)

const columns: BasicColumn[] = [
  {title: '课件编号', dataIndex: 'fileNum', width: 250},
  {title: '课件名称', dataIndex: 'fileName', width: 400},
  {title: '所属部门', dataIndex: 'departmentFullName', width: 400},
  {
    title: '视频总时长', dataIndex: 'totalDuration',
    customRender: ({record}) => record.totalDuration? durationStr(record.totalDuration):""
  },
  {
    title: '视频总学时(45min/学时)', dataIndex: 'totalDuration',
    customRender: ({record}) => {
      if(!record.totalDuration) return ""
      let num: number = record.totalDuration / (45 * 60)
      return num.toFixed(2)
    }
  },
  {title: '自定义分类', dataIndex: 'fileClass'},
  {title: '题目数量', dataIndex: 'questionCount', width: 100},
  {title: '创建者', dataIndex: 'creatorUserName', width: 100},
  {title: '创建时间', dataIndex: 'creatorTime', width: 120, format: 'date|YYYY-MM-DD'},
];
const searchInfo = reactive({
  eduTypeId: '', // 右table所属左treeId
  _sorter: 'f_creator_time DESC', // 按照创建时间倒序排列
});
const selList = ref<any[]>([]); // 选中的试题列表

const [registerTable, {reload, setLoading, getForm}] = useTable({
  api: testEduFileApi.page,
  columns,
  searchInfo,
  useSearchForm: true,
  immediate: false,
  clickToRowSelect: true, // 点击选中条目
  rowSelection: { // 选中行
    onChange: (selectedRowKeys) => { // 选中行change
      // console.log('selectedRowKeys', selectedRowKeys)
      selList.value = selectedRowKeys;
    },
  },
  formConfig: {
    schemas: [
      // genQuerySelect('部门', 'departmentId'),
      genQueryDept('部门', 'departmentId'),
      genQuerySearch(),
    ],
  },
  actionColumn: {
    width: 300,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record:any): ActionItem[] {
  const userInfo = useUserStore().getUserInfo;
  let docSize = 0;
  try {
    docSize = JSON.parse(record.fileLink).length; // 文档大小
  } catch(e) {console.error(e)}
  let videoSize = 0;
  try {
    videoSize = JSON.parse(record.fileScreenshotLink).length; // 文档大小
  } catch(e) {console.error(e)}

  let arr = [
    genEditBtn(record, addOrUpdateHandle),
    //   label: '试题导入',
    //   onClick: batchImportQuestion.bind(null, record),
    // },
    {
      label: `文档(${docSize})`,
      onClick: showBindDocList.bind(null, record.id),
    },
    {
      label: `更新视频(${videoSize})`,
      onClick: updateVideo.bind(null, record),
    },
    {
      label: '查看试题',
      onClick: showBindQuestion.bind(null, record.id),
    },
  ];

  if (record.creatorUserId ===userInfo.userId || userInfo.isAdministrator){
    arr.push(genDeleteBtn(record, handleDelete))
  }

  return arr;
}

function handleDelete(id) {
  testEduFileApi.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function addOrUpdateHandle(id = '') {
  openFormModal(true, {
    id,
    eduTypeId: searchInfo.eduTypeId,
    departmentId: getForm().getFieldsValue().departmentId
  });
}

async function syncOldData() {
  try {
    loadingSync.value = true
    await testEduFileApi.syncOldData()
    loadingSync.value = false
    createMessage.success('同步成功');
  } catch (e) {
    loadingSync.value = false
  }
}

function handleBatchDelete() {
  createConfirm({
    iconType: 'warning',
    title: '删除',
    content: '是否确认删除所有选中课件？',
    onOk: () => {
      setLoading(true)
      testEduFileApi.removeBatchByIds(selList.value).then(_res => {
        setLoading(false);
        reload();
      }).catch(() => setLoading(false));
    },
  });
}

function showBindQuestion(id:string) {
  openBindQuestionModal(true, {id});
}

function showBindDocList(id:string) {
  openDocListDrawer(true, {id});
}

function updateVideo(record) {
  openVideoListDrawer(true, {record});
}

onMounted(() => init());
</script>


