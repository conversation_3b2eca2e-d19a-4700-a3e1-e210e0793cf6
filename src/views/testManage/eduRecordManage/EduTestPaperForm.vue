<template>
  <BasicPopup v-bind="$attrs" @register="registerModal" :title="`培训计划试卷配置-${record ? record.eduName : ''}`" showOkBtn @ok="handleSubmit" @close="handleClose">
    <!--    <a-alert message="提醒info" type="warning" show-icon/>-->
    <a-form v-if="record" :model="dataForm" :rules="formRules" ref="formElRef" :colon="false" :labelCol="{ style: { width: '100px' } }" style="padding: 12px;">
      <a-row>
        <a-col :span="6">
          <a-form-item label="考试最大次数" name="testLimitCount">
            <a-input-number v-model:value="dataForm.testLimitCount" :step="1" :min="1" :max="1000" placeholder="请输入考试最大次数" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="考试限时" name="limitTime">
            <JnpfTimePicker v-model:value="dataForm.limitTime" format="HH:mm:ss" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="试卷总分" name="totalScore">
            <a-input-number v-model:value="dataForm.totalScore" :step="1" :min="1" :max="1000" disabled placeholder="请输入试卷总分" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="及格分数" name="passScore">
            <a-input-number v-model:value="dataForm.passScore" :step="1" :min="1" :max="1000" placeholder="请输入及格分数" />
          </a-form-item>
        </a-col>
      </a-row>

      <div>
        <div class="fa-question-config-table">
          <div class="fa-question-config-thead-tr">
            <a-col class="fa-question-config-thead">题目类型</a-col>
            <a-col class="fa-question-config-thead">题库总数</a-col>
            <a-col class="fa-question-config-thead">总题目数</a-col>
            <a-col class="fa-question-config-thead">每题分值</a-col>
            <a-col class="fa-question-config-thead">题型总分</a-col>
            <a-col class="fa-question-config-thead">固定题目数</a-col>
            <a-col class="fa-question-config-thead">随机题目数</a-col>
          </div>

          <div class="fa-question-config-thead-tr">
            <a-col class="fa-question-config-tr">判断题</a-col>
            <a-col class="fa-question-config-tr">{{ dataForm.questionJudgeTotal }}</a-col>
            <a-col class="fa-question-config-tr">
              <a-form-item name="questionJudgeNum">
                <a-input-number v-model:value="dataForm.questionJudgeNum" :step="1" :min="0" :max="1000" placeholder="请输入判断题总数" />
              </a-form-item>
            </a-col>
            <a-col class="fa-question-config-tr">
              <a-form-item name="questionJudgeScore">
                <a-input v-model:value="dataForm.questionJudgeScore" :step="1" :min="0" :max="100" type="number" placeholder="请输入判断题每题分值" />
              </a-form-item>
            </a-col>
            <a-col class="fa-question-config-tr">{{dataForm.questionJudgeNum * dataForm.questionJudgeScore}}</a-col>
            <a-col class="fa-question-config-tr">
              <a @click="handleAddFixed(QUESTION_TYPE.JUDGE)">{{ dataForm.questionJudgeFixed }}</a>
            </a-col>
            <a-col class="fa-question-config-tr">{{ dataForm.questionJudgeNum - dataForm.questionJudgeFixed }}</a-col>
          </div>

          <div class="fa-question-config-thead-tr">
            <a-col class="fa-question-config-tr">单选题</a-col>
            <a-col class="fa-question-config-tr">{{ dataForm.questionSingleTotal }}</a-col>
            <a-col class="fa-question-config-tr">
              <a-form-item name="questionSingleNum">
                <a-input-number v-model:value="dataForm.questionSingleNum" :step="1" :min="0" :max="1000" placeholder="请输入单选题总数" />
              </a-form-item>
            </a-col>
            <a-col class="fa-question-config-tr">
              <a-form-item name="questionSingleScore">
                <a-input v-model:value="dataForm.questionSingleScore" :step="1" :min="0" :max="100" type="number" placeholder="请输入单选题每题分值" />
              </a-form-item>
            </a-col>
            <a-col class="fa-question-config-tr">{{dataForm.questionSingleNum * dataForm.questionSingleScore}}</a-col>
            <a-col class="fa-question-config-tr">
              <a @click="handleAddFixed(QUESTION_TYPE.SINGLE)">{{ dataForm.questionSingleFixed }}</a>
            </a-col>
            <a-col class="fa-question-config-tr">{{ dataForm.questionSingleNum - dataForm.questionSingleFixed }}</a-col>
          </div>

          <div class="fa-question-config-thead-tr">
            <a-col class="fa-question-config-tr">多选题</a-col>
            <a-col class="fa-question-config-tr">{{ dataForm.questionMultiTotal }}</a-col>
            <a-col class="fa-question-config-tr">
              <a-form-item name="questionMultiNum">
                <a-input-number v-model:value="dataForm.questionMultiNum" :step="1" :min="0" :max="1000" placeholder="请输入多选题总数" />
              </a-form-item>
            </a-col>
            <a-col class="fa-question-config-tr">
              <a-form-item name="questionMultiScore">
                <a-input v-model:value="dataForm.questionMultiScore" :step="1" :min="0" :max="100" type="number" placeholder="请输入多选题每题分值" />
              </a-form-item>
            </a-col>
            <a-col class="fa-question-config-tr">{{dataForm.questionMultiNum * dataForm.questionMultiScore}}</a-col>
            <a-col class="fa-question-config-tr">
              <a @click="handleAddFixed(QUESTION_TYPE.MULTIPLY)">{{ dataForm.questionMultiFixed }}</a>
            </a-col>
            <a-col class="fa-question-config-tr">{{ dataForm.questionMultiNum - dataForm.questionMultiFixed }}</a-col>
          </div>
        </div>
      </div>

      <!-- 固定试题 -->
      <EduTestPaperQuestionFixedList ref="fixedListRef" :paper-id="testPaperId" :question-bank-ids="JSON.parse(record.eduFile)" @count="handleQuestionCount" />

    </a-form>
  </BasicPopup>
</template>
<script lang="ts" setup>
import { reactive, ref, toRefs, watch } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { BasicPopup, usePopupInner } from '/@/components/Popup';
import { Rule } from "/@/components/Form";
import type { FormInstance } from "ant-design-vue";
import { cloneDeep } from "lodash-es";
import { QUESTION_TYPE } from "/@/enums/zzEnums";
import { testPaperApi, testQuestionApi } from "/@/api";
import EduTestPaperQuestionFixedList from "/@/views/testManage/eduRecordManage/cube/EduTestPaperQuestionFixedList.vue";


interface State {
  dataForm: any,
  formRules: Record<any, Rule[]>,
}

const emit = defineEmits(['register', 'reload','created']);
const {createMessage} = useMessage();
const formElRef = ref<FormInstance>();
const [registerModal, {changeLoading, closePopup: closeModal, changeOkLoading}] = usePopupInner(init);

const defaultData = {
  testLimitCount: 1,
  limitTime: '01:00:00',
  totalScore: 100,
  passScore: 60,
  // ----------- 判断题 -----------
  questionJudgeTotal: 0, // 题目总数
  questionJudgeFixed: 0, // 固定题目数
  questionJudgeNum: 10,
  questionJudgeScore: 3,
  // ----------- 单选题 -----------
  questionSingleTotal: 0, // 题目总数
  questionSingleFixed: 0, // 固定题目数
  questionSingleNum: 10,
  questionSingleScore: 3,
  // ----------- 多选题 -----------
  questionMultiTotal: 0, // 题目总数
  questionMultiFixed: 0, // 固定题目数
  questionMultiNum: 10,
  questionMultiScore: 4,
};
const state = reactive<State>({
  dataForm: cloneDeep(defaultData),
  formRules: {
    testLimitCount: [{ required: true, message: '考试最大次数不能为空', trigger: 'change' }],
    limitTime: [{ required: true, message: '考试限时不能为空', trigger: 'change' }],
    totalScore: [{ required: true, message: '试卷总分不能为空', trigger: 'change' }],
    passScore: [{ required: true, message: '及格分数不能为空', trigger: 'change' }],
    questionJudgeNum: [{ required: true, message: '判断题总数不能为空', trigger: 'change' }],
    questionJudgeScore: [{ required: true, message: '判断题分数不能为空', trigger: 'change' }],
    questionSingleNum: [{ required: true, message: '单选题总数不能为空', trigger: 'change' }],
    questionSingleScore: [{ required: true, message: '单选题分数不能为空', trigger: 'change' }],
    questionMultiNum: [{ required: true, message: '多选题总数不能为空', trigger: 'change' }],
    questionMultiScore: [{ required: true, message: '多选题分数不能为空', trigger: 'change' }],
  },
});
const { dataForm, formRules } = toRefs(state);
const record = ref<any>();
const testPaperId = ref<any>();
const testPaper = ref<any>();
const fixedListRef = ref<any>();

async function init(data:any) {
  console.log('EduTestPaperForm', data)
  record.value = data
  testPaperId.value = data.testPaperId

  changeLoading(true)
  try {
    const ret = await testPaperApi.getById(data.testPaperId)
    testPaper.value = ret.data
    state.dataForm = {
      ...state.dataForm,
      ...ret.data,
    }

    // 统计试题数量
    const questionBankIds = JSON.parse(data.eduFile);
    testQuestionApi.countByType(questionBankIds).then(ret => {
      const { judgeCount, singleCount, multipleCount } = ret.data
      state.dataForm.questionJudgeTotal = judgeCount
      state.dataForm.questionSingleTotal = singleCount
      state.dataForm.questionMultiTotal = multipleCount

      state.formRules.questionJudgeNum = [
        { required: true, message: '判断题总数不能为空', trigger: 'change' },
        { type: 'number', min: 0, max: judgeCount, message: `判断题数量要在0-${judgeCount}之间` },
      ]
      state.formRules.questionSingleNum = [
        { required: true, message: '单选题总数不能为空', trigger: 'change' },
        { type: 'number', min: 0, max: singleCount, message: `单选题数量要在0-${singleCount}之间` },
      ]
      state.formRules.questionMultiNum = [
        { required: true, message: '多选题总数不能为空', trigger: 'change' },
        { type: 'number', min: 0, max: multipleCount, message: `多选题数量要在0-${multipleCount}之间` },
      ]
    })

    changeLoading(false)
  } catch (e) {
    changeLoading(false)
  }
}

function handleClose() {
  closeModal();
}

async function handleSubmit() {
  try {
    const values = await formElRef?.value?.validate();
    if (!values) return;
    console.log('values', values)
    const params = {
      ...testPaper.value,
      ...values
    }
    // if (state.dataForm.questionJudgeTotal < params.questionJudgeNum) {
    //   createMessage.error('所选判断题题目数超过题库总数')
    //   return
    // }
    // if (state.dataForm.questionSingleTotal < params.questionSingleNum) {
    //   createMessage.error('所选单选题题题目数超过题库总数')
    //   return
    // }
    // if (state.dataForm.questionMultiTotal < params.questionMultiNum) {
    //   createMessage.error('所选多选题题题目数超过题库总数')
    //   return
    // }
    if (state.dataForm.passScore > params.totalScore) {
      createMessage.error('及格分数不能大于总分')
      return
    }

    changeOkLoading(true)
    await testPaperApi.update(params)
    createMessage.success('更新试卷配置')
    changeOkLoading(false)
    closeModal()
  } catch (_) {
    changeOkLoading(false)
  }
}

watch(() => state.dataForm, () => {
  // console.log('watch', state.dataForm)
  const { questionJudgeNum, questionJudgeScore, questionSingleNum, questionSingleScore, questionMultiNum, questionMultiScore } = state.dataForm;
  state.dataForm.totalScore = questionJudgeNum * questionJudgeScore + questionSingleNum * questionSingleScore + questionMultiNum * questionMultiScore
}, { deep: true, immediate: true })

function handleAddFixed(questionType: QUESTION_TYPE) {
  fixedListRef.value.addHandle(questionType)
}

function handleQuestionCount(countMap:any) {
  console.log('handleQuestionCount', countMap)
  state.dataForm.questionJudgeFixed = countMap[QUESTION_TYPE.JUDGE]
  state.dataForm.questionSingleFixed = countMap[QUESTION_TYPE.SINGLE]
  state.dataForm.questionMultiFixed = countMap[QUESTION_TYPE.MULTIPLY]
}
</script>

<style lang="less">
.fa-question-config-table {
  border-top: 1px solid #e8e8e8;
  border-left: 1px solid #e8e8e8;
  margin-bottom: 12px;
}

.fa-question-config-thead-tr {
  display: flex;
}

.fa-question-config-thead {
  flex: 1;
  padding: 6px 12px;
  font-weight: 600;
  text-align: center;
  border-right: 1px solid #e8e8e8;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.fa-question-config-tr {
  flex: 1;
  padding: 4px 12px;
  text-align: center;
  border-right: 1px solid #e8e8e8;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fa-question-config-tr .ant-form-item {
  width: 100%;
  margin-bottom: 0 !important;
}

.fa-question-config-tr input {
  text-align: center;
}
</style>
