<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-left" style="width: 250px;">
      <BasicLeftTree
        title="培训分类"
        ref="leftTreeRef"
        :treeData="treeData"
        :loading="treeLoading"
        :fieldNames="{ key: 'id', title: 'name' }"
        @reload="reloadTree"
        @select="handleTreeSelect"
        showToolbar/>
    </div>

    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">新增</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handleBatchDelete()" :disabled="selList.length === 0">批量删除</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)"/>
            </template>
          </template>
        </BasicTable>
      </div>

      <Form @register="registerForm" @reload="reload" @created="handleCreated"/>
      <EduTestPaperForm @register="registerQuestionConfigForm" @reload="reload"/>
      <EduRecordDetailDrawer @register="registerDetailDrawer" />
      <ProcessForm @register="registerProcessForm" @reload="reload"/>

    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref, toRefs, unref} from "vue";
import {BasicLeftTree, TreeActionType} from "/@/components/Tree";
import {ActionItem, BasicColumn, BasicTable, TableAction, useTable} from "/@/components/Table";
import {testEduClassTreeApi, testEduRecordApi, eduProcessApi} from "/@/api";
import {useMessage} from "/@/hooks/web/useMessage";
import Form from "./EduRecordForm.vue";
import EduTestPaperForm from "./EduTestPaperForm.vue";
import {usePopup} from "/@/components/Popup";
import {genEditBtn, genDeleteBtn, genQueryKeyword, genQueryDept, genQueryCommon, genQuerySearch} from "/@/utils/tableUtils";
import { useDrawer } from "/@/components/Drawer";
import EduRecordDetailDrawer from "/@/views/testManage/eduRecordManage/EduRecordDetailDrawer.vue";
import {eduRecordColumn} from "/@/enums/eduEnum";
import ProcessForm from "/@/views/study/eduProcessManage/ProcessForm.vue";


defineOptions({name: 'testManage-eduRecordManage'});

interface State {
  treeData: any[];
  treeLoading: boolean;
}

const state = reactive<State>({
  treeData: [],
  treeLoading: false,
});
const {treeData, treeLoading} = toRefs(state);
const leftTreeRef = ref<Nullable<TreeActionType>>(null);
const [registerForm, {openPopup: openFormModal}] = usePopup();
const [registerQuestionConfigForm, {openPopup: openQuestionConfigFormModal}] = usePopup();
const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
const [registerProcessForm, {openPopup: openProcessFormPopup}] = usePopup();


async function init() {
  setLoading(true);
  await reloadTree();
  searchInfo.eduClassId = state.treeData[0]?.id;
  const leftTree = unref(leftTreeRef);
  leftTree?.setSelectedKeys([searchInfo.eduClassId]);
  getForm().resetFields();
  reload()
}

// ----------------------------------- left tree -----------------------------------
/** 重新加载Tree数据 */
async function reloadTree() {
  state.treeLoading = true;
  const ret = await testEduClassTreeApi.allTree();
  // state.treeData = ret.data;
  state.treeData = [
    {id: '', name: '全部'},
    ...ret.data,
  ];
  state.treeLoading = false
}

/** Tree节点点击 */
function handleTreeSelect(id: any) {
  console.log('handleTreeSelect', id)
  // if (!id || searchInfo.eduClassId === id) return;
  searchInfo.eduClassId = id;
  reload()
}

// ----------------------------------- right table -----------------------------------
const {createMessage, createConfirm} = useMessage();

const searchInfo = reactive({
  eduClassId: '', // 右table所属左treeId
  _sorter: 'f_creator_time DESC', // 按照创建时间倒序排列
});
const selList = ref<any[]>([]); // 选中的试题列表

const [registerTable, {reload, setLoading, getForm}] = useTable({
  api: testEduRecordApi.page,
  columns: eduRecordColumn,
  searchInfo,
  useSearchForm: true,
  clickToRowSelect: true, // 点击选中条目
  rowSelection: { // 选中行
    onChange: (selectedRowKeys) => { // 选中行change
      console.log('selectedRowKeys', selectedRowKeys)
      selList.value = selectedRowKeys;
    },
  },
  immediate: false,
  ellipsis: false,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryDept('部门', 'organizeIds'),
      genQueryCommon('岗位', 'posId', 'PosSelect'),
    ],
  },
  actionColumn: {
    width: 320,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record: any): ActionItem[] {
  return [
    {
      label: '详情',
      onClick: detailHandle.bind(null, record),
    },
    {
      label: '进度明细',
      onClick: processDetail.bind(null, record),
    },
    {
      label: '同步成绩',
      onClick: syncUserTestStatus.bind(null, record),
    },
    genEditBtn(record, addOrUpdateHandle),
    genDeleteBtn(record, handleDelete),
    {
      label: '试卷配置',
      onClick: questionConfigHandle.bind(null, record),
      ifShow: record.isNeedTest === 1,
    },
  ];
}

async function syncUserTestStatus(record) {
  setLoading(true)
  let res =await eduProcessApi.syncUserTestStatus(record.id);
  setLoading(false)

  if(!res.data) {
    createMessage.error('同步失败');
    return
  }
}

function handleDelete(id: any) {
  testEduRecordApi.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function detailHandle(record) {
  openDetailDrawer(true, {eduId:record.id, eduRecord:record,userId:undefined});
}

async function getUserStatus(eduRecordId: string, processType: string) {
  const res = await eduProcessApi.getUserStatusListByEdu({
    eduRecordId:eduRecordId,
    processType:processType
  });
  if (!Array.isArray(res.data) || res.data.length <= 0) return new Array;
  return res.data
  // exportUserStatus.value = res.data
}

async function processDetail(record){
  let userStatusList =await getUserStatus(record.id, "")
  openProcessFormPopup(true, {detail: userStatusList, id: record.id,eduName:record.eduName});
}

function addOrUpdateHandle(id = '') {
  openFormModal(true, {id, eduClassId: searchInfo.eduClassId});
}

function handleCreated(data: any) {
  console.log('handleCreated', data)
  if (data.isNeedTest === 1) { // 需要考试，打开考试配置界面
    openQuestionConfigFormModal(true, data);
  }
}

/** 试卷配置 */
async function questionConfigHandle(record: any) {
  openQuestionConfigFormModal(true, record);
}

function handleBatchDelete() {
  createConfirm({
    iconType: 'warning',
    title: '删除',
    content: '是否确认删除所有选中计划？',
    onOk: () => {
      setLoading(true)
      testEduRecordApi.removeBatchByIds(selList.value).then(_res => {
        setLoading(false);
        reload();
      }).catch(() => setLoading(false));
    },
  });
}

onMounted(() => init());
</script>

<style scoped>
</style>
