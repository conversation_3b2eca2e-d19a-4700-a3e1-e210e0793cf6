<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" width="1000px" class="full-drawer" title="培训详情" @close="handleClose">
    <div class="fa-full fa-pl12 fa-pr12 fa-pb12 fa-scroll-auto-y">
      <a-tabs class="jnpf-content-wrapper-tabs" destroyInactiveTabPane>
        <a-tab-pane key="1" tab="材料封面" v-if="showCover">
          <EduCoverComponent :edu-cover-info="eduCoverInfo" />
        </a-tab-pane>

        <a-tab-pane key="2" tab="签到表" v-if="eduRecord.isNeedSign ===1">
          <SignRecordComponent :sign-info="signInfo"/>
        </a-tab-pane>

        <a-tab-pane key="3" tab="成绩表" v-if="showCover && eduRecord.isNeedTest ===1">
          <ScoreTableComponent :score-info="scoreInfo"/>
        </a-tab-pane>

        <a-tab-pane key="4" tab="学员试卷" v-if="eduRecord.isNeedTest ===1">
          <a-button @click="batchPrintPaper" style="color: #1890ff">批量打印试卷</a-button>
          <a-button @click="changeListByUser" style="margin-left: 8px;color: #1890ff">筛选用户</a-button>
<!--          <a-button @click="changeListByUser"  ></a-button>-->
          <JnpfDateRange v-model="timeRange" format="YYYY-MM-DD" @change="changeDate" style="margin-left: 8px;color: #1890ff">筛选时间</JnpfDateRange>
          <a-button @click="reloadGrade(undefined)" style="margin-left: 8px;color: #1890ff">重置</a-button>
          <div>
            <a-table :columns="gradeColumns" :data-source="gradeList"
                     :row-selection="{selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange}"
                     rowKey="id">
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'creatorTime'">
                  <span>{{ formatToDate(record.creatorTime) }}</span>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <TableAction :actions="getExamTableActions(record)"/>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <a-tab-pane key="5" tab="评估表" v-if="eduRecord.isNeedEvaluate ===1">
          <a-button @click="batchPrintEvalua" style="color: #1890ff">批量打印评估表</a-button>
          <a-table :columns="evaluColumns" :data-source="evaluList"
                   :row-selection="{selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange}"
                   rowKey="id">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'userName'">
                <span>{{ record.userEntity.realName }}</span>
              </template>
              <template v-if="column.dataIndex === 'creatorTime'">
                <span>{{ formatToDate(record.creatorTime) }}</span>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <TableAction :actions="getEvaluTableActions(record)"/>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="6" tab="培训视频记录" v-if="eduRecord.isMorningMeeting">
          <div v-for="item in meetingVideoList">
            <div style="padding: 40px;" class="fa-flex-center">
              <video
                id="my-player"
                controls
                preload="auto"
                width="1000"
                height="800"
                data-setup="{}">
                <source :src="fileSaveApi.getFileLocal(item.id)" type="video/mp4"/>
<!--                <source :src="item.url" type="video/mp4"/>-->
              </video>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="7" tab="培训照片记录" v-if="eduRecord.isMorningMeeting">
          <div v-for="item in meetingPicList">
            <div style="padding: 40px;" class="fa-flex-center">
              <a-image :src="fileSaveApi.getFileLocal(item.id)" ></a-image>
<!--              <a-image :src=item.url ></a-image>-->
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <ExamRecordForm @register="registerExamRecordForm" />
    <EvaluationRecordForm @register="registerEvaluationRecordForm" />
    <UsersSelectModal @register="registerUsersSelect" :getDataApi="testGradeApi.getExamUsers" :saveDataApi="testGradeApi.getGradesByUser" @initData="reloadGrade"/>

  </BasicDrawer>
</template>
<script lang="ts" setup>
import ExamRecordForm from '/@/views/testManage/testMaterial/examRecordManage/Form.vue'
import EvaluationRecordForm from '/@/views/testManage/testMaterial/evaluationTableManage/Form.vue'
import EduCoverComponent from "/@/views/testManage/testMaterial/eduCoverManage/EduCoverComponent.vue";
import ScoreTableComponent from "/@/views/testManage/testMaterial/scoreTableManage/ScoreTableComponent.vue";
import SignRecordComponent from "/@/views/testManage/testMaterial/signRecordManage/SignRecordComponent.vue";

import {BasicDrawer, useDrawerInner} from '/@/components/Drawer';
import {reactive, ref} from "vue";
import {
  fileSaveApi,
  studySpecialApprovalApi,
  testEduMorningMeetingApi,
  testEduRecordApi,
  testGradeApi,
  testScoreTableApi,
  testSignApi,
  testUserEduRecordVideoWatchApi as userVideoWatchApi,
  testUserEvaluationApi
} from "/@/api";
import testEduCover from "/@/api/testManage/testMaterial/testEduCover";
import {ActionItem, TableAction} from "/@/components/Table";
import {usePopup} from "/@/components/Popup";
import {getInfo as getUserInfo} from "/@/views/permission/user/helper/api";
import {formatToDate} from "/@/utils/dateUtil";
import {Key} from "path-to-regexp";
import UsersSelectModal from "/@/views/permission/authorize/components/UsersSelectModal.vue";
import {useModal} from "/@/components/Modal";
import {STUDY_SPECIAL_TYPE_ENUM} from "/@/enums/zzEnums";
import {useMessage} from "/@/hooks/web/useMessage";

defineOptions({name: 'EduRecordDetailDrawer'});
const emit = defineEmits(['register', 'refresh']);
const [registerDrawer, { changeLoading, closeDrawer }] = useDrawerInner(init);
const [registerExamRecordForm, {openPopup: openExamPopup}] = usePopup();
const [registerEvaluationRecordForm, {openPopup: openEvaluPopup}] = usePopup();
const [registerUsersSelect, { openModal: registerUsersSelectModal }] = useModal();

const {createMessage,createConfirm} = useMessage();

const gradeColumns =[
  {title: '姓名', dataIndex: 'userName', width: 100},
  {title: '分数', dataIndex: 'score', width: 70},
  {title: '考试次数', dataIndex: 'testTimes', width: 80},
  {title: '考试时间', dataIndex: 'creatorTime', width: 150, format: 'date|YYYY-MM-DD HH:mm'},
  {title: '试卷', dataIndex: 'action', width: 80},
];
const evaluColumns =[
  {title: '姓名', dataIndex: 'userName', width: 100},
  {title: '考试时间', dataIndex: 'creatorTime', width: 150, format: 'date|YYYY-MM-DD HH:mm'},
  {title: '评估表', dataIndex: 'action', width: 80},
];

const state = reactive<{
  selectedRowKeys: Key[];
  loading: boolean;
}>({
  selectedRowKeys: [], // Check here to configure the default column
  loading: false,
});

const onSelectChange = (selectedRowKeys: Key[]) => {
  console.log('selectedRowKeys changed: ', selectedRowKeys);
  state.selectedRowKeys = selectedRowKeys;
};

const id = ref('')
const approvalId = ref('')

const eduRecord = ref<any>({})
const eduCoverInfo = ref<any>({})
const scoreInfo = ref<any>({})
const signInfo = ref<any>({})
const meetingPicList = ref<string[]>([])
const meetingVideoList = ref<string[]>([])
const timeRange = ref<any[]>([])

const gradeList = ref([])
const evaluList = ref([])

function getExamTableActions(record): ActionItem[] {
  return [
    {
      label: '考试详情',
      onClick: examDetail.bind(null, record),
    },
    {
      label: '删除记录',
      onClick: delExam.bind(null, record),
    },
  ];
}

function changeDate(obj) {
  console.log('changeDate msg', obj)
  console.log('gradeList', gradeList.value)
  if (!Array.isArray(obj) || obj.length != 2) return
  const startTime = obj[0]
  const endTime = obj[1]
  gradeList.value = gradeList.value.filter(i=>i.creatorTime < endTime && i.creatorTime > startTime)
}

function getEvaluTableActions(record): ActionItem[] {
  return [
    {
      label: '评估表',
      onClick: evaluationTable.bind(null, record),
    },
  ];
}

const userId = ref('')
const eduId = ref('')
const eduRecordId = ref('')
const showCover = ref<boolean>(true)

async function init(data) {
  eduRecord.value = data.eduRecord
  eduId.value = data.eduId
  eduRecordId.value = data.eduRecordId

  let res = await isMorningMeeting();
  eduRecord.value.isMorningMeeting = res
  await getMeetingImg()

  if (data.userId){
    userId.value = data.userId
    showCover.value = false
  }

  if (showCover.value) {
    await studySpecialApprovalApi.list({'selectTrainingId': eduId.value})
      .then(_res => {
        if (_res.data.length > 0) {
          approvalId.value = _res.data[0].id;
        }
      })

    await setEduCoverInfo();
  }
  if (eduRecord.value.isNeedSign === 1) {
    await setSignInfo(eduRecord.value);
  }

  if (eduRecord.value.isNeedTest === 1) {
    if (showCover.value) {
      await setScoreInfo(eduRecord.value);
    }

    await setGradeList(eduId.value);
  }

  if (eduRecord.value.isNeedEvaluate === 1) {
    await setEvaluList(eduId.value);
  }

}

async function setGradeList(eduId: any) {
  await testGradeApi.getUserMaxGrade({
    eduId:eduId,
    userId:userId.value,
    eduRecordId:eduRecordId.value||"",
  })
    .then(_res => {
    if (_res.data.length > 0) {
      gradeList.value = _res.data
    }
  })
}

async function setEvaluList(eduId: any) {
  await testUserEvaluationApi.list({
    eduId: eduId,
    userId:userId.value,
    userEduRecordId:eduRecordId.value||"",
  }).then(_res => {
    if (_res.data.length > 0) {
      evaluList.value = _res.data
    }
  })
}


async function setEduCoverInfo() {
  if (approvalId.value) {
    testEduCover.getEduCoverInfo(approvalId.value).then(res => {
      if (res.data) {
        eduCoverInfo.value = res.data
      }
    })
  }
}

function changeListByUser(){
  registerUsersSelectModal(true, {id: eduId.value});
}

function reloadGrade(msg){
  if(msg){
    gradeList.value = msg
  }else {
    setGradeList(eduId.value)
  }
}

async function  setScoreInfo(data){
  scoreInfo.value.eduName = data.eduName
  scoreInfo.value.eduTime = data.planStartTime
  scoreInfo.value.eduLocation = data.eduLocation

  if (data.eduTeacherId) {
    getUserInfo(data.eduTeacherId).then(res => {
      if (res.data.realName) {
        scoreInfo.value.eduTeacherName = res.data.realName
      }
    })
  }

  if (approvalId.value) {
    testScoreTableApi.getTestUserScore(approvalId.value).then(res => {
      scoreInfo.value.userScoreList = res.data
    })
  }
  console.log('scoreInfo',scoreInfo.value)
}

async function  setSignInfo(data){
  signInfo.value.eduName = data.eduName
  signInfo.value.eduTime = data.planStartTime
  signInfo.value.eduLocation = data.eduLocation

  if (data.eduTeacherId) {
    getUserInfo(data.eduTeacherId).then(res => {
      if (res.data.realName) {
        signInfo.value.eduTeacherName = res.data.realName
      }
    })
  }

  testSignApi.getSignedInfoByEdu({
    eduId:eduId.value,
    userId:userId.value,
    eduRecordId:eduRecordId.value||"",
  }).then(res => {
    signInfo.value.signedUserInfoList = res.data
  })

}
function batchPrintPaper(){
  console.log('selectedRowKeys',state.selectedRowKeys)
  openExamPopup(true,state.selectedRowKeys)
}
function batchPrintEvalua(){
  console.log('selectedRowKeys',state.selectedRowKeys)
  openEvaluPopup(true,state.selectedRowKeys)
}


function examDetail(record: any){
  openExamPopup(true,new Array(record.id));
}

function delExam(record:any) {
  createConfirm({
    iconType: 'warning',
    title: '提示',
    content: '此操作将通过该记录，是否继续?',
    onOk: () => {
      testGradeApi.remove(record.id)
      setGradeList(eduId.value);
    },
  });
}

function evaluationTable(record:any) {
  console.log('评估表')
  openEvaluPopup(true, new Array(record.id));
}

async function isMorningMeeting() {
  let res = await testEduRecordApi.getStudyType(eduRecord.value.id);
  // console.log('res',res)
  if(!res || !res.data) return false;

  if (res.data === STUDY_SPECIAL_TYPE_ENUM.MORNING_MEETING ||
    res.data === STUDY_SPECIAL_TYPE_ENUM.ASSEMBLE_TRAIN) return true;
}

async function getMeetingImg() {
  console.log('getMeetingImg')
  if (!eduRecord.value.isMorningMeeting) return;

  let res = await testEduMorningMeetingApi.list({
    recordId: eduRecord.value.id
  })
  if (!res || !res.data) return;

  if (Array.isArray(res.data[0].picList) && res.data[0].picList.length > 0) {
    let picRes = await fileSaveApi.getByIds(res.data[0].picList)
    console.log('picList',picRes)
    if(picRes && picRes.data && Array.isArray(picRes.data))
      meetingPicList.value = picRes.data
  }

  if (Array.isArray(res.data[0].videoList) && res.data[0].videoList.length > 0){
    let videoRes = await fileSaveApi.getByIds(res.data[0].videoList)
    console.log('videoRes',videoRes)
    if(videoRes && videoRes.data && Array.isArray(videoRes.data))
      meetingVideoList.value = videoRes.data
  }
}

function handleClose(){
  console.log('close the Drawer。。。')

  approvalId.value = ''
  eduRecord.value = {}
  eduCoverInfo.value = {}
  signInfo.value = {}
  scoreInfo.value = {}
  gradeList.value = []
  evaluList.value = []
  closeDrawer();
}
</script>
<style lang="less">
</style>
