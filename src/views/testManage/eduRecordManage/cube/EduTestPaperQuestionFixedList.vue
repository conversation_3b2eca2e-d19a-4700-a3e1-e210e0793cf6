<template>
  <div style="">
    <a-space class="fa-mb-12">
      <a-button @click="reload()">刷新</a-button>
<!--      <a-button preIcon="icon-ym icon-ym-btn-add" @click="addHandle()">新增固定选题</a-button>-->
<!--      <a-button preIcon="icon-ym icon-ym-btn-remove" @click="addHandle()" danger>清空</a-button>-->
    </a-space>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="getTableActions(record)" />
        </template>
      </template>
    </BasicTable>

    <!-- 选择问题弹窗 -->
    <QuestionSelectModal @register="registerQuestionSelectModal" @selectQuestion="selectQuestionHandle"/>
  </div>
</template>

<script setup lang="ts">
import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '/@/components/Table';
import { testPaperQuestionApi } from "/@/api";
import { useModal } from "/@/components/Modal";
import QuestionSelectModal from "/@/views/testManage/paperQuestionConfig/QuestionSelectModal.vue";
import { useMessage } from "/@/hooks/web/useMessage";
import { groupBy, trim } from "lodash-es";
import { QUESTION_TYPE, QUESTION_TYPE_MAP } from "/@/enums/zzEnums";
import { reactive, toRefs } from "vue";

const emit = defineEmits(['count']);
const props = defineProps(['paperId', 'questionType', 'questionBankIds']); // 暴露给外部传入的属性
const { paperId, questionType, questionBankIds } = toRefs(props); // ✅ 保持响应性

defineExpose({addHandle}); // 暴露给外部调用的方法
const {createMessage} = useMessage();
const [registerQuestionSelectModal, {openModal: openQuestionSelectModal}] = useModal();
const searchInfo = reactive({
  paperId: paperId,
  questionType: questionType,
});

const columns: BasicColumn[] = [
  {title: '所属题库(教材)', dataIndex: ['testQuestion', 'questionBank'], width: 200},
  { title: '题目', dataIndex: ['testQuestion', 'questionContent'] },
  {
    title: '题目选项', dataIndex: ['testQuestion', 'questionChoices'],
    customRender: ({record}) => {
      if (trim(record.questionChoices) === '') return '';
      try {
        const array = JSON.parse(record.questionChoices);
        const str = array.map(i => `${i.value}: ${i.option}`).join(' | ')
        return str
      } catch (e) {
      }
      return '';
    },
  },
  {
    title: '题目答案', dataIndex: ['testQuestion', 'questionAnswer'], width: 100,
  },
  {
    title: '题目类型', dataIndex: ['testQuestion', 'questionType'], width: 100,
    customRender: ({record}) => (QUESTION_TYPE_MAP[record.questionType])
  },
];
const [registerTable, { reload }] = useTable({
  api: testPaperQuestionApi.list,
  columns,
  showTableSetting: false,
  pagination: false,
  searchInfo: searchInfo,
  actionColumn: {
    width: 60,
    title: '操作',
    dataIndex: 'action',
  },
  afterFetch: list => {
    console.log('afterFetch', list)
    // 统计分类
    const countGroup = groupBy(list, i => i.testQuestion.questionType)
    const countMap = {
      [QUESTION_TYPE.JUDGE]: countGroup[QUESTION_TYPE.JUDGE]?.length || 0,
      [QUESTION_TYPE.SINGLE]: countGroup[QUESTION_TYPE.SINGLE]?.length || 0,
      [QUESTION_TYPE.MULTIPLY]: countGroup[QUESTION_TYPE.MULTIPLY]?.length || 0,
    }
    console.log('countMap', countMap)
    emit('count', countMap)
  },
});

function getTableActions(record): ActionItem[] {
  return [
    {
      label: '删除',
      color: 'error',
      modelConfirm: {
        onOk: handleDelete.bind(null, record.id),
      },
    },
  ];
}
function handleDelete(id:string) {
  testPaperQuestionApi.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function addHandle(questionType) {
  openQuestionSelectModal(true, { questionType, questionBankIds })
}

/**
 * 选中问题回调处理
 * @param data
 */
function selectQuestionHandle({ questions }) {
  const list = questions.map((item, index) => ({
    paperId: searchInfo.paperId,
    questionId: item.id,
    questionType: item.questionType,
    sort: index,
  }))
  testPaperQuestionApi.saveBatch(list).then(_res => {
    createMessage.success('添加固定题目成功')
    reload()
  })
}

// watch(() => [props.paperId, props.questionType], (cv, pv) => {
//   console.log('props.paperId', cv, pv)
//   searchInfo.paperId = cv[0]
//   searchInfo.questionType = cv[1]
//   reload()
// }, { deep: true })
</script>
