<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :footer="null" title="选择培训" :width="1000" :min-height="600" class="fa-select-modal">
    <BasicTable @register="registerTable" :max-height="400">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="getTableActions(record)"/>
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive } from "vue";
import { BasicModal, useModalInner } from '/@/components/Modal';
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from "/@/components/Table";
import { testEduRecordApi } from "/@/api";
import { genQuerySearch } from "/@/utils/tableUtils";
import { useMessage } from "/@/hooks/web/useMessage";


const emit = defineEmits(['register', 'select']); // 需要暴露的事件
const [registerModal, { closeModal }] = useModalInner(init);
const { createConfirm } = useMessage();


function init(data:any) {
  console.log('data', data)
  searchInfo.organizeIds = data.organizeId;
  // searchInfo.posId = data.posId;
  reload()
}

const searchInfo = reactive({
  organizeIds: '',
  posId: '',
  _sorter: 'f_creator_time DESC', // 按照创建时间倒序排列
});

const columns: BasicColumn[] = [
  {title: '培训名称', dataIndex: 'eduName', width: 210},
  {title: '培训编号', dataIndex: 'eduNum', width: 210},
  {title: '培训老师', dataIndex: 'eduTeacherName', width: 100},
  {title: '培训地点', dataIndex: 'eduLocation'},
]

const [registerTable, {reload}] = useTable({
  api: testEduRecordApi.page,
  columns,
  searchInfo,
  useSearchForm: true,
  canColDrag: true,
  immediate: false,
  formConfig: {
    schemas: [
      genQuerySearch(),
    ],
  },
  actionColumn: {
    width: 60,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record: any): ActionItem[] {
  return [
    {
      label: '选择',
      onClick: selectHandle.bind(null, record),
    },
  ];
}

function selectHandle(record:any) {
  createConfirm({
    iconType: 'warning',
    title: '替换',
    content: '确定要替换选中的培训计划吗?',
    onOk: () => {
      closeModal();
      emit('select', record);
    },
  });
}
</script>
