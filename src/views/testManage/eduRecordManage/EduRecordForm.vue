<template>
  <BasicPopup v-bind="$attrs" @register="registerModal" :title="getTitle" showOkBtn @ok="handleSubmit" @close="handleClose">
    <!--    <a-alert message="提醒info" type="warning" show-icon/>-->
    <BasicForm @register="registerForm" class="!px-10px !mt-10px" @field-value-change="handleFieldValueChange">
    </BasicForm>
    <div style="font-size: large;width: 900px;height: 50px;border-bottom: 1px solid #1a1a1a;padding: 10px;">
      <div style="width: 300px;padding-left: 50px;">
        课件
        <a-button type="link" size="large" @click="selectEduFile">选择课件</a-button>
      </div>
    </div>
    <div class="fa-flex-column" style="padding: 10px;">
      <div class="fa-flex-row fa-col-line" style="padding-bottom:5px;font-size: medium;">
        <div class="fa-col-div fa-flex-center" style="width: 180px; font-weight: bold; background: #eee;">课件编号</div>
        <div class="fa-col-div fa-flex-center" style="width: 180px; font-weight: bold; background: #eee;">课件名称</div>
        <div class="fa-col-div fa-flex-center" style="width: 180px; font-weight: bold; background: #eee;">所属部门</div>
        <div class="fa-col-div fa-flex-center" style="width: 150px; font-weight: bold; background: #eee;">培训分类</div>
        <div class="fa-col-div fa-flex-center" style="width: 100px; font-weight: bold; background: #eee;">自定义分类</div>
        <div class="fa-col-div fa-flex-center" style="width: 100px; font-weight: bold; background: #eee;">操作</div>

      </div>
      <div v-for="item in eduFileList" class="fa-flex-row fa-col-line" style="padding-bottom:5px;font-size: medium;">
        <div class="fa-col-div fa-flex-center" style="width: 180px;">{{ item.fileNum }}</div>
        <div class="fa-col-div fa-flex-center" style="width: 180px;">{{ item.fileName }}</div>
        <div class="fa-col-div fa-flex-center" style="width: 180px;">{{ item.departmentName }}</div>
        <div class="fa-col-div fa-flex-center" style="width: 150px;">{{ item.eduTypeName }}</div>
        <div class="fa-col-div fa-flex-center" style="width: 100px;">{{ item.fileClass }}</div>
        <div class="fa-col-div fa-flex-center" style="width: 100px;">
          <a-button type="link" size="large" @click="delEduFile(item.id)">删除</a-button>
        </div>
      </div>
    </div>

    <eduFileSelectModal @register="registerFileModal" @select-edu-file="selectEduFileHandle"/>
  </BasicPopup>
</template>
<script lang="ts" setup>
import {computed, ref, unref} from 'vue';
import {BasicForm, FormSchema, useForm} from '/@/components/Form';
import {useMessage} from '/@/hooks/web/useMessage';
import {eduPackRecordRelaApi, testEduClassTreeApi, testEduFileApi, testEduRecordApi, certificateTypeApi} from '/@/api';
import {BasicPopup, usePopupInner} from '/@/components/Popup';
import {genOrganOption} from "/@/api/permission/organize";
import {ORGANIZE_LIST_OPTION} from "/@/enums/zzEnums";
import {stringify} from "qs";
import {eduSchemas} from "/@/enums/eduEnum";
import eduFileSelectModal from "/@/views/testManage/eduFileManage/EduFileSelectModal.vue";
import {useModal} from "/@/components/Modal";
import { tryParseJSON } from "/@/utils/fa-utils";

const id = ref('');
const packId = ref(''); // 培训合集传入-合集ID
const groupId = ref(''); // 培训合集传入-合集分组ID
const organizeIds = ref(''); // 培训合集传入-部门ID
const posId = ref(['']); // 培训合集传入-岗位ID
const props =defineProps(['eduPackId']);
const eduFileList = ref<any[]>([])

const getTitle = computed(() => (!unref(id) ? '新建培训计划' : '编辑培训计划'));
const emit = defineEmits(['register', 'reload', 'created']);
const {createMessage} = useMessage();
const [registerForm, {setFieldsValue, validate, resetFields, updateSchema, getFieldsValue}] = useForm({
  labelWidth: 120,
  baseColProps: {sm: 12, xs: 24},
  schemas: eduSchemas,
});
const [registerModal, {changeLoading, closePopup: closeModal, changeOkLoading}] = usePopupInner(init);
const [registerFileModal, {openModal:openFileModal}]  = useModal();

let eduClassMap = [{
  "eduClass": undefined,
  "eduClassId": undefined
}];

function handleFieldValueChange(field, value) {
  if (id.value) return;
  if (field === 'eduClassId' || field === 'organizeIds') {
    genEduNumByOrganizeIds();
  }
}

//根据所选培训类型获取课件
// async function setFileList(fileRequest) {
//   const res = await testEduFileApi.list(fileRequest);
//   const eduFileOptions = res.data.map(i => ({
//     "fullName": i.fileName,
//     "id": i.id,
//   }))
//
//   updateSchema({field: 'eduFile', componentProps: {options: eduFileOptions}});
// }

async function setFileList(resData) {
  let fileIdArr = JSON.parse(resData.data.eduFile);
  if (!Array.isArray(fileIdArr) || fileIdArr.length <= 0) return;

  let fileArr = await testEduFileApi.page({'id#$in': fileIdArr});
  if (fileArr && fileArr.data && Array.isArray(fileArr.data.list)) {
    fileArr.data.list.forEach(file => {
      eduFileList.value.push(file)
    })
  }
  console.log('eduFileList',eduFileList.value)
}

async function delEduFile(fileId) {
  eduFileList.value = eduFileList.value.filter(item => item.id !== fileId)
}

// 查询监护证类型
async function setTypeList(fileRequest){
  const res = await certificateTypeApi.list(fileRequest);
  const cerTypeOptions = res.data.map(i => ({
    "fullName": i.name,
    "id": i.id,
  }))
  updateSchema({field: 'certificateType', componentProps: {options: cerTypeOptions}});
}

//部门字段一旦有值，即生成课件编号
async function genEduNumByOrganizeIds() {
  const { eduClassId, organizeIds } = getFieldsValue()
  await testEduRecordApi.genEduNum({ eduClassId, organizeIds }).then(res => {
    setFieldsValue({ eduNum: res.msg })
  })
}

async function init(data) {
  resetFields();
  id.value = data.id;
  packId.value = data.packId;
  groupId.value = data.groupId;
  organizeIds.value = data.organizeIds;
  posId.value.push(stringify(data.posId));

  const ret = await testEduClassTreeApi.all()
  eduClassMap = ret.data.map(i => ({
    "eduClass": i.className,
    "eduClassId": i.id
  }))

  await updateDepartmentId();
  // setFileList({});
  setTypeList({});

  if (data.organizeIds) {
    updateSchema({ field: 'organizeIds', componentProps: { disabled: true } })
  }
  if (data.posId) {
    updateSchema({ field: 'posId', componentProps: { disabled: true } })
  }

  if (id.value) {
    changeLoading(true);
    testEduRecordApi.getById(id.value).then(res => {
      // const fileRequest = {
      //   eduTypeId: res.data.eduClassId,
      // };
      setFileList(res);

      setFieldsValue({
        ...res.data,
        posId: tryParseJSON(res.data.posId, []),
        eduFile: tryParseJSON(res.data.eduFile, []), // 多选类型表单需要传入array
        // organizeIds: JSON.parse(res.data.organizeIds),
        // isNeedSign: `${res.data.isNeedSign}`,
        // isNeedSignCommit: `${res.data.isNeedSignCommit}`,
        // isNeedTest: `${res.data.isNeedTest}`,
        // isNeedEvaluate: `${res.data.isNeedEvaluate}`,
        // isNeedGenerate: `${res.data.isNeedGenerate}`,
        status: `${res.data.status}`,
      });

      updateSchema([
        { field: 'organizeIds', componentProps: { disabled: true } },
        { field: 'posId', componentProps: { disabled: true } }
      ])

      changeLoading(false);
    });
  } else {
    setFieldsValue({
      eduClassId: data.eduClassId,
      organizeIds: data.organizeIds,
      posId: [data.posId],
    })
  }
}


function selectEduFile(){
  openFileModal(true,{});
}

async function updateDepartmentId() {
  const res = await genOrganOption(ORGANIZE_LIST_OPTION);
  updateSchema({field: 'organizeIds', componentProps: {options: res.data}})
  resetFields();
}

function selectEduFileHandle(msg){
  console.log('msg',msg)
  // eduFile
  if (Array.isArray(msg)) {
    // let fileArr = msg.map(file => file.id);
    // setFieldsValue({eduFile: fileArr})
    msg.forEach(file=>{
      eduFileList.value.push(file)
    })
  }
}

function handleClose() {
  eduClassMap = [];
  eduFileList.value = []
  closeModal();
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  // 获取对象的所有可枚举属性
  Object.keys(values).forEach(key => {
    if (values[key] === undefined) {
      // 如果属性值是undefined，则将其设置为null
      values[key] = null;
    }
  });

  let query = {
    ...values,
    id: id.value,
  };
  // 新增
  if (!id.value) {
    query = {
      ...query,
      packId: packId.value,
      groupId: groupId.value,
    }
  }

  if (Array.isArray(query.posId)) {
    query.posId = JSON.stringify(query.posId);
  }
  if (Array.isArray(eduFileList.value)) {
    let ids = eduFileList.value.map(file => file.id)
    query.eduFile = JSON.stringify(ids);
    eduFileList.value = []
  }
  if (Array.isArray(query.organizeIds)) {
    query.organizeIds = JSON.stringify(query.organizeIds);
  }
  query.eduClass = eduClassMap.find(item => item.eduClassId === query.eduClassId)?.eduClass;
  console.log('query', query)
  const formMethod = id.value ? testEduRecordApi.update : testEduRecordApi.save;
  formMethod(query)
    .then(res => {
      createMessage.success(res.msg);
      if (props.eduPackId) {
        eduPackRecordRelaApi.save({
          eduPackId: props.eduPackId,
          eduRecordId: res.data.id
        })
      }
      changeOkLoading(false);
      closeModal();
      emit('reload');
      if (!id.value) {
        emit('created', res.data);
      }
    }).catch(() => changeOkLoading(false));
}
</script>
