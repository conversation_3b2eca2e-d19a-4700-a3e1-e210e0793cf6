<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-left" style="width: 250px;">
      <BasicLeftTree
        title="培训分类"
        ref="leftTreeRef"
        :treeData="treeData"
        :loading="treeLoading"
        :fieldNames="{ key: 'id', title: 'name' }"
        @reload="reloadTree"
        @select="handleTreeSelect"
        showToolbar/>
    </div>

    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">新增</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handleBatchDelete()" :disabled="selList.length === 0">批量删除</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)"/>
            </template>
          </template>
        </BasicTable>
      </div>
      <EduPackForm @register="registerForm" @reload="reload" @created="handleCreated"/>
      <EduPackDetailConfig @register="registerDetailConfigForm" @reload="reload"/>
      <EduRecordDetailDrawer @register="registerDetailDrawer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref, toRefs, unref} from "vue";
import {BasicLeftTree, TreeActionType} from "/@/components/Tree";
import {ActionItem, BasicColumn, BasicTable, TableAction, useTable} from "/@/components/Table";
import {testEduClassTreeApi, testEduRecordPackApi} from "/@/api";
import {useMessage} from "/@/hooks/web/useMessage";
import EduPackForm from "./EduPackForm.vue";
import EduRecordDetailDrawer from "/@/views/testManage/eduRecordManage/EduRecordDetailDrawer.vue";
import {usePopup} from "/@/components/Popup";
import { genEditBtn, genDeleteBtn, genQueryKeyword } from "/@/utils/tableUtils";
import { useDrawer } from "/@/components/Drawer";
import EduPackDetailConfig from "./EduPackDetailConfig.vue";
import {eduPackCols} from "/@/enums/eduEnum";


defineOptions({name: 'testManage-eduRecordManage'});

interface State {
  treeData: any[];
  treeLoading: boolean;
}

const state = reactive<State>({
  treeData: [],
  treeLoading: false,
});
const {treeData, treeLoading} = toRefs(state);
const leftTreeRef = ref<Nullable<TreeActionType>>(null);
const [registerForm, {openPopup: openFormModal}] = usePopup();
const [registerDetailConfigForm, {openPopup: openDetailConfigFormModal}] = usePopup();
const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();


async function init() {
  setLoading(true);
  await reloadTree();
  searchInfo.eduClassId = state.treeData[0]?.id;
  const leftTree = unref(leftTreeRef);
  leftTree?.setSelectedKeys([searchInfo.eduClassId]);
  getForm().resetFields();
  reload()
}

// ----------------------------------- left tree -----------------------------------
/** 重新加载Tree数据 */
async function reloadTree() {
  state.treeLoading = true;
  const ret = await testEduClassTreeApi.allTree();
  // state.treeData = ret.data;
  state.treeData = [
    {id: '', name: '全部'},
    ...ret.data,
  ];
  state.treeLoading = false
}

/** Tree节点点击 */
function handleTreeSelect(id: any) {
  console.log('handleTreeSelect', id)
  // if (!id || searchInfo.eduClassId === id) return;
  searchInfo.eduClassId = id;
  reload()
}

// ----------------------------------- right table -----------------------------------
const {createMessage, createConfirm} = useMessage();

const searchInfo = reactive({
  eduClassId: '', // 右table所属左treeId
  _sorter: 'f_creator_time DESC', // 按照创建时间倒序排列
});
const selList = ref<any[]>([]); // 选中的试题列表

const [registerTable, {reload, setLoading, getForm}] = useTable({
  api: testEduRecordPackApi.page,
  columns: eduPackCols,
  searchInfo,
  useSearchForm: true,
  clickToRowSelect: true, // 点击选中条目
  rowSelection: { // 选中行
    onChange: (selectedRowKeys) => { // 选中行change
      console.log('selectedRowKeys', selectedRowKeys)
      selList.value = selectedRowKeys;
    },
  },
  immediate: false,
  formConfig: {
    schemas: [
      genQueryKeyword(),
    ],
  },
  actionColumn: {
    width: 190,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record: any): ActionItem[] {
  return [
    // {
    //   label: '详情',
    //   onClick: detailHandle.bind(null, record),
    // },
    genEditBtn(record, addOrUpdateHandle),
    {
      label: '详细配置',
      onClick: detailConfigHandle.bind(null, record),
    },
    genDeleteBtn(record, handleDelete),
  ];
}

function handleDelete(id: any) {
  testEduRecordPackApi.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function detailHandle(record) {
  openDetailDrawer(true, record);
}

function addOrUpdateHandle(id = '') {
  openFormModal(true, {id, eduClassId: searchInfo.eduClassId});
}

function handleCreated(data: any) {
  if (data.isNeedTest === 1) {
    openDetailConfigFormModal(true, data);
  }
}

/** 详情配置 */
async function detailConfigHandle(record: any) {
  openDetailConfigFormModal(true, record);
}

function handleBatchDelete() {
  createConfirm({
    iconType: 'warning',
    title: '删除',
    content: '是否确认删除所有选中计划？',
    onOk: () => {
      setLoading(true)
      testEduRecordPackApi.removeBatchByIds(selList.value).then(_res => {
        setLoading(false);
        reload();
      }).catch(() => setLoading(false));
    },
  });
}

onMounted(() => init());
</script>

<style scoped>
</style>
