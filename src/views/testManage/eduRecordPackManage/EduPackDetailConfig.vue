<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="配置培训计划合集" showOkBtn @ok="handleSubmit" @close="handleClose">
    <a-space>
      <a-button v-if="groupList.length === 0" @click="handleInitInTemplate">初始化入场三级培训</a-button>
    </a-space>

    <div class="fa-flex-column fa-p12">
      <div class="fa-flex-row fa-col-line">
        <div class="fa-col-div" style="width: 250px; font-weight: bold; background: #eee;">分组</div>
        <div class="fa-col-div" style="width: 300px; font-weight: bold; background: #eee;">培训名称</div>
        <div class="fa-col-div" style="width: 200px; font-weight: bold; background: #eee;">课件</div>
        <div class="fa-col-div" style="width: 230px; font-weight: bold; background: #eee;">操作</div>
      </div>

      <div v-for="item in groupList" :key="item.id" class="fa-flex-column fa-mb-12">
        <a-space class="fa-flex-row-center fa-p4" style="background: #44b4d7;">
          <div>{{item.name}}</div>
          <div> / </div>
          <div>{{item.type}}</div>
        </a-space>

        <!-- 培训group下的培训计划列表 -->
        <EduPackGroupLevelTopList v-if="item.type === '项目级'" :pack="pack" :group="item" />
        <EduPackGroupLevelOrgList v-if="item.type === '部门级'" :pack="pack" :group="item" />
        <EduPackGroupLevelPosList v-if="item.type === '岗位级'" :pack="pack" :group="item" />
      </div>
    </div>
  </BasicPopup>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { BasicPopup, usePopupInner } from '/@/components/Popup';
import { testEduRecordPackApi, testEduRecordPackGroupApi } from "/@/api";
import EduPackGroupLevelTopList from "./EduPackGroupLevelTopList.vue";
import EduPackGroupLevelOrgList from "./EduPackGroupLevelOrgList.vue";
import EduPackGroupLevelPosList from "./EduPackGroupLevelPosList.vue";
import { groupBy } from "lodash-es";

const emit = defineEmits(['register', 'reload']);
const {createMessage, createConfirm} = useMessage();
const [registerPopup, {changeLoading, closePopup: closeModal, changeOkLoading}] = usePopupInner(init);


const id = ref()
const pack = ref()
const groupList = ref<any[]>([])

async function init(data: any) {
  id.value = data.id;
  const res = await testEduRecordPackApi.getDetailById(data.id)
  // group position by organize.id
  res.data.depPosMap = groupBy(res.data.posList || [], i => i.organizeId)
  pack.value = res.data;
  await refresh();
}

async function refresh() {
  changeLoading(true)
  return testEduRecordPackGroupApi.list({ packId: id.value, '_sorter': 'sort ASC' }).then(res => {
    changeLoading(false)
    groupList.value = res.data;
  }).catch(() => changeLoading(false));
}

/** 初始化入场三级培训分组列表 */
function handleInitInTemplate() {
  createConfirm({
    iconType: 'warning',
    title: '初始化',
    content: '确定要 初始化入场三级培训分组 吗?',
    onOk: () => {
      const packId = id.value
      const params = [
        { packId, name: '一级培训', type: '项目级', sort: 0 },
        { packId, name: '二级培训', type: '部门级', sort: 1 },
        { packId, name: '三级培训', type: '岗位级', sort: 2 },
      ]
      testEduRecordPackGroupApi.saveBatch(params).then(res => {
        createMessage.success(res.msg)
        refresh()
      })
    },
  });
}

function handleClose() {
  groupList.value = []
  closeModal();
}

async function handleSubmit() {
  changeOkLoading(true);
  createMessage.success('success');
  changeOkLoading(false);
  closeModal();
  emit('reload');
}
</script>

<style>
.fa-col-line {
  height: 40px;
}

.fa-col-div {
  padding: 4px;
  //border-top: 1px solid #333;
  //border-left: 1px solid #333;
  border-bottom: 1px solid #333;
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
