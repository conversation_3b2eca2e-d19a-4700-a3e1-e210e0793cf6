<template>
  <div>
    <div class="fa-flex-column">
      <!-- 部门 -->
      <div v-if="group.type === '岗位级'" v-for="org in pack.organizeList" :key="org.id" class="fa-flex-row">
        <div style="width: 100px;" class="fa-col-div">{{ org.fullName }}</div>

        <!-- 岗位 -->
        <div v-if="group.type === '岗位级'" class="fa-flex-column">
          <div v-for="pos in (pack.depPosMap[org.id]||[])" :key="pos.id" class="fa-flex-row-center fa-col-line">
            <div style="width: 150px;" class="fa-col-div fa-full-h">{{ pos.fullName }}</div>
            <EduPackConfigBtn
              :pack-id="pack.id"
              :group-id="group.id"
              :org-id="org.id"
              :pos-id="pos.id"
              :list="getPosList(org.id, pos.id)"
              @reload="refresh"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { forEach, groupBy } from "lodash-es";
import { testEduRecordPackApi } from "/@/api";
import EduPackConfigBtn from "./cube/EduPackConfigBtn.vue";


const props = defineProps(['pack', 'group']); // 暴露给外部传入的属性
const {pack, group} = props

const list = ref<any[]>([])
const orgPosMapList = ref<any>({}) // 组织-岗位-培训计划list

async function refresh() {
  const res = await testEduRecordPackApi.getRecordList(pack.id, group.id)
  list.value = res.data
  const orgMapList = groupBy(res.data, i => i.organizeIds) // 按照组织分组
  console.log('orgMapList', orgMapList)
  orgPosMapList.value = {}
  forEach(orgMapList, (v, k) => {
    orgPosMapList.value[k] = groupBy(v, i => i.packPosId) // 按照部门分组
  })
  console.log('orgPosMapList', orgPosMapList.value)
}

function getPosList(orgId, posId) {
  const orgMap = orgPosMapList.value[orgId] || {}
  const posList = orgMap[posId]
  return posList;
}

watch(() => group.id, () => {
  refresh()
}, {deep: true, immediate: true})
</script>

<style scoped>
</style>
