<template>
  <div>
    <div class="fa-flex-column">
      <!-- 部门 -->
      <div v-if="group.type === '部门级'" v-for="org in pack.organizeList" :key="org.id" class="fa-flex-row-center fa-col-line">
        <div style="width: 250px;" class="fa-col-div fa-full-h">{{org.fullName}}</div>
        <EduPackConfigBtn
          :pack-id="pack.id"
          :group-id="group.id"
          :org-id="org.id"
          :list="orgMapList[org.id]"
          @reload="refresh"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { testEduRecordPackApi } from "/@/api";
import { groupBy } from "lodash-es";
import EduPackConfigBtn from "./cube/EduPackConfigBtn.vue";


const props = defineProps(['pack', 'group']); // 暴露给外部传入的属性
const { pack, group } = props

const list = ref<any[]>([])
const orgMapList = ref<any>({})

async function refresh() {
  const res = await testEduRecordPackApi.getRecordList(pack.id, group.id)
  list.value = res.data
  orgMapList.value = groupBy(res.data, i => i.organizeIds)
  // console.log('orgMapList', orgMapList.value)
}

watch(() => group.id, () => {
  refresh()
}, {deep: true, immediate: true})
</script>

<style scoped>
</style>
