<template>
  <div class="fa-flex-row fa-full-h">
    <div v-if="isNil(childList) || childList.length === 0" class="fa-col-div fa-full-h">
      <a-button type="link" size="small" @click="handleAddEduRecord">新增</a-button>
      <a-button type="link" size="small" @click="handleReplace">选择</a-button>
    </div>
    <!-- 到此应该只有一个计划 -->
    <div v-else v-for="item in childList" :key="item.id" class="fa-flex-row fa-full-h">
      <div class="fa-p6 fa-col-div" style="width: 300px;">{{item.eduName}}</div>
      <div class="fa-p6 fa-col-div" style="width: 200px;">{{(item.eduFileList||[]).map(i=>i.fileName).join()}}</div>
      <div class="fa-col-div" style="width: 230px;">
        <div>
          <a-button type="link" size="small" @click="handleReplace">替换</a-button>
          <a-button type="link" size="small" @click="detailHandle(item)">详情</a-button>
          <a-button type="link" size="small" @click="editHandle(item.id)">编辑</a-button>
          <a-button type="link" size="small" @click="questionConfigHandle(item)">试卷</a-button>
          <a-button type="link" size="small" :danger="true" @click="handleRemoveFromPack(item)">移除</a-button>
        </div>
      </div>
    </div>

    <EduRecordForm @register="registerForm" @reload="refresh" @created="handleCreated"/>
    <EduRecordSelectModal @register="registerEduRecordSelectForm" @select="handleSelectEdu" />
    <EduRecordDetailDrawer @register="registerDetailDrawer" />
    <EduTestPaperForm @register="registerQuestionConfigForm" @reload="refresh"/>
  </div>
</template>

<script setup lang="ts">
import { ref, toRefs, watch } from "vue";
import { isNil } from "lodash-es";
import { useMessage } from "/@/hooks/web/useMessage";
import { usePopup } from "/@/components/Popup";
import { useModal } from "/@/components/Modal";
import EduRecordSelectModal from "/@/views/testManage/eduRecordManage/EduRecordSelectModal.vue";
import EduRecordForm from "/@/views/testManage/eduRecordManage/EduRecordForm.vue";
import { testEduRecordPackApi } from "/@/api";
import EduRecordDetailDrawer from "/@/views/testManage/eduRecordManage/EduRecordDetailDrawer.vue";
import { useDrawer } from "/@/components/Drawer";
import EduTestPaperForm from "/@/views/testManage/eduRecordManage/EduTestPaperForm.vue";

const emit = defineEmits(['reload']);
const props = defineProps(['packId', 'groupId', 'orgId', 'posId', 'list']); // 暴露给外部传入的属性
const { packId, groupId, posId } = props;
const { orgId } = toRefs(props);

const childList = ref<any[]>([])

const { createMessage, createConfirm } = useMessage();
const [registerForm, {openPopup: openFormModal}] = usePopup();
const [registerEduRecordSelectForm, { openModal: openEduRecordSelectFormModal }] = useModal();
const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
const [registerQuestionConfigForm, {openPopup: openQuestionConfigFormModal}] = usePopup();

function refresh() {
  emit('reload')
}

function handleAddEduRecord() {
  openFormModal(true, { packId, groupId, organizeIds: orgId?.value, posId: posId });
}

function handleCreated(eduRecord:any) {
  console.log('posId',posId)
  testEduRecordPackApi.replace(packId, groupId, eduRecord.id, orgId?.value, posId).then(_res => {
    createMessage.success('新增成功')
    refresh()
  })
}

function handleReplace() {
  console.log('orgId', orgId?.value)
  openEduRecordSelectFormModal(true, { organizeId: orgId?.value, posId: posId })
}

function handleSelectEdu(eduRecord:any) {
  testEduRecordPackApi.replace(packId, groupId, eduRecord.id, orgId?.value, posId).then(_res => {
    createMessage.success('替换成功')
    refresh()
  })
}

function handleRemoveFromPack(item) {
  createConfirm({
    iconType: 'warning',
    title: '移出',
    content: '确定要移出选中的培训计划吗?',
    onOk: () => {
      testEduRecordPackApi.removeFromPack(packId, groupId, item.id, orgId?.value, posId).then(_res => {
        createMessage.success('移出成功')
        refresh()
      })
    },
  });
}

function detailHandle(record) {
  openDetailDrawer(true, record);
}

function editHandle(id = '') {
  openFormModal(true, {id});
}

/** 试卷配置 */
async function questionConfigHandle(record: any) {
  openQuestionConfigFormModal(true, record);
}

watch(() => props, () => {
  // console.log('props', props)
  childList.value = props.list;
}, { deep: true, immediate: true})
</script>

