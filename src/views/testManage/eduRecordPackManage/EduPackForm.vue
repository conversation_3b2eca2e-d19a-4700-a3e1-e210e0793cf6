<template>
  <BasicPopup v-bind="$attrs" @register="registerModal" :title="getTitle" showOkBtn @ok="handleSubmit" @close="handleClose">
    <!--    <a-alert message="提醒info" type="warning" show-icon/>-->
    <BasicForm @register="registerForm" class="!px-10px !mt-10px" @field-value-change="handleFieldValueChange">
    </BasicForm>
  </BasicPopup>
</template>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicForm, FormSchema, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { testEduClassTreeApi, testEduRecordPackApi } from '/@/api';
import { BasicPopup, usePopupInner } from '/@/components/Popup';
import { genOrganOption } from "/@/api/permission/organize";
import { BOOL_OPTIONS, ORGANIZE_LIST_OPTION } from "/@/enums/zzEnums";
import { genCommon, genInput, genSelect, genSelectMulti } from "/@/utils/formUtils";
import { isNil } from "lodash-es";

const id = ref('');
const getTitle = computed(() => (!unref(id) ? '新建培训计划合集' : '编辑培训计划合集'));
const emit = defineEmits(['register', 'reload', 'created']);
const {createMessage} = useMessage();
const [registerModal, {changeLoading, closePopup: closeModal, changeOkLoading}] = usePopupInner(init);

// ---------------------------------------培训合集参数---------------------------------------
const schemas: FormSchema[] = [
  {
    field: 'eduClassId',
    label: '培训分类',
    component: 'FaCascader',
    componentProps: {placeholder: '请选择培训分类', api: testEduClassTreeApi, showRoot: false},
    rules: [{required: true, trigger: 'change', message: '培训分类不能为空'}],
  },
  genInput('计划合集名称', 'packName'),
  genSelectMulti('培训部门', 'organizeIds'),
  genInput('计划合集编号', 'packNum'),
  // {
  //   field: 'posIds',
  //   label: '培训岗位',
  //   component: 'PosSelect',
  //   componentProps: {placeholder: '请选择培训岗位', multiple: true},
  //   rules: [{required: true, trigger: 'change', message: '培训岗位不能为空', type: 'array'}],
  // },
  genSelect('是否按顺序学习', 'isByOrder', true, 'boolean', BOOL_OPTIONS),
  genCommon('预计开始时间', 'planStartTime', 'DatePicker', false),
  genCommon('预计结束时间', 'planEndTime', 'DatePicker', false),
];
const [registerForm, {setFieldsValue, validate, resetFields, updateSchema, getFieldsValue}] = useForm({
  labelWidth: 120,
  baseColProps: {sm: 12, xs: 24},
  schemas: schemas,
});


function handleFieldValueChange(field, value) {
  if (field === 'eduClassId' || field === 'organizeIds') {
    genPackNumByOrganizeIds();
  }
}

//部门字段一旦有值，即生成课件编号
async function genPackNumByOrganizeIds() {
  const {eduClassId, organizeIds} = getFieldsValue()
  if (isNil(eduClassId) || isNil(organizeIds)) return;
  await testEduRecordPackApi.genPackNum({eduClassId, organizeIds}).then(res => {
    setFieldsValue({packNum: res.msg})
  })
}

async function init(data: any) {
  await resetFields();
  id.value = data.id;

  await updateDepartmentId();
  if (id.value) {
    changeLoading(true);
    testEduRecordPackApi.getById(id.value)
      .then(res => {
        setFieldsValue({
          ...res.data,
          organizeIds: JSON.parse(res.data.organizeIds),
          posIds: JSON.parse(res.data.posIds),
        })
        changeLoading(false);
      })
  } else {
    await setFieldsValue({eduClassId: data.eduClassId})
  }
}


async function updateDepartmentId() {
  const res = await genOrganOption(ORGANIZE_LIST_OPTION);
  let valArr = res.data.filter(op => op['fullName'] !== '项目部').map(op => op['id']);
  console.log('valArr',valArr)

  await updateSchema({field: 'organizeIds', componentProps: {options: res.data}})
  setFieldsValue({ 'organizeIds':valArr})
}

function handleClose() {
  closeModal();
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);

  const query = {
    ...values,
    organizeIds: JSON.stringify(values.organizeIds),
    posIds: JSON.stringify(values.posIds),
    id: id.value,
  };

  const formMethod = id.value ? testEduRecordPackApi.update : testEduRecordPackApi.save;
  formMethod(query).then(res => {
    createMessage.success(res.msg);
    changeOkLoading(false);
    closeModal();
    emit('reload');
    if (!id.value && res.data.isNeedTest == 1) {
      emit('created', res.data);
    }
  }).catch(() => changeOkLoading(false));
}
</script>
