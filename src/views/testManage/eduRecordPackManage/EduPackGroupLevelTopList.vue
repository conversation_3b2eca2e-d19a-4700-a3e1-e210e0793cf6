<template>
  <div class="fa-flex-row fa-col-line">
    <div class="fa-flex-column">
      <!-- 项目级 -->
      <div style="width: 250px;" class="fa-col-div fa-full-h">项目级</div>
    </div>

    <EduPackConfigBtn
      :pack-id="pack.id"
      :group-id="group.id"
      :org-id="orgId"
      :list="list"
      @reload="refresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import {organizeTreeApi, testEduRecordPackApi} from "/@/api";
import EduPackConfigBtn from "./cube/EduPackConfigBtn.vue";


const props = defineProps(['pack', 'group']); // 暴露给外部传入的属性
const { pack, group } = props

const list = ref<any[]>([])
const orgId = ref('')

async function refresh() {
  const res = await testEduRecordPackApi.getRecordList(pack.id, group.id)
  list.value = res.data
  let orgRes = await organizeTreeApi.list({"fullName":"项目部"});
  console.log('orgRes',orgRes)

  if (!Array.isArray(orgRes.data)) return;
  orgId.value=orgRes.data[0].id
  console.log('orgId',orgId.value)
}

watch(() => group.id, () => {
  refresh()
}, {deep: true, immediate: true})
</script>

<style scoped>
</style>
