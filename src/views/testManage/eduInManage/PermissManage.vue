<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-left" style="width: 250px;">
      <BasicLeftTree
        title="组织架构"
        ref="leftTreeRef"
        :treeData="treeData"
        :loading="treeLoading"
        :fieldNames="{ key: 'id', title: 'name' }"
        @reload="reloadTree"
        @select="handleTreeSelect"
        showToolbar
        :default-expand-all="true"
      />
    </div>

    <div class="jnpf-content-wrapper-center fa-edu-in-main">
      <div class="jnpf-content-wrapper-content fa-flex-column" style="background-color: white;">
        <div style="height: 40px;width: 100%;">
          <div style="margin-right: 40px;padding-right: 0px;">
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="confirmBindPermiss">确认</a-button>
          </div>
        </div>
        <div style="width:1000px;height: 200px;margin-left: 30px;margin-top: 40px;font-size: 18px;" class="fa-flex-column">
          <div>
            权限配置:
            <span style="font-size: 16px;margin-left: 15px;">/{{organizeName}}</span>
          </div>
          <div style="margin-top: 30px;" class="fa-flex-row">
            权限范围：
            <div style="width:800px;height:35px;font-size: 16px;">
              <a-select v-model:value="orgPermissLevel" placeholder="权限等级" style="width: 400px;height: 20px;">
                <a-select-option :value=0>默认范围(当前部门及以下)</a-select-option>
                <a-select-option :value=1>公司级</a-select-option>
                <a-select-option :value=2>事业部</a-select-option>
                <a-select-option :value=3>项目部</a-select-option>
                <a-select-option :value=4>部门</a-select-option>
                <a-select-option :value=5>班组</a-select-option>
              </a-select>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref, toRefs, unref, watch} from "vue";
import {BasicLeftTree, TreeActionType} from "/@/components/Tree";
import {organizeTreeApi, testEduRecordInOrgPermissApi as api} from '/@/api';
import {useUserStore} from '/@/store/modules/user';
import {useMessage} from "/@/hooks/web/useMessage";


defineOptions({ name: 'extend-tableDemo-treeTable2' });

const userStore = useUserStore();
const userInfo = userStore.getUserInfo;
const organizeId = ref("")
const organizeName = ref("")
const orgPermissLevel = ref(undefined)
const recordId = ref("")


interface State {
  treeData: any[];
  treeLoading: boolean;
  activeKey: '1' | '2' | '3'
}

const state = reactive<State>({
  treeData: [],
  treeLoading: false,
  activeKey: '1',
});
const { treeData, treeLoading, activeKey } = toRefs(state);
const leftTreeRef = ref<Nullable<TreeActionType>>(null);
const {createMessage} = useMessage();

async function init() {
  // setLoading(true);
  await reloadTree();
  organizeId.value = state.treeData[0]?.id;
  organizeName.value = state.treeData[0]?.name;
  const leftTree = unref(leftTreeRef);
  leftTree?.setSelectedKeys([organizeId.value]);
  await handleTreeSelect(organizeId.value)
  organizeId.value && await reload();
}

// ----------------------------------- left tree -----------------------------------
/** 重新加载Tree数据 */
async function reloadTree() {
  state.treeLoading = true;
  // const ret = await organizeTreeApi.allTreeDept();
  const ret = await organizeTreeApi.allFilteredTreeDept();
  state.treeData = ret.data;
  state.treeLoading = false
}

/** Tree节点点击 */
async function handleTreeSelect(id:any) {
  console.log('handleTreeSelect', id)
  console.log('treeData', state.treeData)
  if (!id) return;
  const ret = await organizeTreeApi.getById(id);
  organizeName.value= ret.data.fullName;
  organizeId.value = id
  await getPermissConfig(id)
}


// ----------------------------------- right table -----------------------------------
async function getPermissConfig(orgId) {
  let res = await api.list({
    orgId: orgId
  });

  if (Array.isArray(res.data) && res.data.length > 0) {
    orgPermissLevel.value = res.data[0].permissLevel;
    recordId.value = res.data[0].id;
  } else {
    orgPermissLevel.value = undefined;
    recordId.value = "";
  }
}

async function confirmBindPermiss() {
  let query = {
    id:recordId.value,
    orgId:organizeId.value,
    permissLevel:orgPermissLevel.value,
  };

  const formMethod = recordId.value ? api.update : api.save;
  formMethod(query).then(res => {
    createMessage.success(res.msg);
    setTimeout(() => {
      // emit('reload');
    }, 300);
  }).catch(() => {
  });
}

function reload(){
  console.log('reload')
}


watch(() => orgPermissLevel, () => {
  console.log('watch.orgPermissLevel', orgPermissLevel.value)
  // reload()
}, {deep: true, immediate: false})

onMounted(() => init());
</script>

<style lang="less">
.fa-edu-in-main {
  .jnpf-basic-table-header {
    height: 100px;
  }
}
</style>
