<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-left" style="width: 250px">
      <BasicLeftTree
        title="组织架构"
        ref="leftTreeRef"
        :treeData="treeData"
        :loading="treeLoading"
        :fieldNames="{ key: 'id', title: 'name' }"
        @reload="reloadTree"
        @select="handleTreeSelect"
        showToolbar
        :default-expand-all="true" />
    </div>

    <div class="jnpf-content-wrapper-center fa-edu-in-main">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <div class="fa-flex-column">
              <div class="fa-mb12">
                <a-tabs v-model:activeKey="activeKey" type="card" class="jnpf-content-wrapper-tabs" destroyInactiveTabPane>
                  <a-tab-pane key="1" tab="一级" />
                  <a-tab-pane key="2" tab="二级" />
                  <a-tab-pane key="3" tab="三级" />
                </a-tabs>
              </div>
              <ASpace>
                <div v-if="activeKey === '3'">
                  <a-select v-model:value="searchInfo.posId" style="width: 200px">
                    <a-select-option v-for="item in posOptions" :key="item.id" :value="item.id">{{ item.fullName }}</a-select-option>
                  </a-select>
                </div>
                <a-button v-if="havePermisson" @click="addOrUpdateHandle()" type="primary" preIcon="icon-ym icon-ym-btn-add">新增课件</a-button>
                <a-button v-if="havePermisson" v-auth="'btn_paper_config'" @click="handlePaperConfig()" preIcon="icon-ym icon-ym-system">配置试卷</a-button>
                <a-button v-if="havePermisson && activeKey === '3'" @click="handleEduInStart()" preIcon="ym-custom ym-custom-send">发起培训</a-button>
              </ASpace>
            </div>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <template v-if="record.organizeId === searchInfo.organizeId">
                <TableAction :actions="getTableActions(record)" />
              </template>
              <template v-else>
                <span @click="handleJumpToParent(record)" style="cursor: pointer">继承自 {{ record.organizeName }}</span>
              </template>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="recordUpdate" />
    <EduInStartForm @register="registerEduInStartForm" @reload="reload" />
    <EduTestPaperForm @register="registerQuestionConfigForm" @reload="reload" />
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, toRefs, unref, watch } from 'vue';
  import { BasicLeftTree, TreeActionType } from '/@/components/Tree';
  import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from '/@/components/Table';
  import { organizeTreeApi, positionApi, testEduRecordInConfigApi, testEduRecordInOrgPermissApi } from '/@/api';
  import { useModal } from '/@/components/Modal';
  import Form from './Form.vue';
  import EduInStartForm from './EduInStartForm.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { genDeleteBtn, genEditBtn } from '/@/utils/tableUtils';
  import { ZZ_EDU_RECORD_IN_CONFIG_LEVEL_MAP, ZZ_EDU_RECORD_IN_CONFIG_ORG_RANGE_MAP } from '/@/enums/zzEnums';
  import { useUserStore } from '/@/store/modules/user';
  import { usePopup } from '/@/components/Popup';
  import EduTestPaperForm from '/@/views/testManage/eduRecordManage/EduTestPaperForm.vue';

  defineOptions({ name: 'extend-tableDemo-treeTable2' });

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const organizeId = userInfo.organizeId;
  const isAdministrator = userInfo.isAdministrator;
  const havePermisson = ref(false);

  const [registerQuestionConfigForm, { openPopup: openQuestionConfigFormModal }] = usePopup();

  interface State {
    treeData: any[];
    treeLoading: boolean;
    activeKey: '1' | '2' | '3';
  }

  const state = reactive<State>({
    treeData: [],
    treeLoading: false,
    activeKey: '1',
  });
  const { treeData, treeLoading, activeKey } = toRefs(state);
  const leftTreeRef = ref<Nullable<TreeActionType>>(null);
  const posOptions = ref<any[]>([]);

  async function init() {
    setLoading(true);
    await reloadTree();
    searchInfo.organizeId = state.treeData[0]?.id;
    const leftTree = unref(leftTreeRef);
    leftTree?.setSelectedKeys([searchInfo.organizeId]);
    await checkPermiss();
    await handleTreeSelect(searchInfo.organizeId);
    searchInfo.organizeId && (await reload());
  }

  // ----------------------------------- left tree -----------------------------------
  /** 重新加载Tree数据 */
  async function reloadTree() {
    state.treeLoading = true;
    // const ret = await organizeTreeApi.allTreeDept();
    const ret = await organizeTreeApi.allFilteredTreeDept();
    state.treeData = ret.data;
    state.treeLoading = false;
  }

  /** Tree节点点击 */
  async function handleTreeSelect(id: any) {
    // console.log('handleTreeSelect', id)
    if (!id || searchInfo.organizeId === id) return;
    searchInfo.organizeId = id;
    await checkPermiss();
    await reload();

    // const res = await organizeTreeApi.getById(id);
    // if (res && res.data) {
    //   // 加一些客户的特殊判断
    //   if (res.data.fullName === '项目部') {
    //     state.activeKey = '1'
    //   } else if (res.data.category === 'department') {
    //     state.activeKey = '2'
    //   }
    // }

    await updateQueryPosSelect(id);
    // await getForm().resetFields();
  }

  async function updateQueryPosSelect(organizeId: any) {
    const res = await positionApi.list({ organizeId });
    const options = res.data.map((i: any) => ({ id: i.id, fullName: i.fullName }));
    posOptions.value = options || [];
    if (state.activeKey === '3' && options && options[0]) {
      searchInfo.posId = options[0].id;
    }
    // await getForm().updateSchema({
    //   field: 'posId',
    //   componentProps: { options },
    // })
  }

  // ----------------------------------- right table -----------------------------------
  const { createMessage } = useMessage();
  const [registerForm, { openModal: openFormModal }] = useModal();
  const [registerEduInStartForm, { openModal: openEduInStartFormModal }] = useModal();
  const columns: BasicColumn[] = [
    { title: '级别', dataIndex: 'level', width: 100, customRender: ({ value }) => ZZ_EDU_RECORD_IN_CONFIG_LEVEL_MAP[value] },
    { title: '课件名称', dataIndex: 'eduFileName', minWidth: 200 },
    { title: '学员范围', dataIndex: 'orgRange', width: 130, customRender: opt => ZZ_EDU_RECORD_IN_CONFIG_ORG_RANGE_MAP[opt.value] },
    {
      title: '岗位范围',
      dataIndex: 'posRange',
      width: 200,
      customRender: opt => {
        if (opt.value === '1') return '全部岗位';
        return opt.record.posIdsName;
      },
    },
    // { title: '排序', dataIndex: 'sort', width: 70 },
    { title: '创建部门', dataIndex: 'createDeptName', width: 100 },
    { title: '创建人', dataIndex: 'creatorUserName', width: 100 },
    { title: '创建时间', dataIndex: 'creatorTime', width: 170, format: 'date|YYYY-MM-DD HH:mm:ss' },
  ];
  const searchInfo = reactive({
    organizeId: '', // 右table所属左organizeId
    level: '1', // 层级
    posId: '', // 岗位
    _sorter: 'sort ASC',
  });
  const [registerTable, { reload, setLoading, getForm }] = useTable({
    api: testEduRecordInConfigApi.listConfig,
    columns,
    searchInfo,
    useSearchForm: false,
    immediate: false,
    pagination: false,
    ellipsis: false,
    // formConfig: {
    //   schemas: [
    //     // genQuerySearch(),
    //     genQuerySelect('岗位', 'posId', [])
    //   ],
    // },
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function getTableActions(record: any): ActionItem[] {
    if (!isAdministrator && record.createDeptId !== organizeId) {
      // 限制只可以修改自己本部门创建的课件数据
      return [];
    }
    return [genEditBtn(record, addOrUpdateHandle), genDeleteBtn(record, handleDelete)];
  }

  function addOrUpdateHandle(id = '') {
    openFormModal(true, {
      id,
      organizeId: searchInfo.organizeId,
      level: state.activeKey,
      posId: state.activeKey === '3' ? searchInfo.posId : undefined,
    });
  }

  async function handleDelete(id: any) {
    let res = await testEduRecordInConfigApi.remove(id);
    if (!res) return;

    let getRes = await getEduRecord();
    if (!res.msg) return;
    createMessage.success(getRes.msg);
    reload();
  }

  function handleJumpToParent(record: any) {
    handleTreeSelect(record.organizeId);
    const leftTree = unref(leftTreeRef);
    leftTree?.setSelectedKeys([record.organizeId]);
  }

  /**
   * 试卷配置：
   * 1. 试卷属于一个培训计划
   * 2. 创建一个培训计划：层级+部门+岗位
   *    2.1 一级+技术部
   *    2.2 二级+技术部
   *    2.3 三级+技术部+信息技术岗
   */
  async function handlePaperConfig() {
    let getRes = await getEduRecord();
    if (getRes.data) openQuestionConfigFormModal(true, getRes.data);
  }

  /**
   * 更新关联培训计划（绑定课件）
   */
  async function recordUpdate() {
    console.log(recordUpdate);
    let getRes = await getEduRecord();
    if (getRes) reload;
  }

  async function getEduRecord(): Promise<any> {
    let res = testEduRecordInConfigApi.getEduRecord({
      level: searchInfo.level,
      organizeId: searchInfo.organizeId,
      posId: searchInfo.posId,
    });
    return res;
  }

  /**
   * 发起三级培训
   */
  function handleEduInStart() {
    openEduInStartFormModal(true, { organizeId: searchInfo.organizeId, posId: searchInfo.posId });
  }

  async function checkPermiss() {
    let res = await testEduRecordInOrgPermissApi.checkPermiss({
      userOrgId: organizeId,
      selectOrgId: searchInfo.organizeId,
    });

    if (res) havePermisson.value = res.data;
  }

  watch(
    () => state.activeKey,
    () => {
      // console.log('watch.state.activeKey', state.activeKey)
      if (state.activeKey !== '3') {
        searchInfo.posId = '';
      } else {
        if (posOptions.value && posOptions.value[0]) {
          searchInfo.posId = posOptions.value[0].id;
        }
      }
      if (state.activeKey !== searchInfo.level) {
        searchInfo.level = state.activeKey;
        reload();
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => searchInfo.posId,
    () => {
      // console.log('watch.searchInfo.posId', searchInfo.posId)
      reload();
    },
    { deep: true, immediate: false },
  );

  onMounted(() => init());
</script>

<style lang="less">
  .fa-edu-in-main {
    .jnpf-basic-table-header {
      height: 100px;
    }
  }
</style>
