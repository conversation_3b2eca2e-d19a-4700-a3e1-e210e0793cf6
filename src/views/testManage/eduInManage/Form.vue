<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit" :destroyOnClose="true">
    <BasicForm @register="registerForm" @field-value-change="handleValueChange">
      <template #eduFileId="{  }">
        <a-input v-model:value="eduFileName" placeholder="请选择课件" disabled>
          <template #addonAfter>
            <span class="cursor-pointer" @click="handleChange">选择课件</span>
          </template>
        </a-input>
      </template>
    </BasicForm>

    <EduFileSelectModal @register="registerFileModal" @select-edu-file="selectEduFileHandle"/>
  </BasicModal>
</template>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { organizeTreeApi, positionApi, testEduFileApi, testEduRecordInConfigApi } from '/@/api';
import { genCommon, genInputNumber, genSelect } from "/@/utils/formUtils";
import { ZZ_EDU_RECORD_IN_CONFIG_LEVEL, ZZ_EDU_RECORD_IN_CONFIG_ORG_RANGE, ZZ_EDU_RECORD_IN_CONFIG_POS_RANGE } from "/@/enums/zzEnums";
import EduFileSelectModal from "/@/views/testManage/eduFileManage/EduFileSelectModal.vue";


const [registerFileModal, {openModal:openFileModal}]  = useModal();

const eduFileName = ref('')
const emit = defineEmits(['register', 'reload']);
const [registerForm, { setFieldsValue, getFieldsValue, resetFields, updateSchema, validate }] = useForm({
  schemas: [
    {
      ...genCommon('所属组织', 'organizeId', 'FaCascader'),
      componentProps: {
        api: {
          ...organizeTreeApi,
          allTree: organizeTreeApi.allFilteredTreeDept
        },
        showRoot: false
      },
      // dynamicDisabled: ({ values }) => values.id,
    },
    {
      ...genCommon('课件', 'eduFileId', 'Input',false),
      slot: 'eduFileId',
    },
    genSelect('级别', 'level', true, 'number', ZZ_EDU_RECORD_IN_CONFIG_LEVEL),
    genSelect('学员范围', 'orgRange', true, 'string', ZZ_EDU_RECORD_IN_CONFIG_ORG_RANGE),
    {
      ...genSelect('岗位范围', 'posRange', true, 'string', ZZ_EDU_RECORD_IN_CONFIG_POS_RANGE),
      show: ({ values }) => values.level === 3,
    },
    {
      ...genSelect('指定岗位', 'posId', true, 'string', []),
      show: ({ values }) => values.level === 3 && values.posRange === '2',
    },
    // genInputNumber('排序', 'sort'),
  ],
});
const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);
const id = ref('');
const inited = ref(false);
const { createMessage } = useMessage();

const getTitle = computed(() => (!unref(id) ? '新建数据' : '编辑数据'));
const fileList = ref<string[]>()

async function init(data:any) {
  console.log('data', data)
  await resetFields();
  id.value = data.id;
  inited.value = false
  if (id.value) {
    changeLoading(true);
    testEduRecordInConfigApi.getById(id.value).then(res => {
      setFieldsValue({
        ...res.data,
      });

      testEduFileApi.getById(res.data.eduFileId).then(res2 => {
        if (res2.data) {
          eduFileName.value = res2.data.fileName
        }
      })

      changeLoading(false);
    });
  } else {
    eduFileName.value = ''
    await setFieldsValue({ organizeId: data.organizeId, level: Number(data.level), posId: data.posId })
    if (data.level === '3') {
      await setFieldsValue({
        orgRange: '1',
        posRange: '2',
        posId: data.posId,
      })
    }
  }
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);

  if (!Array.isArray(fileList.value)) return;
  const queryList =  fileList.value.map(i=>{
    let query = {
      ...values,
      id: id.value,
      posRange: values.posRange || '1',
    };
    query.eduFileId = i.id
    console.log('query',query)
    return query
  })

  const formMethod = id.value ? testEduRecordInConfigApi.update : testEduRecordInConfigApi.saveBatch;
  formMethod(queryList).then(res => {
    createMessage.success(res.msg);
    changeOkLoading(false);
    closeModal();
    setTimeout(() => {
      emit('reload');
    }, 300);
  }).catch(() => changeOkLoading(false));
}

function handleChange() {
  openFileModal(true,getFieldsValue());
}

function selectEduFileHandle(list:any[]) {
  console.log('选择课件', list)
  if (list) {
    fileList.value = list
    if (list.length > 1) {
      eduFileName.value = list[0].fileName + "..."
    }else {
      eduFileName.value = list[0].fileName
    }
    // setFieldsValue({ eduFileId: list[0].id })
  }
}

function handleValueChange(key:any, value:any) {
  console.log('handleValueChange', key, value)
  // 部门变动，查询岗位
  if (key === 'organizeId') {
    if (inited.value) {
      setFieldsValue({ posId: undefined })
    }
    inited.value = true;
    if (value) {
      positionApi.list({ organizeId: value }).then(res => {
        const options = res.data.map((i:any) => ({ id: i.id, fullName: i.fullName }));
        updateSchema({
          field: 'posId',
          componentProps: { options },
        })
      })
    } else {
      setFieldsValue({ posId: undefined })
      updateSchema({
        field: 'posId',
        componentProps: { options: [] },
      })
    }
  }
}
</script>
