<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="发起培训" @ok="handleSubmit" :destroyOnClose="true">
    <BasicForm @register="registerForm">
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { positionApi, testEduRecordInConfigApi } from '/@/api';
import { genDeptCascade, genSelect, genUser } from "/@/utils/formUtils";


const emit = defineEmits(['register', 'reload']);
const [registerForm, {setFieldsValue, resetFields, updateSchema, validate}] = useForm({
  schemas: [
    genDeptCascade('部门', 'organizeId', true, 'string', {disabled: true}),
    genSelect('指定岗位', 'posId', true, 'string', []),
    genUser('员工', 'userIds', true, 'array', { multiple: true }),
  ],
});
const [registerModal, {closeModal, changeLoading, changeOkLoading}] = useModalInner(init);
const organizeId = ref('');
const posId = ref('');
const {createMessage} = useMessage();

async function init(data: any) {
  console.log('data', data)
  await resetFields();
  organizeId.value = data.organizeId;
  posId.value = data.posId;

  await setFieldsValue({organizeId: data.organizeId, posId: data.posId, userIds: []})

  positionApi.list({organizeId: data.organizeId}).then(res => {
    const options = res.data.map((i: any) => ({id: i.id, fullName: i.fullName}));
    updateSchema({
      field: 'posId',
      componentProps: {options, disabled: true},
    })
  })
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const query = {
    ...values,
  };
  testEduRecordInConfigApi.start(query).then(res => {
    createMessage.success(res.msg);
    changeOkLoading(false);
    closeModal();
    setTimeout(() => {
      emit('reload');
    }, 300);
  }).catch(() => changeOkLoading(false));
}
</script>
