<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="" showOkBtn @ok="handleSubmit" @close="handleClose">

    <div v-for="item in videoList" :key="item.id" class="fa-flex-column">
      <div class="fa-flex-row fa-col-line fa-border-dark-b">
        <div class="fa-col-div fa-flex-center" style="flex: 1;">{{ item.name ? item.name : '/' }}</div>
        <div class="fa-col-div fa-flex-center" style="flex: 1">进度: {{formatPercentage(item.watchedTime,item.duration)}}</div>

        <div class="fa-col-div fa-flex-center" style="width: 100px;">
          <a-button type="link" size="large" @click="studyHandle(item)">学习</a-button>
        </div>
      </div>
    </div>

    <eduVideoModal @register="regisEduVideoModal"/>
  </BasicPopup>
</template>
<script lang="ts" setup>
import {BasicPopup, usePopupInner} from '/@/components/Popup';
import {ref} from "vue";
import {testEduFileApi, testUserEduRecordVideoApi as userVideoApi} from "/@/api";
import eduVideoModal from '/src/views/testManage/eduFileManage/fileModal/eduVideoModal.vue'
import {useModal} from "/@/components/Modal";
import {formatPercentage} from "/@/utils/formUtils";

defineOptions({name: 'eduVideoStudy'});

const emit = defineEmits(['register', 'reload']);
const [registerPopup, {closePopup}] = usePopupInner(init);
const [regisEduVideoModal, {openModal: openVideoModal}] = useModal();

const userId = ref('')
const eduId = ref('')
const userEduRecordId = ref('')
const eduNum = ref('')
const videoList = ref<any[]>([])

async function init(data: any) {
  console.log('data', data)
  userId.value = data.userId
  eduId.value = data.eduId
  userEduRecordId.value = data.id
  getData();
}

async function getData() {
  // console.log('getData')
  const res = await testEduFileApi.getFileDataByEduId(eduId.value);

  if (res && res.data && Array.isArray(res.data)) {
    videoList.value = []
    // totalWatchTime = 0;
    res.data.forEach(item => {
      let videoArr = JSON.parse(item.fileScreenshotLink).map((v) => {
        // 从服务器获取每个视频的观看历史位置状态
        const duration = v.duration || 0
        return {
          ...v,
          eduId: eduId.value,
          eduNum: eduNum.value,
          eduRecordId: userEduRecordId.value,
          userId: userId.value,
          videoFileId: v.fileId,
          videoName: v.name,
          duration, // 视频总时长
          // watchStatus, // 0-未开始/1-观看中/2-已看完
          // initialTime, // 指定视频初始播放位置，单位为秒（s）。
          watchedTime: 0, // 记录已经观看完成的视频时长
          // watchedPer, // 观看进度百分比
          // duration: 0, // 视频总时长（FIXME: 这个要后台每次上传视频的时候解析获取视频的时长，单位为秒（s））
        }
      })
      videoList.value.push(...videoArr);
    })
  }

  const userVideoRes = await userVideoApi.page({
    userId:userId.value,
    eduRecordId: userEduRecordId.value
  })

  videoList.value.forEach(v => {
    const userVideo = userVideoRes.data.list.find(i => i.videoFileId === v.videoFileId)
    if (!userVideo) return
    v.watchedTime = userVideo.watchedTime
  })

  console.log('videoList', videoList)
}


function studyHandle(record) {
  openVideoModal(true, record);
}

async function handleClose() {
  closePopup();
}

async function handleSubmit() {
  closePopup();
  emit('reload');
}
</script>

