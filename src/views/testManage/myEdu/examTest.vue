<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :footer="null" @cancel="handleClose"
              defaultFullscreen class="jnpf-full-modal full-modal">

    <div class="jnpf-content-wrapper">
      <div class="jnpf-content-wrapper-left fa-flex-column-center fa-scroll-auto-y">
        <div v-if="viewStage === 1" class="fa-p12">
          <div class="fa-flex-row fa-flex-wrap">
            <div v-for="(item,index) in questions" :key="item.id" @click="handleJumpItem(index)" class="fa-question-cube"
                 :class="{'fa-question-cube-answered': item.myAnswer !== '', 'fa-question-cube-cur': currentQuestionIndex === index}">
              {{ index + 1 }}
            </div>
          </div>
          <a-button type="primary" @click="submitAnswers()">提交试卷</a-button>
        </div>

        <div v-if="viewStage === 2">
          <a-button style="margin:5px; width:200px; height:30px; color: dodgerblue;" @click="handleStartNewExam()">重新考试</a-button>
        </div>

      </div>

      <div class="jnpf-content-wrapper-center">
        <div class="jnpf-content-wrapper-content">

          <div v-if="viewStage === 1">
            <div class="fa-file-doc-countdown fa-mb-12">
              考试剩余时间：{{ Math.floor(remainingTime / 60 / 60) }} 小时 {{ Math.floor(remainingTime / 60) % 60 }} 分钟 {{ remainingTime % 60 }} 秒
            </div>

            <div class="fa-full fa-flex-column">
              <div class="fa-flex-column fa-mb-12">
                <div class="fa-flex-row fa-mb-12">
                  <div class="fa-exam-question-type" style="{background-color: questionTypeColor}">
                    <text>{{ questionTypeTitle }}</text>
                  </div>
                </div>
                <div class="fa-exam-question-title" v-if="currentQuestion">{{ currentQuestionIndex + 1 }}: {{ currentQuestion.questionContent }}</div>
              </div>

              <!-- 判断题 -->
              <div v-if="currentQuestion && currentQuestion.questionType === 0">
                <div class="fa-exam-question-option" @click="answer(true)">
                  <div class="fa-exam-question-radio" :class="{ 'fa-radio-selected': currentQuestion.userAnswer === true }">
                    <text class="u-font-30 item-icon icon-ym icon-ym ym-custom ym-custom-check"/>
                  </div>
                  <text style="font-size: 16px;">正确</text>
                </div>

                <div class="fa-exam-question-option" @click="answer(false)">
                  <div class="fa-exam-question-radio" :class="{ 'fa-radio-selected': currentQuestion.userAnswer === false }">
                    <text class="u-font-30 item-icon icon-ym icon-ym ym-custom ym-custom ym-custom-close"/>
                  </div>
                  <text style="font-size: 16px;">错误</text>
                </div>
              </div>

              <!-- 单选题 -->
              <div v-if="currentQuestion && currentQuestion.questionType === 1">
                <div class="fa-exam-question-option" v-for="(item, index) in currentQuestion.options" :key="index" @click="answer(item.value)">
                  <div class="fa-exam-question-radio" :class="{ 'fa-radio-selected': currentQuestion.userAnswer === item.value }">
                    <text v-if="currentQuestion.userAnswer === item.value" class="u-font-30 item-icon icon-ym icon-ym ym-custom ym-custom-check"/>
                  </div>
                  <text>{{ item.value }} {{ item.option }}</text>
                </div>
              </div>

              <!-- 多选题 -->
              <div v-if="currentQuestion && currentQuestion.questionType === 2">
                <div class="fa-exam-question-option" v-for="(item, index) in currentQuestion.options" :key="index" @click="answer(item.value)">
                  <div class="fa-exam-question-radio" :class="{ 'fa-radio-selected': (currentQuestion.userAnswer||[]).includes(item.value) }">
                    <text v-if="(currentQuestion.userAnswer||[]).includes(item.value)" class="u-font-30 item-icon icon-ym icon-ym ym-custom ym-custom-check"/>
                  </div>
                  <text>{{ item.value }} {{ item.option }}</text>
                </div>
              </div>

            </div>
          </div>

          <div v-if="viewStage === 2">
            剩余考试次数：{{ leftTimes }}
          </div>

        </div>
      </div>
    </div>

  </BasicModal>
</template>
<script lang="ts" setup>
import {ref, watch} from "vue";
import {testGradeApi, testPaperApi, testUserEduRecordApi, testExamRecordApi} from "/@/api";
import {BasicModal, useModalInner} from "/@/components/Modal";
import dayjs from "dayjs";
import {useMessage} from "/@/hooks/web/useMessage";
import {isNil} from "lodash-es";

defineOptions({name: 'examTest'});

const emit = defineEmits(['register', 'reload']);
const [registerModal, {closeModal}] = useModalInner(init);

const {createMessage, createConfirm} = useMessage();

const questions = ref<any[]>([])
const userId = ref('')
const eduId = ref('')
const userEduRecordId = ref('')
const currentQuestionIndex = ref(0)
const currentQuestion = ref()
const viewStage = ref(1) // 当前视图的状态：0-确认封面页/1-考试进行中/10-考试提交确认/2-考试结果展示
const testEduRecord = ref({}) // 本次培训信息
const testGrade = ref({}) // 本次考试记录
const testPaper = ref({}) // 本次考试试卷信息
const remainingTime = ref(60)
const leftTimes = ref(0) // 剩余考试次数
const isEnterEdu = ref(false)
const countTimer = ref()
const questionTypeTitle = ref('判断题')
const questionTypeColor = ref('')

// interface State {
//   userId:string,
//   eduId :string,
//   userEduRecordId :string,
//   currentQuestionIndex :number,
//    currentQuestion :object,
//   viewStage :number, // 当前视图的状态：0-确认封面页/1-考试进行中/10-考试提交确认/2-考试结果展示
//   testEduRecord :object,// 本次培训信息
//   testGrade :object, // 本次考试记录
//   testPaper :object, // 本次考试试卷信息
// // questionTypeTitle :undefined,
//   remainingTime :number,
//   leftTimes :number, // 剩余考试次数
//   isEnterEdu :boolean,
//   countTimer: number
// }

async function init(data: any) {
  console.log('init', init)
  userId.value = data.userId
  eduId.value = data.eduId
  userEduRecordId.value = data.id

  let ret = await testUserEduRecordApi.getById(userEduRecordId.value)
  if (ret.data.eduPackId) {
    isEnterEdu.value = true
  }
  await refreshAll()
  if (viewStage.value === 2) return;
  await handleStartExam()
}

async function refreshAll() {
// 获取考试记录
  await refreshTestGrade()

  // 获取试卷配置
  const ret1 = await testPaperApi.getDetailById(testGrade.value.paperId)
  testPaper.value = ret1.data

  //console.log('testGrade', testGrade)
  leftTimes.value = testPaper.value.testLimitCount - testGrade.value.testTimes
  //console.log('leftTimes', leftTimes)

  // 判断考试时间
  if (testGrade.value.status === 0) { // 考试尚未开始，考试剩余时间为最大时间
    remainingTime.value = testPaper.value.limitTimeSeconds
  } else { // 考试已经开始，计算剩余时间
    const testStartTime = testGrade.value.testStartTime / 1000
    const now = dayjs().unix()
    // used seconds
    const seconds = now - testStartTime

    remainingTime.value = testPaper.value.limitTimeSeconds - seconds
    remainingTime.value = remainingTime.value > 0 ? remainingTime.value : 0
  }

  getPaperQuestions();
}

async function getPaperQuestions() {
  const ret = await testGradeApi.getQuestionByUser({
    eduId:eduId.value,
    eduRecordId:userEduRecordId.value,
    gradeId:"",
  })
  questions.value = ret.data.map(i => ({
    ...i,
    options: parseOptions(i.questionChoices),
    userAnswer: parseUserAnswer(i),
  }))
  //console.log(questions.value)
  syncQuestion(0)
}

async function refreshTestGrade() {
  const ret = await testGradeApi.getCurTestByUser(userEduRecordId.value)
  testGrade.value = ret.data
  if (testGrade.value.status === 2) {
    viewStage.value = 2
  }
}

async function handleStartExam() {
  console.log('handleStartExam')
  // no time left, end exam
  if (remainingTime.value && remainingTime.value <= 0) {
    createConfirm({
      iconType: 'info',
      title: '结束考试',
      content: '考试时间已用完，确认提交？',
      onOk: () => {
        testGradeApi.endExam(testGrade.value.id)
        refreshTestGrade()
        handleClose()
      },
    });
  } else {
    if (leftTimes.value < 0) {
      alert('已达考试次数上限')
      handleClose()
    }
    console.log('开始考试确认')
    createConfirm({
      iconType: 'info',
      title: '开始考试',
      content: '确认开始考试？剩余考试次数: ' + leftTimes.value,
      onOk: () => {
        testGradeApi.startExam(testGrade.value.id)
        refreshTestGrade()
        viewStage.value = 1
        startCountdown();
      },
    });
  }

}

function parseOptions(value) {
  try {
    return JSON.parse(value || '[]')
  } catch (e) {
  }
  return []
}

function parseUserAnswer(question) {
  switch (question.questionType) {
    case 0: { // 判断题
      if (question.myAnswer === undefined || question.myAnswer === '') {
        return "";
      }
      return question.myAnswer === '√';
    }
      break;
    case 1: { // 单选题
      return question.myAnswer
    }
      break;
    case 2: { // 多选题
      return (question.myAnswer || "").split('/')
    }
      break;
  }
  return "";
}

/** 回答问题，保存回答结果(暂存到本地) */
function answer(result) {
  console.log('answer', result, currentQuestion.value)
  //题目类型(0:判断题 1:单选题 2:多选题 3:填空题 4:简答题 5:材料题)
  switch (currentQuestion.value.questionType) {
    case 0:
    case 1: {
      // 0: result: true/false
      // 1: result: index
      updateAnswer(result)
    }
      break;
    case 2: {
      //console.log('2', result)
      // 2: result: index array
      const preUserAnswer = currentQuestion.value.userAnswer || []
      if (preUserAnswer.indexOf(result) === -1) {
        updateAnswer([...preUserAnswer, result])
      } else {
        updateAnswer(preUserAnswer.filter(i => i !== result))
      }
    }
      break;
  }
}

async function updateAnswer(value) {
  console.log('updateAnswer', value, currentQuestion.value)

  currentQuestion.value.userAnswer = value
  const myAnswer = parseAnswer()
  currentQuestion.value.myAnswer = myAnswer
  questions.value[currentQuestionIndex.value].userAnswer = value
  questions.value[currentQuestionIndex.value].myAnswer = myAnswer

  const question = currentQuestion.value;
  await testExamRecordApi.answer(question.id, question.myAnswer)
}

// 解析答案
function parseAnswer() {
  const charArr = ["A", "B", "C", "D", "E", "F"]
  switch (currentQuestion.value.questionType) {
    case 0: { // 判断题
      return currentQuestion.value.userAnswer ? '√' : '×'
    }
      break;
      break;
    case 1: { // 单选题
      return currentQuestion.value.userAnswer
    }
      break;
      break;
    case 2: { // 多选题
      return charArr.filter((item, index) => currentQuestion.value.userAnswer.indexOf(item) > -1).join('/')
    }
      break;
  }
  return "";
}

function startCountdown() {
  if (viewStage.value === 1) {
    if (countTimer.value !== null) {
      clearInterval(countTimer.value)
    }
    countTimer.value = setInterval(() => {
      remainingTime.value--;
      if (remainingTime.value && remainingTime.value <= 0) {
        submitAnswers();
        stopCountdown();
      }
    }, 1000);
  }
}

function stopCountdown() {
  if (countTimer.value !== null) {
    clearInterval(countTimer.value)
  }
}

function handleJumpItem(index) {
  //console.log('handleJumpItem')
  syncQuestion(index)
}

function syncQuestion(index) {
  currentQuestionIndex.value = index;
  if (questions.value.length > 0) {
    currentQuestion.value = questions.value[index];
  } else {
    currentQuestion.value = undefined;
  }
  //console.log('currentQuestion', currentQuestion.value)
}

async function submitAnswers() {
  try {
    // await jnpf.confirm('提交', '确定提交当前试卷吗？', showCancel)
    createConfirm({
      iconType: 'info',
      title: '提交',
      content: '确定提交当前试卷吗？',
      onOk: () => {
        testGradeApi.endExam(testGrade.value.id)
        refreshTestGrade()
        leftTimes.value = testPaper.value.testLimitCount - testGrade.value.testTimes
        // viewStage.value = 2
        handleClose()
      },
    });
  } catch (e) {
    // uni.navigateBack()
  }
}

// 本次不及格，开始下一次新的考试
async function handleStartNewExam() {
  await createConfirm({
    iconType: 'info',
    title: '提交',
    content: '确定开始下一次新的考试吗？',
    onOk: () => {
      testGradeApi.startNewExam(userEduRecordId.value)
      refreshAll()
      handleStartExam()
    },
  });
}

async function handleClose() {
  if (countTimer.value !== null) {
    clearInterval(countTimer.value)
  }
  closeModal();
}

async function handleSubmit() {
  if (countTimer.value !== null) {
    clearInterval(countTimer.value)
  }
  closeModal();
  emit('reload');
}

watch(() => currentQuestion,
  async (val) => {
    //console.log('currentQuestion', val)
    if (!isNil(val)) {
      const types = {
        '0': '判断题',
        '1': '单选题',
        '2': '多选题',
      }
      const questionType = currentQuestion.value ? currentQuestion.value.questionType : 0
      questionTypeTitle.value = types[questionType];

      const colors = {
        '0': '#007AFF',
        '1': '#fd9220',
        '2': '#fc001d',
      }
      questionTypeColor.value = colors[questionType]
      //console.log('questionTypeColor', questionTypeColor.value)
    }
  },
  {immediate: true, deep: true},
)

</script>

<style>

.fa-exam-test-title {
  padding: 24px;
  font-size: 600px;
  background-color: #FFCA28;
}

.fa-exam-question-type {
  border-radius: 2px;
  padding: 12px;
  background-color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFF;
  font-size: 14px;
  font-weight: 600;
  width: 150px;
}

.fa-exam-question-title {
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 2px;
  line-height: 30px;
  /* text-indent: 110px; */
}

.fa-exam-question-option {
  margin: 8px;
  padding: 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
}

.fa-exam-question-option:hover {
  background-color: #ccc;
}

.fa-exam-question-radio {
  width: 26px;
  height: 26px;
  border-radius: 4px;
  border: 1px solid #333;
  margin-right: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fa-radio-selected {
  background-color: #59A9FF;
  border: 1px solid #59A9FF;
  color: #FFF;
}

.fa-radio-selected-red {
  background-color: #FC6D6D;
  border: 1px solid #FC6D6D;
}

.fa-exam-btns {
  display: flex;
  flex-direction: row;
  margin-top: 24px;
  justify-content: center;
}

.fa-exam-bottom {
  padding: 24px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.black-font {
  font-family: '黑体', 'Hei', sans-serif;
  /* 黑体是中文字体，Hei是一些系统中黑体的英文名称 */
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.question-text {
  margin-bottom: 20px;
}

.option {
  margin-right: 10px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  margin-bottom: 50px;
  gap: 50px;
}

.selected {
  background-color: blue;
  color: white;
}

.navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.fa-question-cube-main {
  padding-left: 15px;
  /* justify-content: space-between; */
  margin-bottom: 12px;
}

.fa-question-cube {
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #333;
  margin: 0 9px 9px 0;
  cursor: pointer;
}

.fa-question-cube-cur {
  border: 1px solid #fd891b;
  outline: 4px solid #fd891b;
}

.fa-question-cube-answered {
  background-color: #18b43c;
  border: 1px solid #18b43c;
  color: #FFF;
}

.fa-relative {
  position: relative;
}

.fa-mb-24 {
  margin-bottom: 24px;
}

.fa-mb-12 {
  margin-bottom: 12px;
}
</style>
