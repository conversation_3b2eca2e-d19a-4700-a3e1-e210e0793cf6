<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="人脸识别" showOkBtn @ok="handleSubmit"
              defaultFullscreen class="jnpf-full-modal full-modal"
  >
    <div style="height: 40px;width: 100%;padding-right: 10px;">
      <div class="fa-flex-row" style="width: 400px;margin-left: 20px;">
        <a-tooltip title="点击开启摄像头后,进行人脸验证" placement="topLeft">
          <input type="button" title="HTML5摄像头" value="开启摄像头" @click="getMedia()" style="margin-right: 20px;"/>
        </a-tooltip>
        <button id="snap" @click="takePhoto()" style="margin-right: 20px;">人脸验证</button>
        <input type="button" title="关闭摄像头" value="关闭摄像头" @click="stopMedia()" style="margin-right: 20px;"/>
      </div>
      <br>
      <video id="video" width="500px" height="500px" :autoplay=true></video>
      <canvas id="canvas" width="500px" height="500px"></canvas>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import {onMounted, ref} from "vue";
import {BasicModal, useModalInner} from "/@/components/Modal";
import {fileSaveApi, testGradeApi, testUserExamFaceCheckApi} from "/@/api";
import {useMessage} from "/@/hooks/web/useMessage";

defineOptions({name: 'faceCheck'});
const [registerModal, {closeModal, changeLoading, changeOkLoading}] = useModalInner(init);
const {createMessage} = useMessage();
const emit = defineEmits(['faceCheckStatus']);

const id = ref('');
const video = ref()
const canvas = ref()
const initData = ref()

async function init(data) {
  console.log('window.location',window.location)
  if(window.location.protocol === 'http:') {
    createMessage.error('请点击跳转：https://zz.szh.dward.cn/')
    return
  }

  if (data) initData.value = data
  console.log('init data',data)
}

function getMedia() {
  try {
    let constraints = {
      //参数
      video: {width: 500, height: 500},
    };
    //获得video摄像头区域
    video.value = document.getElementById("video");
    //返回的Promise对象
    let promise = navigator.mediaDevices.getUserMedia({video: true});
    //then()异步，调用MediaStream对象作为参数
    promise.then(function (MediaStream) {
      video.value.srcObject = MediaStream;
      video.value.play();
    });
  } catch (error) {
    console.error("无法访问摄像头:", error)
    alert("无法访问摄像头，请确保已授予权限。");
  }
}


async function takePhoto() {
  //获得Canvas对象
  canvas.value = document.getElementById("canvas");
  let ctx = canvas.value.getContext('2d');
  //绘图
  ctx.drawImage(video.value, 0, 0, 300, 300);

  let file = getImageFile()
  console.log('file',file)

  let res = await fileSaveApi.uploadFile(file, () => {
    // console.log('callback')
  })
  let ret = await testGradeApi.getCurTestByUser(initData.value.id)

  if(res && res.data && ret && ret.data)
    testUserExamFaceCheckApi.saveAndDetect({
      eduId: initData.value.eduId,
      userEduRecordId: initData.value.id,
      userId: initData.value.userId,
      gradeId: ret.data.id,
      paperId: ret.data.paperId,
      facePhotoFileId:res.data.id
    })
      .then(res=>{
        if (res.data){
          stopMedia()
          createMessage.success('人脸验证成功');
          emit('faceCheckStatus', res.data)
          closeModal();
        }
      })
}

function getImageFile(){
  var dataurl = canvas.value.toDataURL('image/png')
  var arr = dataurl.split(',')
  var mime = arr[0].match(/:(.*?);/)[1]
  var bstr = atob(arr[1])
  var n = bstr.length
  var u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], 'test.png', { type: mime })

}

function stopMedia() {
  const stream = video.value.srcObject;
  stream.getTracks().forEach(function(track) {
    track.stop();  //停止视频流
  });

  video.value.srcObject = null;
}


async function reload() {
  console.log('reload')
}
async function handleSubmit() {
  changeOkLoading(false);
  closeModal();
}

onMounted(() => {
  init();
});
</script>
