<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center" ref="table">
      <div class="jnpf-content-wrapper-content">
        <div style="height: 40px;width: 100%;padding-right: 10px;">
          <a-button class="ant-btn ant-btn-primary mr-2" @click="reload">重置</a-button>
<!--          <a-button class="ant-btn ant-btn-primary mr-2" @click="faceCheckTest">人脸测试</a-button>-->
        </div>
        <div style="background-color: #f0f0f0;">
          <a-table :columns="columns" :data-source="userProcessList" :pagination="false" size="small" :scroll="{y: scrollY}">
            <template #bodyCell="{ column, record }">

              <template v-if="column.key === 'isFinish'">
                <a-tag v-if="record.isFinish" color="green">完成</a-tag>
                <a-tag v-else color="red">未完成</a-tag>
              </template>

              <template v-if="column.key === 'isPass'">
                <a-tag v-if="record.isPass === 1" color="green">及格</a-tag>
                <a-tag v-if="record.isPass === 0" color="red">不及格</a-tag>
              </template>

              <template v-if="column.key === 'action'">
                <span>
                  <a @click="videoStudy(record)">视频学习</a>
                  <a-divider type="vertical"/>
                  <a @click="docStudy(record)">文档学习</a>
                  <a-divider type="vertical"/>
                  <a @click="startTest(record)">开始考试</a>
                </span>
              </template>
            </template>
          </a-table>
        </div>
        <faceCheck @register="registerFaceCheck" @face-check-status="handleFaceCheck"/>
        <eduVideoStudy @register="registerVideoStudy"/>
        <eduDocStudy @register="registerDocStudy"/>
        <examTest @register="registerExamTest"/>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {usePopup} from '/@/components/Popup';
import {formatToDate} from "/@/utils/dateUtil";
import {onMounted, ref} from "vue";
import {eduProcessApi, testEduRecordApi} from "/@/api";
import eduVideoStudy from './eduVideoStudy.vue';
import eduDocStudy from './eduDocStudy.vue';
import examTest from './examTest.vue';
import faceCheck from './FaceCheck.vue';
import {useModal} from "/@/components/Modal";
import {onlineUtils} from "/@/utils/jnpf";
import {useMessage} from "/@/hooks/web/useMessage";

defineOptions({name: 'testManage-examRecordManage'});

const [registerVideoStudy, {openPopup: openEduVideoStudy}] = usePopup();
const [registerDocStudy, {openPopup: openDocStudy}] = usePopup();
const [registerExamTest, {openModal: openExamTest}] = useModal();
const [registerFaceCheck, {openModal: openFaceCheck}] = useModal();

const scrollY = document.body.clientHeight - 180
const userProcessList = ref<any[]>([])
const userInfo = ref<any>({})
const {createMessage} = useMessage();

const columns = [
  {title: '培训分类', dataIndex: 'eduClassName', key: 'eduClassName'},
  {title: '培训编号', dataIndex: 'eduNum', key: 'eduNum', width: 160},
  {title: '培训名称', dataIndex: 'eduName', key: 'eduName'},
  {
    title: '参与培训时间', dataIndex: 'creatorTime', key: 'creatorTime',
    customRender: (record) => formatToDate(record.creatorTime)
  },
  {title: '视频进度', dataIndex: 'eduProcess', key: 'eduProcess'},
  {
    title: '培训状态', dataIndex: 'isFinish', key: 'isFinish',
    // customRender: ({text}) => text ? '完成✅' : '未完成❌'
  },
  { title: '是否需要考试', dataIndex: 'isNeedTest', key: 'isNeedTest', customRender: ({text}) => text ? '是' : '/' },
  { title: '考试时间', dataIndex: 'testTime', key: 'testTime', customRender: ({text}) => text ? formatToDate(text) : '' },
  { title: '考试成绩', dataIndex: 'score', key: 'score' },
  { title: '合格分数', dataIndex: 'passScore', key: 'passScore' },
  { title: '是否合格', dataIndex: 'isPass', key: 'isPass',
    // customRender: ({text}) => text === 1 ? '及格✅' : text === 0 ? '不及格❌' : ''
  },
  { title: '操作', dataIndex: 'action', key: 'action', fixed: 'right', width: 220 },
]
const testRecord = ref()
let openTest = ref<boolean>()

async function init() {
  userInfo.value = onlineUtils.getUserInfo();
  await getData()
}

async function reload() {
  await getData()
}

async function getData() {
  let _res = await eduProcessApi.getUserStatusList({
    userId: userInfo.value.userId,
    type: "",
  });
  userProcessList.value = _res.data
}


// 视频学习弹窗
function videoStudy(record:any) {
  testRecord.value = record
  openTest.value = false
  openFaceCheck(true, record)
}

// 文档学习弹窗
function docStudy(record:any) {
  openDocStudy(true, record);
}

// 考试学习界面
async function startTest(record:any) {
  testRecord.value = record
  openTest.value = true

  let eduRecord =await testEduRecordApi.getById(record.eduId);
  if (!(eduRecord && eduRecord.data)) {
    createMessage.error('无法获得培训计划详情');
    return
  }

  if (eduRecord.data.isNeedFaceCheck === 1){
    openFaceCheck(true, record)
  }  else {
    openExamTest(true, record);
  }

}

function handleFaceCheck(e) {
  if(!e) return
  if (openTest.value) {
    openExamTest(true, testRecord.value)
  } else {
    openEduVideoStudy(true, testRecord.value);
  }
}



onMounted(() => {
  init();
});
</script>
