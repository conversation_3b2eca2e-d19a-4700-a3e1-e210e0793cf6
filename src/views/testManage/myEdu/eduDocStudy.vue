<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="" showOkBtn @ok="handleSubmit" @close="handleClose">

    <div v-for="item in fileList" :key="item.id" class="fa-flex-column">
      <div class="fa-flex-row fa-col-line fa-border-dark-b">
        <div class="fa-col-div fa-flex-center" style="flex: 1;">{{ item.docName ? item.docName : '/' }}</div>

        <div class="fa-col-div fa-flex-center" style="width: 100px;">
          <a-button type="link" size="large" @click="studyHandle(item)">学习</a-button>
        </div>
      </div>
    </div>

    <eduDocModal @register="regisEduDocModal"/>
  </BasicPopup>
</template>
<script lang="ts" setup>
import {BasicPopup, usePopupInner} from '/@/components/Popup';
import {ref} from "vue";
import {eduProcessApi, testEduFileApi, testUserEduRecordApi, testUserEduRecordDocApi} from "/@/api";
import eduDocModal from '/src/views/testManage/eduFileManage/fileModal/eduDocModal.vue'
import {useModal, useModalInner} from "/@/components/Modal";

defineOptions({name: 'eduDocStudy'});

const emit = defineEmits(['register', 'reload']);
const [registerPopup, {closePopup}] = usePopupInner(init);
const [regisEduDocModal, {openModal: openVideoModal}] = useModal();

const userId = ref('')
const eduId = ref('')
const userEduRecordId = ref('')
const fileList = ref<any[]>([])

async function init(data: any) {
  console.log('data', data)
  userId.value = data.userId
  eduId.value = data.eduId
  userEduRecordId.value = data.id
  getData();
}

async function getData() {
  const resp = await testUserEduRecordDocApi.getDocList(userEduRecordId.value, userId.value)
  console.log('res.data', resp.data)
  fileList.value = resp.data
}


function studyHandle(record) {
  openVideoModal(true, record);
}

async function handleClose() {
  closePopup();
}

async function handleSubmit() {
  closePopup();
  emit('reload');
}
</script>

