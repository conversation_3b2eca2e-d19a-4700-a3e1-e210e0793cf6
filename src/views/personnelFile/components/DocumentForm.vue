<script setup lang="ts">
  import { computed, reactive, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { userDocumentApi as api} from '/@/api';
  import dayjs from 'dayjs';
  import JnpfFaUploadFileQiniu from '/@/components/Jnpf/Upload/src/FaUploadFileQiniu.vue';
  import {useUserStore} from "/@/store/modules/user";

  const { t } = useI18n();
  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const formRef = ref(null);
  const userStore = useUserStore();
  // 证件类型选项
  const certTypeOptions = [
    { id: 'PDF', fullName: 'PDF文档' },
    { id: 'IMAGE', fullName: '图片' },
  ];

  // 证件状态选项
  const certStatusOptions = [
    { id: 1, fullName: '有效' },
    { id: 0, fullName: '过期' },
    { id: 2, fullName: '作废' },
  ];

  // 表单默认值
  const defaultFormData = {
    id: '',
    userId: '',
    documentType: 'PDF',
    attachmentId: '',
    validStart: '',
    validEnd: '',
    certStatus: 1,
    sort: 0,
  };

  // 表单数据
  const formData = reactive({ ...defaultFormData });

  // 表单标题
  const formTitle = computed(() => (formData.id ? '编辑证件' : '新增证件'));

  // 表单定义
  const formSchema: FormSchema[] = [
    {
      field: 'documentType',
      label: '证件类型',
      component: 'Select',
      required: true,
      componentProps: {
        options: certTypeOptions,
        placeholder: '请选择证件类型',
      },
      colProps: { span: 24 },
    },
    {
      field: 'validStart',
      label: '有效期开始',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: '开始日期',
        style: { width: '100%' },
      },
      colProps: { span: 24 },
    },
    {
      field: 'validEnd',
      label: '有效期结束',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: '结束日期',
        style: { width: '100%' },
      },
      colProps: { span: 24 },
    },
    {
      field: 'certStatus',
      label: '证件状态',
      component: 'Select',
      defaultValue: 1,
      componentProps: {
        options: certStatusOptions,
        placeholder: '请选择证件状态',
      },
      colProps: { span: 24 },
    },
    {
      field: 'sort',
      label: '排序号',
      component: 'InputNumber',
      defaultValue: 0,
      componentProps: {
        min: 0,
        max: 999,
        style: { width: '100%' },
      },
      colProps: { span: 24 },
    },
  ];

  // 注册表单
  const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
  });

  // 注册弹窗
  const [registerModal, { closeModal, setModalProps }] = useModalInner(data => {
    resetFields();
    setModalProps({ loading: false, title: '新增证件' });

    if (data?.isEdit && data?.record) {
      // 编辑模式，设置表单数据
      const record = data.record;

      // 处理attachmentId，将字符串转换为数组
      if (record.attachmentId && typeof record.attachmentId === 'string') {
        record.attachmentId = record.attachmentId.includes(',') ? record.attachmentId.split(',') : [record.attachmentId];
      }

      Object.assign(formData, record);

      setFieldsValue({
        documentType: record.documentType,
        certStatus: record.certStatus,
        sort: record.sort,
        validStart: record.validStart ? dayjs(record.validStart) : null,
        validEnd: record.validEnd ? dayjs(record.validEnd) : null,
      });

      setModalProps({ title: '编辑证件' });
    } else {
      // 新增模式，重置表单数据
      Object.assign(formData, defaultFormData);
    }
  });


  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ loading: true });

      // 处理日期范围
      if (values.validStart && values.validEnd) {
        formData.validStart = dayjs(values.validStart).format('YYYY-MM-DD HH:mm:ss');
        formData.validEnd = dayjs(values.validEnd).format('YYYY-MM-DD HH:mm:ss');
      } else {
        formData.validStart = '';
        formData.validEnd = '';
      }

      // 设置其他表单字段
      formData.documentType = values.documentType;
      formData.certStatus = values.certStatus;
      formData.sort = values.sort;
      const userInfo = userStore.getUserInfo;
      formData.userId = userInfo.userId||'';
      if (!formData.attachmentId) {
        createMessage.warning('请上传证件文件');
        setModalProps({ loading: false });
        return;
      }

      // 调用保存API
      await saveDocument(formData);

      closeModal();
      emit('reload');
    } catch (error) {
      console.error('表单提交错误', error);
    } finally {
      setModalProps({ loading: false });
    }
  }


  // 模拟保存文档API
  function saveDocument(data) {
    // 如果attachmentId是数组，转换为逗号分隔的字符串
    if (Array.isArray(data.attachmentId)) {
      data.attachmentId = data.attachmentId.join(',');
    }

    const formMethod = formData.id ? api.update : api.save;
    formMethod(data)
      .then(res => {
        createMessage.success(res.msg);
        emit('reload');
      })
      .catch(() => {
      });
  }
</script>

<template>
  <BasicModal @register="registerModal" :title="formTitle" @ok="handleSubmit" :width="800" :maskClosable="false">
    <a-row class="dynamic-form">
      <BasicForm @register="registerForm" />

      <!-- 证件上传组件 -->
      <a-col :span="24" class="ant-col-item">
        <a-form-item label="证件文件" :label-col="{ style: { width: '100px' } }">
          <JnpfFaUploadFileQiniu v-model:value="formData.attachmentId" prefix="document" :multiple="true" />
        </a-form-item>
      </a-col>
    </a-row>
  </BasicModal>
</template>

<style scoped lang="less">
  .dynamic-form {
    padding: 0 10px;

    .ant-col-item {
      margin-bottom: 15px;
    }
  }
</style>
