<script setup lang="ts">
  import { useMessage } from '/@/hooks/web/useMessage';
  import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import dayjs from 'dayjs';
  import DocumentForm from './components/DocumentForm.vue';
  import { userDocumentApi as api} from '/@/api';
  import { getUserInfoList } from '/@/api/permission/user';
  import { ref, onMounted } from 'vue';
  defineOptions({ name: 'PersonnelFileManagement' });

  const { createMessage } = useMessage();
  const certTypeOptions = [
    { id: 'PDF', fullName: 'PDF文档' },
    { id: 'IMAGE', fullName: '图片' },
  ];

  const certStatusOptions = [
    { id: 1, fullName: '有效' },
    { id: 0, fullName: '过期' },
    { id: 2, fullName: '作废' },
  ];

  // 用户数据缓存
  const userCache = ref<Record<string, any>>({});
  // 用户名到ID的映射
  const nameToIdMap = ref<Record<string, string>>({});

  // 初始化时获取所有用户信息
  onMounted(async () => {
    try {
      const { data } = await getUserInfoList([]);
      if (data.list && Array.isArray(data.list)) {
        data.list.forEach(user => {
          userCache.value[user.id] = user;
          // 建立姓名到ID的映射
          if (user.realName) {
            nameToIdMap.value[user.realName] = user.id;
          }
        });
      }
    } catch (error) {
      console.error('初始化获取用户信息失败', error);
    }
  });

  // 获取用户信息
  async function getUserNames(userIds: string[]) {
    if (!userIds || userIds.length === 0) return;
    
    // 过滤掉已经缓存的用户ID
    const idsToFetch = userIds.filter(id => !userCache.value[id]);
    
    if (idsToFetch.length > 0) {
      try {
        const { data } = await getUserInfoList(idsToFetch);
        if (data.list && Array.isArray(data.list)) {
          data.list.forEach(user => {
            userCache.value[user.id] = user;
            // 更新姓名到ID的映射
            if (user.realName) {
              nameToIdMap.value[user.realName] = user.id;
            }
          });
        }
      } catch (error) {
        console.error('获取用户信息失败', error);
      }
    }
  }

  // 搜索前处理函数，将姓名转换为用户ID
  function handleSearchInfoBefore(params) {
    const nameInput = params.userId;
    if (nameInput && nameToIdMap.value[nameInput]) {
      // 如果输入的是姓名且在映射中存在，则转换为ID
      params.userId = nameToIdMap.value[nameInput];
    } else if (nameInput) {
      // 如果输入不是精确的姓名，则尝试模糊匹配
      const matchedNames = Object.keys(nameToIdMap.value).filter(name => 
        name.includes(nameInput)
      );
      
      if (matchedNames.length > 0) {
        // 找到匹配的用户，使用第一个匹配的ID
        params.userId = nameToIdMap.value[matchedNames[0]];
      }
      // 如果没有匹配到，保留原输入，后端可能有自己的处理逻辑
    }
    return params;
  }

  // 表格列定义
  const columns: BasicColumn[] = [
    {
      title: '姓名',
      dataIndex: 'userId',
      width: 120,
      customRender: ({ text }) => {
        const user = userCache.value[text];
        return user ? user.realName : text;
      },
    },
    {
      title: '证件类型',
      dataIndex: 'documentType',
      width: 120,
      customRender: ({ text }) => {
        const option = certTypeOptions.find(item => item.id === text);
        return option ? option.fullName : text;
      },
    },

    {
      title: '有效期',
      dataIndex: 'validPeriod',
      width: 250,
      customRender: ({ record }) => {
        const start = record.validStart ? dayjs(record.validStart).format('YYYY-MM-DD') : '永久';
        const end = record.validEnd ? dayjs(record.validEnd).format('YYYY-MM-DD') : '永久';
        return `${start} 至 ${end}`;
      },
    },
    {
      title: '状态',
      dataIndex: 'certStatus',
      width: 100,
      customRender: ({text}) => {
        const option = certStatusOptions.find(item => item.id === text);
        return option ? option.fullName : text;
      },
    },
  ];

  // 表格操作列
  const actionColumn = {
    width: 100,
    title: '操作',
    dataIndex: 'action',
  };

  // 注册表格
  const [registerTable, { reload }] = useTable({
    api: api.page,
    columns,
    actionColumn,
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'userId',
          component: 'Input',
          label: '姓名',
          componentProps: {
            placeholder: '请输入姓名',
          },
          colProps: { span: 8 },
        },
        {
          field: 'documentType',
          component: 'Select',
          label: '证件类型',
          componentProps: {
            options: certTypeOptions,
            placeholder: '请选择证件类型',
          },
          colProps: { span: 8 },
        },
        {
          field: 'certStatus',
          component: 'Select',
          label: '证件状态',
          componentProps: {
            options: certStatusOptions,
            placeholder: '请选择证件状态',
          },
          colProps: { span: 8 },
        },
      ],
    },
    beforeFetch: handleSearchInfoBefore,
    rowKey: 'id',
    bordered: true,
    showIndexColumn: true,
    canResize: true,
    pagination: {
      pageSize: 10,
    },
    searchInfo: {
      _sorter: 'f_creator_time DESC',
    },
  });

  // 监听数据加载完成事件，获取用户信息
  function handleFetchSuccess({ items }) {
    if (items && items.length > 0) {
      const userIds = items.map(item => item.userId).filter(Boolean);
      getUserNames(userIds);
    }
  }

  // 注册表单弹窗
  const [registerModal, { openModal }] = useModal();

  // 表格操作按钮

  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '编辑',
        color: 'success',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除此证件吗？',
          confirm: handleDelete.bind(null, record),
        },
      }]
  }


  // 查看证件
  function handleView(record) {
    // 查看逻辑，根据证件类型展示PDF或图片预览
    const url = `/api/file/download?fileId=${record.attachment_id}`;
    if (record.document_type === 'PDF') {
      window.open(url);
    } else {
      // 图片预览逻辑
      createImagePreview({
        imageList: [url],
      });
    }
  }

  // 编辑证件
  function handleEdit(record) {
    openModal(true, { record, isEdit: true });
  }

  // 删除证件
  async function handleDelete(record) {
    try {
      await deleteDocument(record.id);
      reload();
    } catch (error) {
      console.error('删除失败', error);
    }
  }

  // 新增证件
  function handleAdd() {
    openModal(true, { isEdit: false });
  }

  // 模拟删除证件API
  function deleteDocument(id) {
    api.remove(id).then(res => {
      createMessage.success('删除成功');
      reload();
    });
  }

  // 创建图片预览
  function createImagePreview(options) {
    // 实际项目中应该使用真实的图片预览组件
    console.log('预览图片', options);
  }
</script>

<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable" @fetch-success="handleFetchSuccess">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd">新增证件</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <DocumentForm @register="registerModal" @reload="reload" />
  </div>
</template>

<style scoped lang="less"></style>
