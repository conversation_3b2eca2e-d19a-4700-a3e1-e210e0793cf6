import type { AppRouteRecordRaw } from '/@/router/types';

import { PAGE_NOT_FOUND_ROUTE, REDIRECT_ROUTE, COMMON_ROUTE } from '/@/router/routes/basic';

import { mainOutRoutes } from './mainOut';
import { PageEnum } from '/@/enums/pageEnum';
import { t } from '/@/hooks/web/useI18n';

// 根路由
export const RootRoute: AppRouteRecordRaw = {
  path: '/',
  name: 'Root',
  redirect: PageEnum.BASE_HOME,
  meta: {
    title: 'Root',
  },
};

export const LoginRoute: AppRouteRecordRaw = {
  path: '/login',
  name: 'Login',
  component: () => import('/@/views/basic/login/Login.vue'),
  meta: {
    title: t('routes.basic.login'),
  },
};
// 初始重置密码路由
export const ResetPwdRoute: AppRouteRecordRaw = {
  path: '/resetPwd',
  name: 'ResetPwd',
  component: () => import('/@/views/basic/resetPwd/index.vue'),
  meta: {
    title: '重置密码',
  },
};
// 初始修改个人信息
export const FirstUpdateProfileRoute: AppRouteRecordRaw = {
  path: '/firstUpdateProfile',
  name: 'FirstUpdateProfile',
  component: () => import('/@/views/basic/firstUpdateProfile/index.vue'),
  meta: {
    title: '重置密码',
  },
};
// 表单外链
export const FormShortLinkRoute: AppRouteRecordRaw = {
  path: '/formShortLink',
  name: 'FormShortLink',
  component: () => import('/@/views/common/formShortLink/index.vue'),
  meta: {
    title: '',
  },
};
// 漳州-证件H5查看页面
export const CertificateViewRoute: AppRouteRecordRaw = {
  path: '/h5/out/cert',
  name: 'CertificateView',
  component: () => import('/@/views/h5/out/certificate/CertificateView.vue'),
  meta: {
    title: '',
    ignoreAuth: true,
  },
};

// 漳州-摄像头直播H5查看页面
export const IotCameraRoute: AppRouteRecordRaw = {
  path: '/h5/out/iot/camera',
  name: 'CameraView',
  component: () => import('/@/views/h5/out/iot/camera/CameraView.vue'),
  meta: {
    title: '',
    ignoreAuth: true,
  },
};

// APP下载页面
export const AppDownloadRoute: AppRouteRecordRaw = {
  path: '/h5/out/app-download',
  name: 'AppDownload',
  component: () => import('/@/views/h5/out/AppDownload.vue'),
  meta: {
    title: 'APP下载',
    ignoreAuth: true,
  },
};

// 人员信息更新页面
export const UserInfoUpdateRoute: AppRouteRecordRaw = {
  path: '/h5/out/user-info-update',
  name: 'UserInfoUpdate',
  component: () => import('/@/views/h5/out/UserInfoUpdate.vue'),
  meta: {
    title: '人员信息更新',
    ignoreAuth: true,
  },
};

// -------------------------- 可视化大屏 --------------------------
// 入场三级配置-公司级
export const DatavEduRoute: AppRouteRecordRaw = {
  path: '/datav/edu',
  name: 'DatavEdu',
  component: () => import('/src/views/datav/layout/ScaleScreen.vue'),
  meta: {
    title: t('routes.basic.login'),
  },
  children: [
    {
      path: '/datav/edu/company',
      name: 'Company',
      component: () => import('/@/views/datav/edu/company/index.vue'),
      meta: {
        title: '入场三级-公司级',
      },
    },
    {
      path: '/datav/edu/bu',
      name: 'Bu',
      component: () => import('/@/views/datav/edu/bu/index.vue'),
      meta: {
        title: '入场三级-事业部级',
      },
    },
    {
      path: '/datav/edu/project',
      name: 'Project',
      component: () => import('/@/views/datav/edu/project/index.vue'),
      meta: {
        title: '入场三级-项目部级',
      },
    },
  ]
};


// Basic routing without permission
// 未经许可的基本路由
export const basicRoutes = [LoginRoute, ResetPwdRoute, DatavEduRoute, FirstUpdateProfileRoute, FormShortLinkRoute, CertificateViewRoute, IotCameraRoute, AppDownloadRoute, UserInfoUpdateRoute, RootRoute, ...mainOutRoutes, REDIRECT_ROUTE, PAGE_NOT_FOUND_ROUTE, COMMON_ROUTE];
