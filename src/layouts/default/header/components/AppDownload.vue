<template>
  <div :class="prefixCls">
    <!-- 扫码更新人员信息图标 -->
    <div
      :class="`${prefixCls}-icon`"
      @mouseenter="handleUserInfoMouseEnter"
      @mouseleave="handleUserInfoMouseLeave"
      @click="handleUserInfoClick"
    >
      <UserOutlined />
    </div>

    <!-- 人员信息更新二维码卡片 -->
    <div
      v-if="showUserInfoQrcode"
      :class="`${prefixCls}-card ${prefixCls}-user-info-card`"
      @mouseenter="handleUserInfoCardMouseEnter"
      @mouseleave="handleUserInfoCardMouseLeave"
    >
      <div class="qrcode-container">
        <div class="qrcode-title">扫码更新人员信息</div>
        <div class="qrcode-wrapper">
          <canvas ref="userInfoQrcodeRef" class="qrcode-canvas"></canvas>
        </div>
        <div class="qrcode-desc">使用手机扫描二维码更新信息</div>
        <div class="qrcode-actions">
          <div class="qrcode-print" @click="handleUserInfoPrint" title="打印二维码">
            <PrinterOutlined />
          </div>
          <div class="qrcode-close" @click="handleUserInfoClose">
            <CloseOutlined />
          </div>
        </div>
      </div>
    </div>

    <!-- APP下载图标 -->
    <div
      :class="`${prefixCls}-icon`"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @click="handleClick"
    >
      <DownloadOutlined />
    </div>

    <!-- APP下载二维码卡片 -->
    <div
      v-if="showQrcode"
      :class="`${prefixCls}-card`"
      @mouseenter="handleCardMouseEnter"
      @mouseleave="handleCardMouseLeave"
    >
      <div class="qrcode-container">
        <div class="qrcode-title">扫码下载APP</div>
        <div class="qrcode-wrapper">
          <canvas ref="qrcodeRef" class="qrcode-canvas"></canvas>
        </div>
        <div class="qrcode-desc">使用手机扫描二维码下载</div>
        <div class="qrcode-close" @click="handleClose">
          <CloseOutlined />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, nextTick } from 'vue';
  import { DownloadOutlined, CloseOutlined, UserOutlined, PrinterOutlined } from '@ant-design/icons-vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import QRCode from 'qrcode';

  defineOptions({ name: 'AppDownload' });

  const props = defineProps({
    // APP下载链接，可以是应用商店链接或直接下载链接
    downloadUrl: {
      type: String,
      default: `${window.location.origin}/h5/out/app-download`, // APP下载页面链接
      // default: 'http://************:3100/h5/out/app-download',
    },
    // 二维码大小
    qrcodeSize: {
      type: Number,
      default: 180,
    },
    // 人员信息更新链接
    userInfoUrl: {
      type: String,
      default: `${window.location.origin}/h5/out/user-info-update`, // 人员信息更新页面链接
      // default: 'http://************:3100/h5/out/user-info-update',
    },
  });

  const { prefixCls } = useDesign('header-app-download');
  const qrcodeRef = ref<HTMLCanvasElement>();
  const userInfoQrcodeRef = ref<HTMLCanvasElement>();
  const showQrcode = ref(false);
  const showUserInfoQrcode = ref(false);
  const isHovering = ref(false);
  const isCardHovering = ref(false);
  const isUserInfoHovering = ref(false);
  const isUserInfoCardHovering = ref(false);
  let hideTimer: any = null;

  // 生成二维码
  const generateQRCode = async () => {
    if (!qrcodeRef.value) return;

    try {
      await QRCode.toCanvas(qrcodeRef.value, props.downloadUrl, {
        width: props.qrcodeSize,
        height: props.qrcodeSize,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      });
    } catch (error) {
      console.error('生成二维码失败:', error);
    }
  };

  // 生成人员信息更新二维码
  const generateUserInfoQRCode = async () => {
    if (!userInfoQrcodeRef.value) return;

    try {
      await QRCode.toCanvas(userInfoQrcodeRef.value, props.userInfoUrl, {
        width: props.qrcodeSize,
        height: props.qrcodeSize,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      });
    } catch (error) {
      console.error('生成人员信息更新二维码失败:', error);
    }
  };

  // 打印人员信息更新二维码
  const handleUserInfoPrint = () => {
    if (!userInfoQrcodeRef.value) return;

    try {
      const canvas = userInfoQrcodeRef.value;
      const dataURL = canvas.toDataURL('image/png');
      
      // 创建打印窗口
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        console.error('无法打开打印窗口，请检查浏览器弹窗设置');
        return;
      }

      // 设置打印页面内容
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>扫码更新人员信息</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              display: flex;
              flex-direction: column;
              align-items: center;
              font-family: Arial, sans-serif;
            }
            .print-container {
              text-align: center;
              max-width: 400px;
            }
            .print-title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 20px;
              color: #333;
            }
            .print-qrcode {
              margin: 20px 0;
            }
            .print-qrcode img {
              max-width: 200px;
              height: auto;
            }
            .print-desc {
              font-size: 14px;
              color: #666;
              margin-top: 20px;
              line-height: 1.5;
            }
            .print-url {
              font-size: 12px;
              color: #999;
              margin-top: 10px;
              word-break: break-all;
            }
            @media print {
              body { margin: 0; }
              .print-container { max-width: none; }
            }
          </style>
        </head>
        <body>
          <div class="print-container">
            <div class="print-title">扫码更新人员信息</div>
            <div class="print-qrcode">
              <img src="${dataURL}" alt="人员信息更新二维码" />
            </div>
            <div class="print-desc">
              使用手机扫描上方二维码<br>
              快速更新个人信息
            </div>
            <div class="print-url">${props.userInfoUrl}</div>
          </div>
        </body>
        </html>
      `);
      
      printWindow.document.close();
      
      // 等待图片加载完成后打印
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };
    } catch (error) {
      console.error('打印二维码时出错:', error);
    }
  };

  // 鼠标进入图标
  const handleMouseEnter = () => {
    isHovering.value = true;
    clearTimeout(hideTimer);
    showQrcode.value = true;
    nextTick(() => {
      generateQRCode();
    });
  };

  // 鼠标离开图标
  const handleMouseLeave = () => {
    isHovering.value = false;
    hideTimer = setTimeout(() => {
      if (!isCardHovering.value) {
        showQrcode.value = false;
      }
    }, 200);
  };

  // 点击图标
  const handleClick = () => {
    showQrcode.value = !showQrcode.value;
    if (showQrcode.value) {
      nextTick(() => {
        generateQRCode();
      });
    }
  };

  // 鼠标进入卡片
  const handleCardMouseEnter = () => {
    isCardHovering.value = true;
    clearTimeout(hideTimer);
  };

  // 鼠标离开卡片
  const handleCardMouseLeave = () => {
    isCardHovering.value = false;
    if (!isHovering.value) {
      hideTimer = setTimeout(() => {
        showQrcode.value = false;
      }, 200);
    }
  };

  // 关闭卡片
  const handleClose = () => {
    showQrcode.value = false;
  };

  // 鼠标进入人员信息更新图标
  const handleUserInfoMouseEnter = () => {
    isUserInfoHovering.value = true;
    clearTimeout(hideTimer);
    showUserInfoQrcode.value = true;
    nextTick(() => {
      generateUserInfoQRCode();
    });
  };

  // 鼠标离开人员信息更新图标
  const handleUserInfoMouseLeave = () => {
    isUserInfoHovering.value = false;
    hideTimer = setTimeout(() => {
      if (!isUserInfoCardHovering.value) {
        showUserInfoQrcode.value = false;
      }
    }, 200);
  };

  // 点击人员信息更新图标
  const handleUserInfoClick = () => {
    showUserInfoQrcode.value = !showUserInfoQrcode.value;
    if (showUserInfoQrcode.value) {
      nextTick(() => {
        generateUserInfoQRCode();
      });
    }
  };

  // 鼠标进入人员信息更新卡片
  const handleUserInfoCardMouseEnter = () => {
    isUserInfoCardHovering.value = true;
    clearTimeout(hideTimer);
  };

  // 鼠标离开人员信息更新卡片
  const handleUserInfoCardMouseLeave = () => {
    isUserInfoCardHovering.value = false;
    if (!isUserInfoHovering.value) {
      hideTimer = setTimeout(() => {
        showUserInfoQrcode.value = false;
      }, 200);
    }
  };

  // 关闭人员信息更新卡片
  const handleUserInfoClose = () => {
    showUserInfoQrcode.value = false;
  };
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-header-app-download';

  .@{prefix-cls} {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;

    &-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 18px;
      color: @text-color;
      transition: color 0.3s;

      &:hover {
        color: @primary-color;
      }

      .anticon {
        font-size: 18px;
      }
    }

    &-card {
      position: absolute;
      top: 100%;
      right: 0;
      margin-top: 8px;
      z-index: 1050;
      animation: fadeInDown 0.3s ease-out;
    }

    &-user-info-card {
      right: auto;
      left: 0;
    }

    .qrcode-container {
      position: relative;
      text-align: center;
      padding: 20px;
      background: @component-background;
      border-radius: 12px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      min-width: 220px;
      border: 1px solid @border-color-base;

      &::before {
        content: '';
        position: absolute;
        top: -8px;
        right: 20px;
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid @component-background;
      }

      &::after {
        content: '';
        position: absolute;
        top: -9px;
        right: 20px;
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid @border-color-base;
      }
    }

    .qrcode-title {
      font-size: 16px;
      font-weight: 500;
      color: @text-color;
      margin-bottom: 16px;
    }

    .qrcode-wrapper {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;
    }

    .qrcode-canvas {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .qrcode-desc {
      font-size: 12px;
      color: @text-color-secondary;
      line-height: 1.4;
    }

    .qrcode-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;
    }

    .qrcode-print {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 16px;
      color: @text-color;
      transition: color 0.3s;
      padding: 4px 8px;
      border-radius: 4px;

      &:hover {
        color: @primary-color;
        background-color: @hover-background;
      }

      .anticon {
        font-size: 16px;
      }
    }

    .qrcode-close {
      position: absolute;
      top: 8px;
      right: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      color: @text-color-secondary;
      transition: color 0.3s;
      padding: 4px;
      border-radius: 4px;

      &:hover {
        color: @text-color;
        background-color: @hover-background;
      }

      .anticon {
        font-size: 14px;
      }
    }
  }

  @keyframes fadeInDown {
    0% {
      opacity: 0;
      transform: translateY(-10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
