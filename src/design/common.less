.jnpf-content-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  position: relative;
  background: @app-main-background;
  overflow: hidden;
  &.jnpf-content-wrapper-form {
    flex-direction: column;
    background-color: @component-background;
    .jnpf-content-wrapper-form-body {
      flex: 1;
      overflow: hidden;
    }
  }
  .jnpf-content-wrapper-left {
    width: 220px;
    background-color: @component-background;
    flex-shrink: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-right: 10px;
  }
  .jnpf-content-wrapper-center {
    flex: 1;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .jnpf-content-wrapper-search-box {
      padding: 10px 10px 0;
      flex-shrink: 0;
      margin-bottom: 10px;
      background-color: @component-background;
    }
    .jnpf-content-wrapper-content {
      flex: 1;
      overflow: hidden;
    }
    .jnpf-content-wrapper-tabs {
      &.ant-tabs-card {
        height: 100%;
        & > .ant-tabs-nav {
          margin-bottom: 0;
          .ant-tabs-tab {
            border-top: none;
            &:first-child {
              border-left: none;
            }
          }
        }
        & > .ant-tabs-content-holder {
          & > .ant-tabs-content {
            height: 100%;
            overflow: hidden;
            & > .ant-tabs-tabpane {
              height: 100%;
              overflow: auto;
            }
          }
        }
      }
    }
  }
}
.ant-modal.transfer-modal {
  &.member-modal {
    .ant-modal-body > .scrollbar {
      padding: 10px;
      .transfer__body .transfer-pane {
        width: 100%;
      }
    }
  }
  .ant-modal-body {
    padding: 20px 10px;
    & > .scrollbar {
      padding: 20px 10px;
    }
    .scrollbar .scrollbar__wrap {
      margin-bottom: 0 !important;
    }
    .transfer__body {
      line-height: 32px;
      display: flex;
      justify-content: space-around;
      height: 400px;
    }
    .transfer-pane {
      width: 360px;
      &.left-pane {
        .selected-item-user {
          cursor: pointer;
        }
      }
      .transfer-pane__tool {
        margin-bottom: 8px;
        height: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .remove-all-btn {
          cursor: pointer;
          color: @error-color;
        }
      }
      .transfer-pane__body {
        position: relative;
        width: 100%;
        height: calc(100% - 40px);
        overflow: auto;
        overflow-x: hidden;
        font-size: 14px;
        border: 1px solid @border-color-base;
        border-radius: @border-radius-base;
        &.transfer-pane__body-tab {
          overflow: hidden;
          display: flex;
          flex-direction: column;
          .ant-tabs {
            .ant-tabs-nav {
              margin-bottom: 0;
            }
            .ant-tabs-nav-list {
              width: 100%;
            }
            .ant-tabs-tab {
              flex: auto;
              .ant-tabs-tab-btn {
                width: 100%;
                text-align: center;
              }
            }
          }
          .tree-main {
            flex: 1;
            overflow: auto;
          }
          .pane-tabs {
            flex-shrink: 0;
            &.pane-tabs-single {
              .ant-tabs-tab {
                width: 25%;
                flex: none;
              }
            }
            .ant-tabs-nav-operations {
              display: none !important;
            }
          }
        }
      }
      .ant-tree {
        .ant-tree-treenode {
          &.ant-tree-treenode-selected {
            background-color: @selected-hover-bg;
          }
        }
      }
      .custom-title {
        height: 38px;
        padding: 0 12px;
        line-height: 38px;
        font-size: 14px;
        border-bottom: 1px solid @border-color-base;
      }
      .selected-item {
        width: 100%;
        padding: 0px 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &.selected-item-user {
          .selected-item-main {
            border-bottom: 1px solid @border-color-base1;
            display: flex;
            align-items: center;
            height: 50px;
            width: 100%;
            box-sizing: border-box;
          }
          .selected-item-headIcon {
            flex-shrink: 0;
            &.icon {
              width: 36px;
              height: 36px;
              text-align: center;
              i {
                font-size: 22px;
                line-height: 36px;
              }
            }
          }
          .selected-item-text {
            min-width: 0;
            flex: 1;
            margin-left: 10px;
            .name {
              height: 20px;
              line-height: 20px;
              font-size: 14px;
              margin-bottom: 2px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .organize {
              height: 17px;
              line-height: 17px;
              color: #999999;
              font-size: 12px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
        &:hover {
          background-color: @selected-hover-bg;
        }
        span {
          max-width: 90%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .delete-btn:hover {
          color: @error-color;
          cursor: pointer;
        }
      }
      .selected-item-user-multiple {
        padding: 0 12px;
        position: relative;
        .selected-item-title {
          font-size: 14px;
          display: flex;
          align-items: center;
          span {
            padding-left: 6px;
          }
        }
        .selected-item-user {
          padding: 0 15px;
          &:last-child {
            border-bottom: 1px solid @border-color-base1;
            .selected-item-main {
              border-bottom: none;
            }
          }
          .selected-item-main {
            box-sizing: content-box;
          }
        }
        .selected-item-icon {
          width: 36px;
          height: 36px;
          background: linear-gradient(193deg, #a7d6ff 0%, #1990fa 100%);
          border-radius: 50%;
          line-height: 36px;
          color: #ffffff;
          font-size: 14px;
          text-align: center;
        }
      }
    }
  }
}
.ant-modal.form-script-modal {
  .ant-modal-body {
    height: 70vh;
    & > .scrollbar {
      padding: 0;
      height: 100%;
      .scrollbar__view {
        height: 100%;
        overflow: hidden;
        & > div {
          height: 100%;
          overflow: hidden;
          max-height: 100% !important;
        }
        .form-script-modal-body {
          height: 100%;
          display: flex;
          overflow: hidden;
          padding: 20px;
          box-sizing: border-box;
          .left-board {
            height: 100%;
            width: 220px;
            flex-shrink: 0;
            margin-right: 10px;
            overflow: hidden auto;
          }
          .main-board {
            height: 100%;
            flex: 1;
            display: flex;
            flex-direction: column;
            .main-board-editor {
              flex: 1;
              border: 1px solid @border-color-base1;
            }
            .main-board-tips {
              flex-shrink: 0;
              padding: 8px 16px;
              background-color: @primary-1;
              border-radius: 4px;
              border-left: 5px solid @primary-color;
              margin-top: 20px;

              p {
                line-height: 24px;
                color: @text-color-help-dark;

                span {
                  display: inline-block;
                  padding-right: 10px;
                }
              }
            }
          }
        }
      }
    }
  }
  &.btn-event-modal {
    .form-script-modal-body {
      padding: 20px 50px !important;
      overflow: auto !important;
      .ant-form-item {
        margin-bottom: 20px;
        &.ant-form-item-with-help {
          margin-bottom: 0;
        }
        .ant-form-item-explain {
          height: 20px;
          min-height: 20px;
        }
      }
      .tip {
        display: inline-block;
        vertical-align: middle;
        line-height: 32px;
        color: @text-color-secondary;
      }
    }
  }
}
.ant-modal.formula-modal {
  .ant-modal-body {
    & > .scrollbar {
      padding: 20px;
    }
  }
  .formula-modal-body {
    .code-editor-area {
      height: 250px;
      border: 1px solid @border-color-base1;
      border-radius: 6px;
      margin-bottom: 10px;
      overflow: hidden;
    }
    .operation-area {
      height: 250px;
      display: flex;
      .area-item {
        height: 250px;
        border: 1px solid @border-color-base1;
        border-radius: 6px;
        width: 220px;
        overflow: hidden;
        &.formula-area {
          margin: 0 20px;
        }
        &.formula-desc-area {
          width: 280px;
          .area-content {
            padding: 10px;
          }
        }
        .area-title {
          padding: 0 10px;
          height: 34px;
          line-height: 34px;
          border-bottom: 1px solid @border-color-base1;
        }
        .area-content {
          height: 216px;
          overflow: hidden auto;
          .formula-desc-wrapper {
            color: #5e6d82;
            overflow-y: auto;
            & > li {
              margin-bottom: 4px;
              word-break: break-all;
              word-wrap: break-word;
              list-style-type: none;
              font-size: 12px;
              line-height: 18px;
            }
            .formula-name {
              color: @primary-color;
            }
          }
        }
      }
    }
  }
}
.jnpf-common-page-header {
  height: 60px;
  border-bottom: 1px solid @border-color-base;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  flex-shrink: 0;
}
.ant-modal.jnpf-full-modal {
  .ant-modal-header {
    padding: 0;
    .ant-modal-title {
      font-weight: normal;
    }
    .jnpf-full-modal-header {
      padding: 0 20px;
      height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .header-title {
        height: 60px;
        width: 320px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .header-logo {
          display: inline-block;
          width: auto;
          height: 40px;
          vertical-align: top;
          margin-right: 3px;
          font-size: 30px;
        }

        .header-txt {
          line-height: 60px;
          display: inline-block;
          margin: 0;
          font-size: 18px;
          max-width: 150px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
        }
      }
      .options {
        width: 320px;
        justify-content: flex-end;
      }
      .ant-steps {
        width: auto;
        .ant-steps-item {
          width: 150px;
        }
      }
    }
  }
  .ant-modal-body {
    overflow: hidden;
    background-color: @app-main-background;
    & > .scrollbar {
      padding: 10px;
      background-color: @app-main-background;
      & > .scrollbar__wrap {
        & > .scrollbar__view {
          height: 100%;
          overflow: hidden;
          & > div {
            height: 100% !important;
          }
        }
      }
    }
    .basic-content {
      height: 100%;
      overflow: hidden;
      .basic-form {
        height: 100%;
        padding: 20px;
        border-radius: 4px;
        background-color: @component-background;
        overflow-y: auto;
      }
    }
    .@{namespace}-basic-table {
      height: auto;
    }
  }
}

.ant-modal.jnpf-list-modal {
  .ant-modal-body {
    height: 70vh;
    overflow: hidden;
    & > .scrollbar {
      padding: 0;
      & > .scrollbar__bar {
        display: none !important;
      }
      .scrollbar__wrap {
        .scrollbar__view {
          height: 100%;
          overflow: hidden;
          & > div {
            height: 100% !important;
            max-height: 100% !important;
          }
        }
      }
    }
  }
}
.ant-modal.jnpf-add-modal {
  &.jnpf-preview-modal {
    .add-main {
      .add-item {
        background: #f0fff7;
        &.add-item-left {
          background: #edfbfd;
          .add-icon {
            background: #d6f0ff;
            color: #3399fa;
          }
        }
        .add-icon {
          background: #ccf7e0;
          color: #36ac6c;
        }
        .add-txt {
          height: auto;
        }
      }
    }
  }
  .ant-modal-body {
    height: 176px;
    overflow: hidden;
    & > .scrollbar {
      padding: 20px;
      .scrollbar__bar {
        display: none !important;
      }
    }
  }
  .add-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .add-item {
      width: 270px;
      height: 136px;
      background: #fef3e6;
      display: flex;
      align-items: center;
      cursor: pointer;
      padding-left: 20px;
      &:hover {
        opacity: 0.9;
      }
      &.add-item-left {
        background: #f1f5ff;
        .add-icon {
          background: #ccd9ff;
          color: #537eff;
        }
      }
      .add-icon {
        width: 56px;
        height: 56px;
        margin-right: 10px;
        background: #fce1bf;
        border-radius: 10px;
        color: #ea986c;
        flex-shrink: 0;
        font-size: 30px;
        line-height: 56px;
        text-align: center;
      }
      .add-txt {
        height: 56px;
        P {
          line-height: 28px;
        }
        .add-title {
          font-size: 18px;
        }
        .add-desc {
          color: @text-color-secondary;
          font-size: 12px;
        }
      }
    }
  }
}
.ant-modal.fixed-height-modal {
  .ant-modal-body {
    height: 70vh;
  }
}
.ant-modal.jnpf-flow-list-modal {
  .ant-modal-body > .scrollbar {
    padding: 20px 0;
  }
  .template-list {
    height: 260px;
    .template-item {
      margin: 0 50px;
      height: 40px;
      border-radius: 4px;
      margin-bottom: 10px;
      line-height: 40px;
      padding: 0 20px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      background-color: @app-main-background;
      cursor: pointer;
      &:hover {
        color: @primary-color;
        background-color: @primary-2;
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
.ant-modal.jnpf-flow-user-list-modal {
  .ant-modal-body > .scrollbar {
    padding: 20px 0;
  }
  .flow-user-list {
    height: 360px;
    .user-item-main {
      margin: 0 100px 15px;
      cursor: pointer;
      height: 70px;
      position: relative;
      border-radius: 4px;
      border: 1px solid @border-color-base1;
      display: flex;
      align-items: center;
      padding: 0 20px;
      cursor: pointer;
      &.active {
        border: 1px solid @primary-color;
        box-shadow: 0 0 6px rgba(6, 58, 108, 0.26);
        .icon-checked {
          display: block;
        }
      }
      &:last-child {
        margin-bottom: 0;
      }
      .user-avatar {
        margin-right: 20px;
        flex-shrink: 0;
      }
      .user-text {
        min-width: 0;
        flex: 1;
        line-height: 24px;
        font-size: 14px;
        .user-organize {
          font-size: 12px;
          color: #999999;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-break: break-all;
        }
      }
      .icon-checked {
        display: none;
        width: 18px;
        height: 18px;
        border: 18px solid @primary-color;
        border-left: 18px solid transparent;
        border-top: 18px solid transparent;
        border-bottom-right-radius: 4px;
        position: absolute;
        right: -1px;
        bottom: -1px;
        .anticon {
          position: absolute;
          top: 1px;
          left: 0;
          font-size: 14px;
          color: #fff;
        }
      }
    }
  }
}
.ant-modal.icon-modal {
  .icon-modal-title {
    display: flex;
    align-items: center;
    font-weight: normal;
    font-size: 18px;
    .ant-input-search {
      width: 300px;
      margin-left: 10px;
    }
  }
  .ant-modal-body {
    padding: 0 10px !important;
  }
  .main {
    height: 70vh;
    overflow: hidden;
    .ant-tabs,
    .ant-tabs-content {
      height: 100%;
    }
    .ant-tabs-nav {
      margin-bottom: 0 !important;
    }
    .icon-box-list {
      padding-bottom: 8px;
      > .ant-btn {
        margin: 8px 0 0 8px;
        width: 60px;
        height: 60px;
        padding: 0;
        line-height: 60px;
        text-align: center;
        transition: transform 300ms;
        i {
          display: inline-block;
          font-size: 24px;
          transition: 300ms;
        }
        &.is-active {
          color: @primary-color;
          border-color: @primary-color;
        }
        &:hover {
          i {
            transform: scale(1.8);
          }
        }
      }
    }
  }
}

.table-add-action {
  margin-top: 10px;
  border: 1px dashed @border-color-base;
  text-align: center;
  cursor: pointer;
}
.jnpf-sub-table.jnpf-basic-table-form-container {
  &.jnpf-sub-table-full {
    .ant-form {
      width: 100% !important;
    }
  }
  position: relative;
  .ant-form {
    position: absolute;
    left: 0;
    top: 4px;
    z-index: 1;
    width: calc(100% - 80px) !important;
  }
}
.jnpf-task-table.jnpf-basic-table-form-container {
  .ant-form {
    width: calc(100% - 150px) !important;
  }
}
.ant-table {
  .ant-table-expanded-row-fixed {
    padding: 10px !important;
  }
  .ant-select {
    width: 100%;
  }
  .ant-btn.action-btn {
    padding: 0;
  }
  .ant-table__empty-text {
    line-height: 30px;
    padding: 30px 0;
  }
}

.jnpf-sign {
  .sign-tip {
    color: @primary-color;
  }
}

.average-tabs {
  &.ant-tabs {
    .ant-tabs-nav {
      margin-bottom: 0;
    }
    .ant-tabs-nav-list {
      width: 100%;
    }
    .ant-tabs-tab {
      flex: auto;
      padding: 10px 0;
      .ant-tabs-tab-btn {
        width: 100%;
        text-align: center;
      }
    }
  }
}

.common-container-modal {
  .ant-modal-body {
    height: 70vh;
    overflow: hidden;
    .jnpf-content-wrapper-left {
      margin-right: 0;
      border-right: 1px solid @border-color-base1;
    }
  }
}
.common-container {
  .disabled-select {
    .ant-select-selection-item {
      color: @primary-color !important;
      text-decoration: underline;
    }
  }
}
.required-sign {
  color: @error-color;
}
.common-container-drawer {
  .common-container-drawer-body {
    height: calc(100% - 60px);
  }
}
.dynamic-form {
  & > .ant-form {
    width: 100%;
  }
}
.word-form {
  margin-bottom: 20px;
  border-top: 1px solid @border-color-base;
  border-left: 1px solid @border-color-base;
  &.word-form-detail {
    .ant-col-item {
      & > .ant-form-item {
        .ant-form-item-label + .ant-form-item-control {
          padding: 8px 10px;
        }
      }
    }
  }
  .table-grid-box {
    margin-bottom: 0;
    td {
      padding-top: 0;
    }
  }
  .ant-col-item {
    padding: 0 !important;
    border-right: 1px solid @border-color-base;
    border-bottom: 1px solid @border-color-base;
    & > .ant-form-item {
      margin-bottom: 0;
      .ant-form-item-label {
        border-right: 1px solid @border-color-base;
        padding: 5px 0;
      }
      .ant-form-item-label + .ant-form-item-control {
        padding: 5px 0;
      }
      .ant-input,
      .ant-input-number,
      .ant-select,
      .ant-select-selector,
      .ant-input-affix-wrapper,
      .ant-picker {
        border: 0 !important;
        &:focus,
        &-focused {
          box-shadow: unset !important;
        }
      }
      .ant-select-focused {
        .ant-select-selector {
          box-shadow: unset !important;
        }
      }
      .tox-tinymce {
        border: none !important;
      }
    }
  }
  .jnpf-basic-caption {
    border-bottom: 0 !important;
  }
  .ant-collapse {
    .ant-collapse-header {
      border-right: 1px solid @border-color-base;
      border-bottom: 1px solid @border-color-base;
    }
    .ant-collapse-content-box {
      padding: 0 !important;
    }
  }

  .ant-card {
    border: none !important;
    &:hover {
      box-shadow: unset !important;
    }
    .ant-card-head {
      border-right: 1px solid @border-color-base;
    }
    .ant-card-body {
      padding: 0;
    }
  }
  .ant-tabs {
    .ant-tabs-nav {
      margin-bottom: 0;
      border-right: 1px solid @border-color-base;
    }
  }
  .ant-rate,
  .ant-radio-group,
  .ant-checkbox-group,
  .upload-file-container,
  .upload-img-container {
    padding: 0 11px;
  }
  .ant-switch,
  .jnpf-color-picker,
  .ant-slider {
    margin: 0 11px;
  }

  .ant-table {
    td {
      background-color: @component-background !important;
    }
    .ant-switch,
    .jnpf-color-picker,
    .ant-slider {
      margin: 0;
    }
    .ant-table-tbody {
      .ant-table-placeholder,
      .ant-table-placeholder .ant-table-cell {
        border-right: none !important;
      }
    }
  }
  .table-add-action {
    border: none;
    margin-top: 0;
    // border-top: 1px solid @border-color-base1;
  }
}
.table-grid-box {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  margin-bottom: 18px;
  & > tbody {
    & > tr {
      & > td {
        border: var(--borderWidth) var(--borderType) var(--borderColor);
        background-color: var(--backgroundColor);
        overflow: hidden;
        height: 50px;
        padding: 18px 18px 0;
      }
    }
  }
}
.export-modal {
  &.ant-modal .ant-modal-body > .scrollbar {
    padding: 12px;
  }
  .ant-form-item {
    margin-bottom: 20px;
  }
  .export-line {
    line-height: 32px;
    border-bottom: 1px solid @border-color-base1;
    margin-bottom: 5px;
    .export-label {
      font-size: 18px;
      font-weight: bold;
      width: 100%;
      span {
        margin-left: 10px;
        font-size: 14px;
        font-weight: normal;
      }
    }
  }
  .ant-checkbox-wrapper {
    line-height: 32px;
  }
  .options-list {
    display: block;
    .ant-checkbox-wrapper {
      width: calc(33.33% - 10px);
      margin-right: 10px;
      .ant-checkbox + span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      & + .ant-checkbox-wrapper {
        margin-left: 0;
      }
    }
  }
  .footer-tip {
    float: left;
    line-height: 32px;
    color: @text-color-secondary;
  }
}
.ant-tabs.common-left-tabs {
  height: 100%;
  flex-shrink: 0;
  margin-right: 10px;
  .ant-tabs-nav-list {
    width: 160px;
    .ant-tabs-tab + .ant-tabs-tab {
      margin-top: 2px;
    }
  }
  .ant-tabs-content-holder {
    width: 0;
  }
}
.drag-handler {
  cursor: move;
  font-size: 20px;
}
.jnpf-common-search-box {
  padding-top: 10px;
  flex-shrink: 0;
  position: relative;
  &.jnpf-common-search-box-modal {
    padding: 10px 10px 0;
  }
  .jnpf-common-search-box-right {
    position: absolute;
    right: 10px;
    top: 15px;
    .jnpf-common-search-box-right-icon {
      margin-left: 10px;
      svg {
        width: 1.3em;
        height: 1.3em;
      }
    }
  }
}
.flow-com-title {
  height: 60px;
  line-height: 60px;
  text-align: center;
  position: relative;
  margin-bottom: 10px;

  h1 {
    font-size: 18px;
    margin: 0;
    font-weight: 700;
  }

  .number {
    position: absolute;
    right: 0;
    bottom: 0;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
  }
}
.ant-modal.jnpf-cron-modal {
  .ant-modal-body {
    padding: 0 10px;
  }
}
.jnpf-import-modal {
  .import-main {
    margin: 20px 0;
    height: 480px;
    position: relative;
    .import-preview-table {
      .child-table-column .child-table__row td {
        flex: unset !important;
      }
    }
    .upload {
      display: flex;
      border: 1px solid @border-color-base;
      margin-bottom: 25px;
      &.error-show {
        margin-top: 10px;
        margin-bottom: 15px;
        .up_left {
          height: 120px;
        }
        .up_right {
          padding-top: 20px;
          font-size: 16px;
        }
      }
      .up_left {
        width: 126px;
        height: 140px;
        background: #f9f9f9;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        img {
          width: 80px;
          height: 80px;
        }
      }
      .up_right {
        margin-left: 30px;
        font-size: 14px;
        padding-top: 30px;
        flex: 1;
        .title {
          font-size: 18px;
          font-weight: bold;
        }
        .tip {
          margin: 15px 0;
          line-height: 16px;
          &.success-tip span {
            color: @primary-color;
          }
          &.error-tip span {
            color: @error-color;
          }
        }
        .ant-btn-link {
          padding: 0 !important;
        }
      }
      .upload-area {
        display: flex;
        padding-right: 200px;
        .ant-upload-select {
          margin-right: 50px;
          flex-shrink: 0;
        }
        .ant-upload-list {
          flex: 1;
        }
        .ant-upload-list-item:first-child {
          margin-top: 5px;
        }
      }
    }
    .success {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 110px;
      .success-title {
        margin: 20px 0;
        font-size: 18px;
        font-weight: bold;
      }
    }
    .conTips {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
      .ant-btn-link {
        padding: 0 !important;
      }
    }
  }
}
.jnpf-sso-modal {
  .scrollbar {
    padding: 0 !important;
    overflow: hidden;
    .scrollbar__view {
      height: 600px !important;
      & > div {
        max-height: 600px !important;
        height: 600px !important;
      }
    }
  }
}
.jnpf-tenant-social-modal {
  .ant-modal-header {
    display: none;
  }
  .scrollbar {
    padding: 0 !important;
  }
  .other-main {
    width: 100%;
    padding-top: 10px;
    height: 500px;
    overflow: hidden;
    background: url('../assets/images/other-login-bg.png') no-repeat center;
    background-size: auto 100%;
    .other-title {
      display: flex;
      height: 50px;
      line-height: 50px;
      justify-content: center;
      align-items: center;
      .other-icon {
        width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 50%;
        border: 2px solid @primary-color;
        i {
          font-size: 16px;
          color: @primary-color;
        }
      }
      .other-text {
        height: 24px;
        line-height: 24px;
        font-size: 18px;
        font-weight: bold;
        margin: 0 5px;
      }
    }
    .other-body {
      padding: 20px;
      width: 100%;
      height: 440px;
      overflow-x: hidden;
      overflow-y: auto;
      .other-login-card {
        border-radius: 5px;
        border-left: 5px solid #9dc8fa;
        font-size: 12px;
        margin-bottom: 20px;
        background-color: @component-background;
        cursor: pointer;
        &:hover {
          border-color: @primary-color;
          i {
            color: #fff;
          }
        }
      }
      .other-login-des {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-top: 12px;
        font-size: 14px;
        line-height: 20px;
        &.other-login-title {
          margin-top: 0;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 20px;
        }
      }
    }
  }
}
.jnpf-release-modal {
  .ant-modal-body > .scrollbar {
    padding: 12px 55px !important;
  }
  .release-main {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    .item {
      position: relative;
      width: 215px;
      height: 127px;
      cursor: pointer;
      border: 1px solid @border-color-base;
      border-radius: 6px;
      text-align: center;
      padding-top: 20px;
      color: #606266;
      &.active {
        border-color: @primary-color;
        color: @primary-color;
        box-shadow: 0 0 6px rgba(6, 58, 108, 0.1);
        .item-icon {
          border-color: @primary-color;
        }
        .icon-checked {
          display: block;
        }
      }
      .item-icon {
        display: inline-block;
        width: 44px;
        height: 44px;
        margin-bottom: 16px;
        border: 2px solid #606266;
        line-height: 40px;
        font-size: 24px;
        text-align: center;
        border-radius: 50%;
      }
      .item-title {
        font-size: 16px;
        font-weight: 400;
      }
      .icon-checked {
        display: none;
        width: 18px;
        height: 18px;
        border: 18px solid @primary-color;
        border-left: 18px solid transparent;
        border-top: 18px solid transparent;
        border-bottom-right-radius: 4px;
        position: absolute;
        right: 0px;
        bottom: 0px;

        .anticon {
          position: absolute;
          top: 1px;
          left: 0;
          font-size: 14px;
          color: #fff;
        }
      }
    }
  }
  .release-form-main {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    .ant-form-item {
      width: 215px;
    }
  }
}
.link-text {
  color: @primary-color;
  cursor: pointer;
}
.jnpf-basic-table {
  .link-text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.child-table-box {
  padding: 0 !important;
  vertical-align: top !important;
}
.child-table-column {
  .child-table__row {
    background: transparent;
    border-bottom: 1px solid @border-color-base1;
    display: flex;
    align-items: center;
    min-height: 39px;

    td {
      border: none !important;
      flex-shrink: 0;
      flex: 1;
      &.td-flex-1 {
        flex: 1;
      }
      .cell {
        min-height: 23px !important;
        padding: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    &:last-child {
      border-bottom: none !important;
    }
  }
  .expand-more-btn {
    height: 39px;
    text-align: center;
    padding-top: 4px;
  }
}
.jnpf-content-table-tabs {
  &.ant-tabs-card {
    & > .ant-tabs-nav {
      margin-bottom: 0;
      .ant-tabs-tab {
        &:first-child {
          border-left: none;
        }
      }
    }
  }
}
.jnpf-super-query-modal {
  .super-query-main {
    .query-noData {
      text-align: center;
      padding: 20px 0;
      .noData-img {
        width: 160px;
        margin-bottom: 10px;
        display: inline-block;
      }
      .noData-txt {
        color: @text-color-secondary;
      }
    }
  }
}
.jnpf-condition-modal {
  .ant-modal-body > .scrollbar {
    padding: 20px !important;
  }
}
.condition-main {
  .condition-list {
    margin-bottom: 10px;
  }
  .icon-ym-btn-clearn {
    cursor: pointer;
    font-size: 18px;
    line-height: 32px;
    color: @error-color;
  }
  .ant-select,
  .ant-picker {
    width: 100%;
  }
  .jnpf-color-picker {
    display: block !important;
  }
}
.plan-popover {
  .ant-popover-inner-content {
    padding: 0;
    width: 240px;
  }
  .plan-list {
    padding: 6px 0;
    max-height: 182px;
    overflow: auto;
    &-item {
      height: 34px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: @text-color-label;
      font-size: 14px;
      cursor: pointer;
      padding: 0 20px;
      .icon-ym-nav-close {
        font-size: 12px;
      }
      &:hover {
        background-color: @tree-hover-font-color;
      }
    }
    .plan-list-name {
      width: 160px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: @primary-color;
      cursor: pointer;
    }
    .icon-ym-nav-close:hover {
      color: @error-color;
    }
  }
  .noData-txt {
    height: 34px;
    color: @text-color-secondary;
    font-size: 14px;
    line-height: 34px;
    text-align: center;
  }
}
.edit-row-action {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 24px;
  &:hover {
    .edit-row-index {
      display: none;
    }
    .ym-custom {
      display: block;
    }
  }
  .ym-custom {
    display: none;
    cursor: pointer;
  }
}
.short-link-wrapper {
  padding-bottom: 20px;
  &.short-link-wrapper-list {
    padding-bottom: 0;
  }
  .short-link-main {
    position: relative;
    width: 80%;
    height: 100%;
    margin: 0 auto;

    display: flex;
    flex-direction: column;
  }
  .short-link-header {
    height: 60px;
    line-height: 60px;
    text-align: center;
    background-color: @primary-color;
    color: #fff;
    font-size: 16px;
    flex-shrink: 0;
  }
  .icon-qrcode {
    cursor: pointer;
    position: absolute;
    right: -50px;
    top: 5px;
    width: 50px;
    height: 50px;
    font-size: 50px;
    line-height: 50px;
    color: @text-color-base;
  }
  .short-link-content {
    flex: 1;
    overflow: hidden;
    &.short-link-form {
      padding: 10px;
      overflow: auto;
      background-color: @component-background;
    }
  }
  .short-link-footer {
    flex-shrink: 0;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 10px;
    background-color: @component-background;
  }
  .short-link-lock-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  .short-link-lock-form {
    .ant-input-affix-wrapper {
      width: 220px;
    }
  }
}
.add-rules-modal {
  .ant-modal-body {
    & > .scrollbar {
      padding: 0 20px 20px !important;
    }
  }
}
.parameter-box {
  display: flex;
  .icon-ym-btn-edit {
    color: @primary-color;
    cursor: pointer;
    font-size: 16px;
  }
  .icon-ym-delete {
    color: @error-color;
    cursor: pointer;
    font-size: 16px;
  }
  .left-pane {
    width: 350px;
    height: 420px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin: 0 10px 18px 20px;
    .left-pane-list {
      border: 1px solid @border-color-base;
      border-radius: 4px;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .list {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .header {
          background-color: @app-content-background;
          display: flex;
          &.detail-header {
            span {
              width: 170px;
            }
          }
          span {
            font-size: 14px;
            color: @text-color-label;
            padding: 8px;
            display: inline-block;
            line-height: 23px;
            width: 134px;
          }
          .operation {
            flex: 1;
          }
        }
        .search-box {
          border-bottom: 1px solid @border-color-base1;
          .search {
            margin: 10px;
            width: calc(100% - 20px);
          }
        }
      }
    }
    .table-add-action {
      margin-top: 0;
      border-width: 1px 0 0;
    }
  }
  .right-pane {
    flex: 1;
    display: flex;
    flex-direction: column;
    .msg-pane {
      margin: 0 0 18px 10px;
      flex: 1;
      display: flex;
      flex-direction: column;
      .list {
        flex: 1;
      }
    }
    .tox-tinymce {
      height: 370px !important;
    }
  }
}
.result-modal {
  .ant-modal-body > .scrollbar {
    padding: 0 !important;
  }
}
.text-primary {
  color: #188ae2 !important;
}
.text-success {
  color: #0eac5c !important;
}
.text-info {
  color: #35b8e0 !important;
}
.text-warning {
  color: #f9c851 !important;
}
.text-danger {
  color: #ff5b5b !important;
}
.text-pink {
  color: #ff8acc !important;
}
.text-purple {
  color: #5b69bc !important;
}
.text-inverse {
  color: #3b3e47 !important;
}
.text-dark {
  color: #282828 !important;
}
.text-white {
  color: #ffffff !important;
}
.text-color {
  color: #6a6c6f !important;
}
.text-grey {
  color: #999 !important;
}
.i-default {
  color: #6b7a99 !important;
}
.title-color {
  color: #475059 !important;
}
.ant-form-item-control {
  .jnpf-color-picker {
    display: block !important;
  }
}
.ant-modal.jnpf-modal-portal {
  .ant-modal-body {
    height: 60vh;
    > .scrollbar {
      padding: 0 !important;
    }
  }
}
.socials-list-justAuth {
  padding: 0 40px;
  .socials-item {
    padding: 10px 0;
    border-bottom: 1px solid @border-color-base;
  }
  .socials-item-main {
    display: flex;
    align-items: center;
    padding: 10px;
    height: 100px;
    &:hover {
      background-color: @selected-hover-bg;
    }
    .item-img {
      width: 80px;
      height: 80px;
      display: block;
      margin-right: 14px;
      flex-shrink: 0;
    }
    .item-txt {
      height: 80px;
      flex: 1;
      .item-name {
        line-height: 22px;
        font-size: 16px;
        margin-bottom: 16px;
        font-weight: 600;
      }
      .item-desc {
        color: @text-color-label;
        font-size: 12px;
        line-height: 18px;
      }
    }
    .item-btn {
      width: 70px;
      text-align: right;
      flex-shrink: 0;
    }
  }
}
.jnpf-log-detail-modal {
  .scrollbar {
    padding: 0 !important;
  }
}

.fa-mt-12 {
  margin-top: 12px;
}

.fa-mb-12 {
  margin-bottom: 12px;
}

body {
  /* 使用 tailwind-config/tailwind.config.js 中的theme配置 */
  --primary-color: theme('colors.blue');
  --primary-dark: theme('colors.blueDark');
  --fa-bg-color: #fff; /* 适配主题-背景色 */
  --fa-bg-color-hover: #eee; /* 适配主题-背景色 */
  --fa-bg-color2: #eee; /* 适配主题-背景色-2级 */
  --fa-bg-color-highlight: #1c5e88; /* 适配主题-背景色-高亮 */
  --fa-bg-color-highlight-hover: #1081c9; /* 适配主题-背景色-高亮-hover */
  --fa-text-color: #353535; /* 适配主题-文字颜色 */
  --fa-text-color2: #353535; /* 适配主题-文字颜色-2级 */
  --fa-text-color-hover: #353535; /* 适配主题-文字颜色 */
  --fa-text-color-highlight: #FFFFFF; /* 适配主题-文字颜色-高亮 */
  --fa-subtitle-color: #666; /* 适配主题-文字颜色-副标题 */
  --fa-border-color: #ccc; /* 适配主题-边框颜色 */
  --fa-border-dark-color: #66666666; /* 适配主题-边框颜色 */
  --fa-bg-grey: #f1f1f1; /* 适配主题-背景色-灰色 */
  --separator-border: #eee; /* 适配主题-allocate-分割面板的分割线颜色 */

  --fa-bg-grey-darker: #e1e1e1;
  --fa-hover-bg: #e1e1e1;
  --fa-transition: all ease-in-out 0.3s;
  --fa-border-radius: 3px;
  --fa-box-shadow: 0 2px 4px 4px #ddd;
  --fa-box-shadow-b: 0px -2px 2px 2px #666;
  --focus-border: var(--primary-color) !important;
}

//.ant-modal-content {
//  padding: 12px !important;
//}
//
//.ant-card-body {
//  padding: 12px !important;
//}
//
//.ant-modal-close {
//  top: 12px !important;
//  right: 12px !important;
//}
//
//.ant-form-item .ant-picker, .ant-input-number-in-form-item {
//  width: 100%;
//}
//
//.ant-input-number-group-wrapper {
//  width: 100%;
//}
//
//.ant-drawer-close {
//  padding: 0;
//}
//
//.ant-drawer-title {
//  font-weight: 600;
//}
//
//.ant-drawer-header {
//  padding: 11px 12px !important;
//}
//
//.ant-drawer-body {
//  padding: 12px !important;
//  position: relative;
//}
//
//.ant-pagination {
//  margin: 8px !important;
//}
//
//.ant-descriptions-item-label, .ant-descriptions-item-content {
//  padding: 6px !important;
//}
//
//.ant-popconfirm-buttons {
//  display: flex;
//}

/*----------------------------------------- 全局通用自定义样式 -----------------------------------------*/
.ant-input-disabled {
  color: #444 !important;
}
.ant-picker-disabled input {
  color: #444 !important;
}

.ant-select-disabled .ant-select-selection-item {
  color: #444 !important;
}

/*----------------------------------------- 全局通用自定义样式 -----------------------------------------*/
.fa-full-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.fa-full-content-p12 {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  bottom: 12px;
}

.fa-flex-1 {
  flex: 1;
  position: relative;
}

.fa-flex-2 {
  flex: 2;
  position: relative;
}

.fa-relative {
  position: relative;
}

.fa-absolute {
  position: absolute;
}

.fa-top {
  top: 0;
}

.fa-left {
  left: 0;
}

.fa-right {
  right: 0;
}

.fa-bottom {
  bottom: 0;
}

.fa-bg-primary {
  background-color: var(--primary-color);
}

.fa-bg-white {
  background-color: var(--fa-bg-color);
}

.fa-bg-blue {
  background-color: #d6e4ff;
}

.fa-bg-grey {
  background-color: var(--fa-bg-grey);
}

.fa-bg-grey-darker {
  background-color: var(--fa-bg-grey-darker);
}

.fa-bg-dark {
  background-color: var(--fa-text-color);
}

.fa-card {
  padding: 12px;
  background-color: var(--fa-bg-color);
  border-radius: var(--fa-border-radius);
  border: 1px solid var(--fa-border-color);
}

.fa-text {
  color: var(--fa-text-color);
}

.fa-text-center {
  text-align: center;
}

.fa-user-fill {
  font-weight: bold;
}

.fa-text:hover {
  color: var(--fa-text-color-hover);
}

.fa-h1 {
  color: var(--fa-text-color);
  font-size: 24px;
  font-weight: 600;
}

.fa-h2 {
  color: var(--fa-text-color);
  font-size: 20px;
  font-weight: 600;
}

.fa-h3 {
  color: var(--fa-text-color);
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
}

.fa-h4 {
  color: var(--fa-text-color);
  font-weight: 500;
  font-size: 14px;
  line-height: 1.5;
}

.fa-normal {
  color: var(--fa-text-color);
  font-weight: 400;
  font-size: 14px;
  line-height: 1.2;
}

.fa-p24 {
  padding: 24px;
}

.fa-p8 {
  padding: 8px;
}

.fa-p12 {
  padding: 12px;
}

.fa-pt12 {
  padding-top: 12px;
}

.fa-pb12 {
  padding-bottom: 12px;
}

.fa-pl12 {
  padding-left: 12px;
}

.fa-pr12 {
  padding-right: 12px;
}

.fa-pr4 {
  padding-right: 4px;
}

.fa-p4 {
  padding: 4px;
}

.fa-p6 {
  padding: 6px;
}

.fa-p8 {
  padding: 8px;
}

.fa-m4 {
  margin: 4px;
}

.fa-mt4 {
  margin-top: 4px;
}

.fa-mt6 {
  margin-top: 6px;
}

.fa-mt8 {
  margin-top: 8px;
}

.fa-mt12 {
  margin-top: 12px;
}

.fa-mtb12 {
  margin-top: 12px;
  margin-bottom: 12px;
}

.fa-mt24 {
  margin-top: 24px;
}

.fa-mb4 {
  margin-bottom: 4px;
}

.fa-mb6 {
  margin-bottom: 6px;
}

.fa-mb8 {
  margin-bottom: 8px;
}

.fa-mb12 {
  margin-bottom: 12px;
}

.fa-mb24 {
  margin-bottom: 24px;
}

.fa-m12 {
  margin: 12px;
}

.fa-ml12 {
  margin-left: 12px;
}

.fa-ml8 {
  margin-left: 8px;
}

.fa-pl12 {
  padding-left: 12px;
}

.fa-pr12 {
  padding-right: 12px;
}

.fa-mr4 {
  margin-right: 4px;
}

.fa-ml4 {
  margin-left: 4px;
}

.fa-mr8 {
  margin-right: 8px;
}

.fa-mr12 {
  margin-right: 12px;
}

.fa-le-sp2 {
  letter-spacing: 2px;
}

.fa-le-sp3 {
  letter-spacing: 3px;
}

.fa-full-w {
  width: 100%;
}

.fa-full-h {
  height: 100%;
}

.fa-full {
  width: 100%;
  height: 100%;
}

.fa-scroll-hidden {
  overflow: hidden;
}

.fa-scroll {
  overflow: auto;
}

.fa-scroll-auto-y {
  overflow-y: auto;
  overflow-x: hidden;
}

.fa-scroll-auto-x {
  overflow-y: hidden;
  overflow-x: auto;
}

.fa-flex-row {
  display: flex;
  flex-direction: row;
}

.fa-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.fa-flex-row-center {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.fa-flex-row-end {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.fa-flex-column {
  display: flex;
  flex-direction: column;
}

.fa-flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.fa-flex-wrap {
  flex-wrap: wrap;
}

.fa-main {
  padding: 8px;
  margin: 0;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  height: 100%;
}

.fa-border {
  border: 1px solid var(--fa-border-color);
  border-radius: var(--fa-border-radius);
}

.fa-border-r {
  border-right: 1px solid var(--fa-border-color);
}

.fa-border-l {
  border-left: 1px solid var(--fa-border-color);
}

.fa-border-t {
  border-top: 1px solid var(--fa-border-color);
}

.fa-border-b {
  border-bottom: 1px solid var(--fa-border-color);
}

.fa-border-dark-b {
  border-bottom: 1px solid var(--fa-border-dark-color);
}

.fa-form-underline > input {
  border: none !important;
  border-radius: 0 !important;
  border-bottom: 1px solid var(--fa-border-color) !important;
}

.fa-input-underline {
  border: none !important;
  border-radius: 0 !important;
}

.fa-input-underline:focus {
  border-bottom: 1px solid var(--fa-border-color) !important;
}

.fa-table-subtitle {
  line-height: 30px;
  color: var(--fa-subtitle-color);
  margin-left: 6px;
  height: 25px;
  display: flex;
}

.fa-subtitle {
  color: var(--fa-subtitle-color);
}

.fa-link-btn {
  border-radius: var(--fa-border-radius);
  /*padding: 2px 4px;*/
  cursor: pointer;
  transition: all ease-in-out 0.3s;
}

.fa-link-btn:hover {
  background-color: var(--fa-bg-color-hover);
}

.fa-normal-btn {
  border-radius: var(--fa-border-radius);
  cursor: pointer;
  transition: all ease-in-out 0.3s;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
}

.fa-normal-btn:hover {
  background-color: var(--fa-bg-color-hover);
}

.fa-cursor-pointer {
  cursor: pointer;
}

.fa-hover {
  cursor: pointer;
  transition: var(--fa-transition);
}

.fa-hover:hover {
  box-shadow: var(--fa-box-shadow);
}

.fa-disabled {
  cursor: not-allowed;
}

.fa-link-grey {
  cursor: pointer;
  transition: var(--fa-transition);
}

.fa-link-grey:hover {
  background-color: var(--fa-bg-color-hover);
}

.fa-box-shadow-b {
  box-shadow: var(--fa-box-shadow-b);
}

.fa-rich-div img {
  max-width: 800px;
}

.fa-rich-div-sm img {
  max-width: 400px;
}

.fa-break-word {
  word-break:break-all; /* 英文 */
  white-space:pre-wrap; /* 中文 */
}

.fa-keep-word {
  word-break: keep-all; /* 英文 */
  white-space: nowrap; /* 中文 */
}

.fa-input-underline {
  border-width: 0;
  border-bottom-width: 1px;
  border-radius: 0;
}

.fa-input-amap-search {
  line-height: 24px;
  width: 200px;
  border: 1px solid var(--fa-border-color);
}

.fa-tips {
  color: #666;
}

.fa-upload-img-local .ant-upload-select {
  margin: 0 !important;
}

.fa-pos-abs {
  position: absolute;
}

.fa-pos-rel {
  position: relative;
}

.fa-pos-t {
  top: 0;
}

.fa-pos-b {
  bottom: 0;
}

.fa-pos-l {
  left: 0;
}

.fa-pos-r {
  right: 0;
}

.fa-cover {
  background-color: rgba(238, 238, 238, 0.4);
}

.fa-table-title {
  font-size: 18px;
  font-weight: 600;;
}

.fa-table {
  border: 1px solid #333;
  border-spacing: 0;
  border-collapse: collapse;
}

.fa-table tr, .fa-table td {
  border: 1px solid #333;
}

.fa-table-tr-p4 td {
  padding: 4px;
}

.fa-table-tr-p12 td {
  padding: 12px;
}

.fa-col13 {
  display: grid;
  grid-template-columns: 1fr 3fr
}

.fa-col-right {
  display: flex;
  flex-direction: row;
  padding: 0 10px;
  justify-content: space-between; /* 元素之间的空间平均分布 */
}

.fa-table-title {
  font-size: 18px;
  font-weight: 600;;
}

.fa-table-title-plus {
  font-size: 38px;
  font-weight: 400;
  font-family: 宋体;
}

.fa-img-center {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 40px;
}

.fa-img-center img {
  height: 100px
}

.fa-checkbox-col {
  display: flex;
  align-items: center;
  flex-direction: row;
}

.fa-checkbox {
  position: relative;
  width: 15px; /* 勾选框的宽度 */
  height: 15px; /* 勾选框的高度 */
  border: 1px solid #000;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}

.fa-upload-file-list-img {
  .ant-image-img {
    height: 100% !important;
    object-fit: cover;
  }
}

.fa-file-doc-countdown {
  padding: 24px;
  background-color: #FFF;
  color: #333;
  z-index: 19999;
}

/*----------------------------------------- fa-corn -----------------------------------------*/
.fa-cron-react-editor-tab {
  background-color: var(--fa-bg-grey) !important;
}

.fa-cron-react-editor-tab-item {
  background-color: var(--fa-bg-color) !important;
  color: var(--fa-text-color);
}

.fa-cron-react-editor-tab-item-active {
  background-color: var(--fa-bg-color-highlight) !important;
  color: var(--fa-text-color);
}

/* ------------------- 自定义样式-证件 ------------------- */
.fa-zz-card-line {
  display: flex;
  flex-direction: row;
  padding-bottom: 8px;
}

.fa-zz-card-line-label {
  width: 60px;
}

.fa-zz-card-top-banner {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 50px;
  //background-size: 100% 100% !important;
  //background: url('/plugins/zz/certificate/certificate_card_banner.png');
}

.fa-zz-card-top-banner-logo {
  height: 46px;
  width: 46px;
  background-size: 100% 100% !important;
  background: url('/plugins/zz/certificate/zz_card_logo.jpg');
}

.fa-zz-card-border-img {
  height: 10px;
  background-size: 100% 100% !important;
  background: url('/plugins/zz/certificate/card_border.png');
}

.fa-zz-card-border-bottom {
  border-bottom: 10px solid;
  border-image: linear-gradient(to right, #024577, #0088E3);
  border-image-slice: 1;
}

.fa-zz-card-bottom-text {
  display: flex;
  flex-direction: row;
}

.fa-zz-card-bottom-text div {
  flex: 1;
  font-size: 22px;
  color: #0E3092;
}

.fa-zz-card-stamp {
  width: 100px;
  height: 100px;
  position: absolute;
  left: 71px;
  bottom: 28px;
  transform:rotate(-19deg)
}

.fa-zz-card-right {
  font-size: 16px;
  color: #333;
}

.fa-zz-card-qrcode {
  width: 140px;
  height: 140px;
  border: 1px solid #333;
}

.fa-select-modal {
  .scrollbar {
    padding: 0 !important;
  }
}

.fa-drag-handler {
  cursor: move;
}

.page {
  width: 21cm;
  min-height: 29.7cm;
  padding: 0.5cm;
  margin: 1cm auto;
  border: 1px #D3D3D3 solid;
  border-radius: 5px;
  //background: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.subpage {
  /*padding: 1cm;*/
  /*border: 5px red solid;*/
  height: 256mm;
  background: white;
  /*outline: 2cm #FFEAEA solid;*/
}

//.p1 {
//  font-size: 20px;
//}

@page {
  size: A4;
  margin: 0;
}

@media print {
  .page {
    margin: 0;
    border: initial;
    border-radius: initial;
    width: initial;
    min-height: initial;
    box-shadow: initial;
    background: initial;
    page-break-after: always;
  }
}
