-- 人员信息更新登记表
CREATE TABLE `user_info_update_registry` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `identification_number` varchar(18) NOT NULL COMMENT '身份证号',
  `original_user_id` varchar(50) DEFAULT NULL COMMENT '原始用户ID',
  
  -- 表单数据字段（复制自原表单）
  `real_name` varchar(50) DEFAULT NULL COMMENT '姓名',
  `age` int DEFAULT NULL COMMENT '年龄',
  `gender` tinyint DEFAULT NULL COMMENT '性别 1-男 2-女',
  `mobile_phone` varchar(20) DEFAULT NULL COMMENT '联系方式',
  `emergency_contacts` varchar(50) DEFAULT NULL COMMENT '紧急联系人',
  `emergency_contacts_phone` varchar(20) DEFAULT NULL COMMENT '紧急联系人电话',
  `native_place` varchar(100) DEFAULT NULL COMMENT '籍贯',
  `nation` varchar(50) DEFAULT NULL COMMENT '民族',
  `home_address` varchar(200) DEFAULT NULL COMMENT '家庭住址',
  `political_outlook` varchar(20) DEFAULT NULL COMMENT '政治面貌',
  `education` varchar(50) DEFAULT NULL COMMENT '学历',
  `specialty` varchar(100) DEFAULT NULL COMMENT '所学专业',
  `graduation_school` varchar(100) DEFAULT NULL COMMENT '毕业学校',
  `graduation_time` datetime DEFAULT NULL COMMENT '毕业时间',
  `join_work_time` datetime DEFAULT NULL COMMENT '首次参加工作时间',
  `seniority` int DEFAULT NULL COMMENT '本工种工作年限',
  `marital_status` varchar(10) DEFAULT NULL COMMENT '婚姻状况',
  `organize_name` varchar(100) DEFAULT NULL COMMENT '部门',
  `position_name` varchar(100) DEFAULT NULL COMMENT '岗位',
  `position_sequence` varchar(50) DEFAULT NULL COMMENT '岗位序列',
  `category_personnel` varchar(50) DEFAULT NULL COMMENT '人员类别',
  `form_employment` varchar(50) DEFAULT NULL COMMENT '用工方式',
  `source_unit` varchar(100) DEFAULT NULL COMMENT '来源单位',
  `go_project_time` datetime DEFAULT NULL COMMENT '进项目时间',
  `remark` text COMMENT '备注',
  
  -- 审核相关字段
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态 0-待审核 1-已审核通过 2-已拒绝 3-已应用',
  `reviewer_id` varchar(50) DEFAULT NULL COMMENT '审核人ID',
  `reviewer_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_comment` text COMMENT '审核意见',
  
  -- 提交信息字段
  `submit_ip` varchar(50) DEFAULT NULL COMMENT '提交IP地址',
  `submit_user_agent` text COMMENT '提交浏览器信息',
  `submit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  
  -- 数据对比字段
  `original_data` longtext COMMENT '原始数据JSON',
  `changed_fields` text COMMENT '变更字段列表',
  `change_summary` text COMMENT '变更摘要',
  
  -- 系统字段
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_user_id` varchar(50) DEFAULT NULL COMMENT '创建人ID',
  `last_modify_user_id` varchar(50) DEFAULT NULL COMMENT '最后修改人ID',
  `delete_mark` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记 0-正常 1-删除',
  
  PRIMARY KEY (`id`),
  KEY `idx_identification_number` (`identification_number`),
  KEY `idx_status` (`status`),
  KEY `idx_submit_time` (`submit_time`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_original_user_id` (`original_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人员信息更新登记表';

-- 创建状态枚举说明
-- status 字段说明：
-- 0: 待审核 - 用户刚提交，等待管理员审核
-- 1: 已审核通过 - 管理员审核通过，可以应用到主表
-- 2: 已拒绝 - 管理员拒绝了此次更新申请
-- 3: 已应用 - 数据已经同步到主表，流程完成

-- 添加表注释
ALTER TABLE `user_info_update_registry` COMMENT = '人员信息更新登记表 - 用于无登录环境下收集人员信息更新申请';
